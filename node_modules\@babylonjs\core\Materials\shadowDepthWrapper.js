import { Effect } from "./effect.js";
import { RandomGUID } from "../Misc/guid.js";
import { DrawWrapper } from "./drawWrapper.js";
import { EngineStore } from "../Engines/engineStore.js";
class MapMap {
    constructor() {
        this.mm = new Map();
    }
    get(a, b) {
        const m = this.mm.get(a);
        if (m !== undefined) {
            return m.get(b);
        }
        return undefined;
    }
    set(a, b, v) {
        let m = this.mm.get(a);
        if (m === undefined) {
            this.mm.set(a, (m = new Map()));
        }
        m.set(b, v);
    }
}
/**
 * Class that can be used to wrap a base material to generate accurate shadows when using custom vertex/fragment code in the base material
 */
export class ShadowDepthWrapper {
    /** Gets the standalone status of the wrapper */
    get standalone() {
        var _a, _b;
        return (_b = (_a = this._options) === null || _a === void 0 ? void 0 : _a.standalone) !== null && _b !== void 0 ? _b : false;
    }
    /** Gets the base material the wrapper is built upon */
    get baseMaterial() {
        return this._baseMaterial;
    }
    /** Gets the doNotInjectCode status of the wrapper */
    get doNotInjectCode() {
        var _a, _b;
        return (_b = (_a = this._options) === null || _a === void 0 ? void 0 : _a.doNotInjectCode) !== null && _b !== void 0 ? _b : false;
    }
    /**
     * Instantiate a new shadow depth wrapper.
     * It works by injecting some specific code in the vertex/fragment shaders of the base material and is used by a shadow generator to
     * generate the shadow depth map. For more information, please refer to the documentation:
     * https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows
     * @param baseMaterial Material to wrap
     * @param scene Define the scene the material belongs to
     * @param options Options used to create the wrapper
     */
    constructor(baseMaterial, scene, options) {
        this._baseMaterial = baseMaterial;
        this._scene = scene !== null && scene !== void 0 ? scene : EngineStore.LastCreatedScene;
        this._options = options;
        this._subMeshToEffect = new Map();
        this._subMeshToDepthWrapper = new MapMap();
        this._meshes = new Map();
        // Register for onEffectCreated to store the effect of the base material when it is (re)generated. This effect will be used
        // to create the depth effect later on
        this._onEffectCreatedObserver = this._baseMaterial.onEffectCreatedObservable.add((params) => {
            var _a;
            const mesh = (_a = params.subMesh) === null || _a === void 0 ? void 0 : _a.getMesh();
            if (mesh && !this._meshes.has(mesh)) {
                // Register for mesh onDispose to clean up our internal maps when a mesh is disposed
                this._meshes.set(mesh, mesh.onDisposeObservable.add((mesh) => {
                    const iterator = this._subMeshToEffect.keys();
                    for (let key = iterator.next(); key.done !== true; key = iterator.next()) {
                        const subMesh = key.value;
                        if ((subMesh === null || subMesh === void 0 ? void 0 : subMesh.getMesh()) === mesh) {
                            this._subMeshToEffect.delete(subMesh);
                            this._subMeshToDepthWrapper.mm.delete(subMesh);
                        }
                    }
                }));
            }
            this._subMeshToEffect.set(params.subMesh, [params.effect, this._scene.getEngine().currentRenderPassId]);
            this._subMeshToDepthWrapper.mm.delete(params.subMesh); // trigger a depth effect recreation
        });
    }
    /**
     * Gets the effect to use to generate the depth map
     * @param subMesh subMesh to get the effect for
     * @param shadowGenerator shadow generator to get the effect for
     * @param passIdForDrawWrapper Id of the pass for which the effect from the draw wrapper must be retrieved from
     * @returns the effect to use to generate the depth map for the subMesh + shadow generator specified
     */
    getEffect(subMesh, shadowGenerator, passIdForDrawWrapper) {
        var _a;
        const entry = (_a = this._subMeshToDepthWrapper.mm.get(subMesh)) === null || _a === void 0 ? void 0 : _a.get(shadowGenerator);
        if (!entry) {
            return null;
        }
        let drawWrapper = entry.drawWrapper[passIdForDrawWrapper];
        if (!drawWrapper) {
            drawWrapper = entry.drawWrapper[passIdForDrawWrapper] = new DrawWrapper(this._scene.getEngine());
            drawWrapper.setEffect(entry.mainDrawWrapper.effect, entry.mainDrawWrapper.defines);
        }
        return drawWrapper;
    }
    /**
     * Specifies that the submesh is ready to be used for depth rendering
     * @param subMesh submesh to check
     * @param defines the list of defines to take into account when checking the effect
     * @param shadowGenerator combined with subMesh, it defines the effect to check
     * @param useInstances specifies that instances should be used
     * @param passIdForDrawWrapper Id of the pass for which the draw wrapper should be created
     * @returns a boolean indicating that the submesh is ready or not
     */
    isReadyForSubMesh(subMesh, defines, shadowGenerator, useInstances, passIdForDrawWrapper) {
        var _a, _b;
        if (this.standalone) {
            // will ensure the effect is (re)created for the base material
            if (!this._baseMaterial.isReadyForSubMesh(subMesh.getMesh(), subMesh, useInstances)) {
                return false;
            }
        }
        return (_b = (_a = this._makeEffect(subMesh, defines, shadowGenerator, passIdForDrawWrapper)) === null || _a === void 0 ? void 0 : _a.isReady()) !== null && _b !== void 0 ? _b : false;
    }
    /**
     * Disposes the resources
     */
    dispose() {
        this._baseMaterial.onEffectCreatedObservable.remove(this._onEffectCreatedObserver);
        this._onEffectCreatedObserver = null;
        const iterator = this._meshes.entries();
        for (let entry = iterator.next(); entry.done !== true; entry = iterator.next()) {
            const [mesh, observer] = entry.value;
            mesh.onDisposeObservable.remove(observer);
        }
    }
    _makeEffect(subMesh, defines, shadowGenerator, passIdForDrawWrapper) {
        var _a, _b, _c;
        const engine = this._scene.getEngine();
        const origEffectAndRenderPassId = this._subMeshToEffect.get(subMesh);
        if (!origEffectAndRenderPassId) {
            return null;
        }
        const [origEffect, origRenderPassId] = origEffectAndRenderPassId;
        let params = this._subMeshToDepthWrapper.get(subMesh, shadowGenerator);
        if (!params) {
            const mainDrawWrapper = new DrawWrapper(engine);
            mainDrawWrapper.defines = (_b = (_a = subMesh._getDrawWrapper(origRenderPassId)) === null || _a === void 0 ? void 0 : _a.defines) !== null && _b !== void 0 ? _b : null;
            params = {
                drawWrapper: [],
                mainDrawWrapper,
                depthDefines: "",
                token: RandomGUID(),
            };
            params.drawWrapper[passIdForDrawWrapper] = mainDrawWrapper;
            this._subMeshToDepthWrapper.set(subMesh, shadowGenerator, params);
        }
        const join = defines.join("\n");
        if (params.mainDrawWrapper.effect) {
            if (join === params.depthDefines) {
                // we already created the depth effect and it is still up to date for this submesh + shadow generator
                return params.mainDrawWrapper.effect;
            }
        }
        params.depthDefines = join;
        const uniforms = origEffect.getUniformNames().slice();
        // the depth effect is either out of date or has not been created yet
        let vertexCode = origEffect.vertexSourceCodeBeforeMigration, fragmentCode = origEffect.fragmentSourceCodeBeforeMigration;
        if (!this.doNotInjectCode) {
            // vertex code
            const vertexNormalBiasCode = this._options && this._options.remappedVariables
                ? `#include<shadowMapVertexNormalBias>(${this._options.remappedVariables.join(",")})`
                : Effect.IncludesShadersStore["shadowMapVertexNormalBias"], vertexMetricCode = this._options && this._options.remappedVariables
                ? `#include<shadowMapVertexMetric>(${this._options.remappedVariables.join(",")})`
                : Effect.IncludesShadersStore["shadowMapVertexMetric"], fragmentSoftTransparentShadow = this._options && this._options.remappedVariables
                ? `#include<shadowMapFragmentSoftTransparentShadow>(${this._options.remappedVariables.join(",")})`
                : Effect.IncludesShadersStore["shadowMapFragmentSoftTransparentShadow"], fragmentBlockCode = Effect.IncludesShadersStore["shadowMapFragment"];
            vertexCode = vertexCode.replace(/void\s+?main/g, Effect.IncludesShadersStore["shadowMapVertexExtraDeclaration"] + "\r\nvoid main");
            vertexCode = vertexCode.replace(/#define SHADOWDEPTH_NORMALBIAS|#define CUSTOM_VERTEX_UPDATE_WORLDPOS/g, vertexNormalBiasCode);
            if (vertexCode.indexOf("#define SHADOWDEPTH_METRIC") !== -1) {
                vertexCode = vertexCode.replace(/#define SHADOWDEPTH_METRIC/g, vertexMetricCode);
            }
            else {
                vertexCode = vertexCode.replace(/}\s*$/g, vertexMetricCode + "\r\n}");
            }
            vertexCode = vertexCode.replace(/#define SHADER_NAME.*?\n|out vec4 glFragColor;\n/g, "");
            // fragment code
            const hasLocationForSoftTransparentShadow = fragmentCode.indexOf("#define SHADOWDEPTH_SOFTTRANSPARENTSHADOW") >= 0 || fragmentCode.indexOf("#define CUSTOM_FRAGMENT_BEFORE_FOG") >= 0;
            const hasLocationForFragment = fragmentCode.indexOf("#define SHADOWDEPTH_FRAGMENT") !== -1;
            let fragmentCodeToInjectAtEnd = "";
            if (!hasLocationForSoftTransparentShadow) {
                fragmentCodeToInjectAtEnd = fragmentSoftTransparentShadow + "\r\n";
            }
            else {
                fragmentCode = fragmentCode.replace(/#define SHADOWDEPTH_SOFTTRANSPARENTSHADOW|#define CUSTOM_FRAGMENT_BEFORE_FOG/g, fragmentSoftTransparentShadow);
            }
            fragmentCode = fragmentCode.replace(/void\s+?main/g, Effect.IncludesShadersStore["shadowMapFragmentExtraDeclaration"] + "\r\nvoid main");
            if (hasLocationForFragment) {
                fragmentCode = fragmentCode.replace(/#define SHADOWDEPTH_FRAGMENT/g, fragmentBlockCode);
            }
            else {
                fragmentCodeToInjectAtEnd += fragmentBlockCode + "\r\n";
            }
            if (fragmentCodeToInjectAtEnd) {
                fragmentCode = fragmentCode.replace(/}\s*$/g, fragmentCodeToInjectAtEnd + "}");
            }
            uniforms.push("biasAndScaleSM", "depthValuesSM", "lightDataSM", "softTransparentShadowSM");
        }
        params.mainDrawWrapper.effect = engine.createEffect({
            vertexSource: vertexCode,
            fragmentSource: fragmentCode,
            vertexToken: params.token,
            fragmentToken: params.token,
        }, {
            attributes: origEffect.getAttributesNames(),
            uniformsNames: uniforms,
            uniformBuffersNames: origEffect.getUniformBuffersNames(),
            samplers: origEffect.getSamplers(),
            defines: join + "\n" + origEffect.defines.replace("#define SHADOWS", "").replace(/#define SHADOW\d/g, ""),
            indexParameters: origEffect.getIndexParameters(),
        }, engine);
        for (let id = 0; id < params.drawWrapper.length; ++id) {
            if (id !== passIdForDrawWrapper) {
                (_c = params.drawWrapper[id]) === null || _c === void 0 ? void 0 : _c.setEffect(params.mainDrawWrapper.effect, params.mainDrawWrapper.defines);
            }
        }
        return params.mainDrawWrapper.effect;
    }
}
//# sourceMappingURL=shadowDepthWrapper.js.map