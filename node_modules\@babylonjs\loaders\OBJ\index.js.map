{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/OBJ/index.ts"], "names": [], "mappings": "AAAA,cAAc,iBAAiB,CAAC;AAChC,cAAc,qBAAqB,CAAC;AACpC,cAAc,eAAe,CAAC;AAC9B,cAAc,iBAAiB,CAAC", "sourcesContent": ["export * from \"./mtlFileLoader\";\r\nexport * from \"./objLoadingOptions\";\r\nexport * from \"./solidParser\";\r\nexport * from \"./objFileLoader\";\r\n"]}