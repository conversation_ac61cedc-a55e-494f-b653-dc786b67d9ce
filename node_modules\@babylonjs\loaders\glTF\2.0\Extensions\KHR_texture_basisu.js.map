{"version": 3, "file": "KHR_texture_basisu.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_texture_basisu.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAMtD,MAAM,IAAI,GAAG,oBAAoB,CAAC;AAElC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,kBAAkB;IAS3B;;OAEG;IACH,YAAY,MAAkB;QAX9B,kCAAkC;QAClB,SAAI,GAAG,IAAI,CAAC;QAWxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,OAAe,EAAE,OAAiB,EAAE,MAA6C;QACtG,OAAO,UAAU,CAAC,kBAAkB,CAAiC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC9H,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5J,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACtG,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,EACP,OAAO,EACP,KAAK,EACL,CAAC,cAAc,EAAE,EAAE;gBACf,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3B,CAAC,EACD,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,qCAAqC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EAC/F,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CACrC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\nimport type { ITexture } from \"../glTFLoaderInterfaces\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { IKHRTextureBasisU } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_texture_basisu\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_texture_basisu/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_texture_basisu implements IGLTFLoaderExtension {\r\n    /** The name of this extension. */\r\n    public readonly name = NAME;\r\n\r\n    /** Defines whether this extension is enabled. */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadTextureAsync(context: string, texture: ITexture, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRTextureBasisU, BaseTexture>(context, texture, this.name, (extensionContext, extension) => {\r\n            const sampler = texture.sampler == undefined ? GLTFLoader.DefaultSampler : ArrayItem.Get(`${context}/sampler`, this._loader.gltf.samplers, texture.sampler);\r\n            const image = ArrayItem.Get(`${extensionContext}/source`, this._loader.gltf.images, extension.source);\r\n            return this._loader._createTextureAsync(\r\n                context,\r\n                sampler,\r\n                image,\r\n                (babylonTexture) => {\r\n                    assign(babylonTexture);\r\n                },\r\n                texture._textureInfo.nonColorData ? { useRGBAIfASTCBC7NotAvailableWhenUASTC: true } : undefined,\r\n                !texture._textureInfo.nonColorData\r\n            );\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_texture_basisu(loader));\r\n"]}