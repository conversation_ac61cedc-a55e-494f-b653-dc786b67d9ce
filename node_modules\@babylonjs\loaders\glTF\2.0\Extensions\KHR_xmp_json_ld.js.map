{"version": 3, "file": "KHR_xmp_json_ld.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_xmp_json_ld.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,iBAAiB,CAAC;AAE/B;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,eAAe;IAkBxB;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,SAAS;;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,IAAI,EAAE;YACvC,OAAO;SACV;QAED,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,0CAAE,eAAqC,CAAC;QACrF,MAAM,QAAQ,GAAG,MAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,0CAAE,UAAU,0CAAE,eAAqC,CAAC;QAC5F,IAAI,QAAQ,IAAI,QAAQ,EAAE;YACtB,MAAM,MAAM,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAChC,IAAI,QAAQ,CAAC,OAAO,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,IAAI,EAAE,CAAC;gBACpF,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACxE;SACJ;IACL,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRXmpJsonLd_Gltf, IKHRXmpJsonLd_Node } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_xmp_json_ld\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_xmp_json_ld/README.md)\r\n * @since 5.0.0\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_xmp_json_ld implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 100;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * Called after the loader state changes to LOADING.\r\n     */\r\n    public onLoading(): void {\r\n        if (this._loader.rootBabylonMesh === null) {\r\n            return;\r\n        }\r\n\r\n        const xmp_gltf = this._loader.gltf.extensions?.KHR_xmp_json_ld as IKHRXmpJsonLd_Gltf;\r\n        const xmp_node = this._loader.gltf.asset?.extensions?.KHR_xmp_json_ld as IKHRXmpJsonLd_Node;\r\n        if (xmp_gltf && xmp_node) {\r\n            const packet = +xmp_node.packet;\r\n            if (xmp_gltf.packets && packet < xmp_gltf.packets.length) {\r\n                this._loader.rootBabylonMesh.metadata = this._loader.rootBabylonMesh.metadata || {};\r\n                this._loader.rootBabylonMesh.metadata.xmp = xmp_gltf.packets[packet];\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_xmp_json_ld(loader));\r\n"]}