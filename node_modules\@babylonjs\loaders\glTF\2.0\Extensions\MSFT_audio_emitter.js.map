{"version": 3, "file": "MSFT_audio_emitter.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/MSFT_audio_emitter.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,6CAA+B;AACjD,OAAO,EAAE,KAAK,EAAE,sCAAwB;AAExC,OAAO,EAAE,cAAc,EAAE,qDAAuC;AAEhE,OAAO,EAAE,KAAK,EAAE,uCAAyB;AACzC,OAAO,EAAE,aAAa,EAAE,+CAAiC;AAIzD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAItD,MAAM,IAAI,GAAG,oBAAoB,CAAC;AAyBlC;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,kBAAkB;IAe3B;;OAEG;IACH,YAAY,MAAkB;QAjB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAexB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,MAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,SAAiB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,gBAAgB;IACT,SAAS;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAsB,CAAC;YAE7D,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC;YAC9B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;YAEpC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACpC;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAe,EAAE,KAAa;QAChD,OAAO,UAAU,CAAC,kBAAkB,CAAsC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACjI,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAE3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAE3D,KAAK,MAAM,YAAY,IAAI,SAAS,CAAC,QAAQ,EAAE;gBAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBAC5F,IACI,OAAO,CAAC,WAAW,IAAI,SAAS;oBAChC,OAAO,CAAC,WAAW,IAAI,SAAS;oBAChC,OAAO,CAAC,aAAa,IAAI,SAAS;oBAClC,OAAO,CAAC,aAAa,IAAI,SAAS;oBAClC,OAAO,CAAC,UAAU,IAAI,SAAS;oBAC/B,OAAO,CAAC,UAAU,IAAI,SAAS,EACjC;oBACE,MAAM,IAAI,KAAK,CAAC,GAAG,gBAAgB,oFAAoF,CAAC,CAAC;iBAC5H;gBAED,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,gBAAgB,aAAa,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;aACnG;YAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe,EAAE,IAAW,EAAE,MAAqD;QACpG,OAAO,UAAU,CAAC,kBAAkB,CAAqD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC/I,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAE3C,OAAO,IAAI,CAAC,OAAO;iBACd,aAAa,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;gBACnD,KAAK,MAAM,YAAY,IAAI,SAAS,CAAC,QAAQ,EAAE;oBAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;oBAC5F,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,iBAAiB,CAAC,GAAG,gBAAgB,aAAa,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;wBACvF,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,cAAc,EAAE;4BACxC,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;4BAChC,IAAI,OAAO,CAAC,UAAU,IAAI,SAAS,IAAI,OAAO,CAAC,UAAU,IAAI,SAAS,EAAE;gCACpE,KAAK,CAAC,uBAAuB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gCACjD,KAAK,CAAC,kBAAkB,CACpB,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EACnF,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EACnF,CAAC,CACJ,CAAC;6BACL;yBACJ;oBACL,CAAC,CAAC,CACL,CAAC;iBACL;gBAED,MAAM,CAAC,WAAW,CAAC,CAAC;YACxB,CAAC,CAAC;iBACD,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBAClB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACnC,OAAO,WAAW,CAAC;gBACvB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAAe,EAAE,SAAqB;QAC5D,OAAO,UAAU,CAAC,kBAAkB,CAAyC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACxI,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,EAAE;gBACtF,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;gBAE3C,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACnC,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,gBAAgB,WAAW,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,qBAAqB,CAAC,CAAC,CAAC;iBAC/I;gBAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACnC,OAAO,qBAAqB,CAAC;gBACjC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,IAAiB;QACrD,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC;SAC1B;QAED,IAAI,OAAiC,CAAC;QACtC,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;SAChE;aAAM;YACH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1G,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC;SAC9F;QAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACpC,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAEO,iBAAiB,CAAC,OAAe,EAAE,OAAuB;QAC9D,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;YACvB,MAAM,YAAY,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC/C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,CAAC,KAAK,EAAE,CAAC;YACvD,MAAM,OAAO,GAAG;gBACZ,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;aAC3D,CAAC;YAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,WAAW,GAAG,eAAe,IAAI,CAAC,IAAI,QAAQ,CAAC;gBACrD,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC5E,YAAY,CAAC,IAAI,CACb,IAAI,CAAC,cAAc,CAAC,GAAG,WAAW,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAiB,EAAE,EAAE;oBAC5F,MAAM,KAAK,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;oBACjH,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;oBAC7C,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,GAAG,CAAC;oBAC/C,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;oBACjD,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC;gBACjE,CAAC,CAAC,CACL,CAAC;aACL;YAED,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAChD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBACvC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBACH,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,IAAI,IAAI,KAAK,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;gBAChG,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpB,aAAa,CAAC,yBAAyB,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;iBACrF;gBACD,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpB,aAAa,CAAC,yBAAyB,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;iBACrF;gBACD,IAAI,OAAO,CAAC,MAAM,EAAE;oBAChB,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;iBACzC;gBACD,OAAO,CAAC,YAAa,CAAC,KAAK,GAAG,aAAa,CAAC;YAChD,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,YAAY,GAAG;gBACnB,MAAM,EAAE,OAAO;aAClB,CAAC;SACL;QAED,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC;IACvC,CAAC;IAEO,eAAe,CACnB,OAAe,EACf,KAAoB,EACpB,MAA8C,EAC9C,IAAY,EACZ,WAAoB;QAEpB,QAAQ,MAAM,EAAE;YACZ,6DAAgD,CAAC,CAAC;gBAC9C,OAAO,CAAC,YAAoB,EAAE,EAAE;oBAC5B,MAAM,WAAW,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC;oBAC/D,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC5B,CAAC,CAAC;aACL;YACD,6DAAgD,CAAC,CAAC;gBAC9C,OAAO,GAAG,EAAE;oBACR,KAAK,CAAC,IAAI,EAAE,CAAC;gBACjB,CAAC,CAAC;aACL;YACD,+DAAiD,CAAC,CAAC;gBAC/C,OAAO,GAAG,EAAE;oBACR,KAAK,CAAC,KAAK,EAAE,CAAC;gBAClB,CAAC,CAAC;aACL;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,wBAAwB,MAAM,EAAE,CAAC,CAAC;aAC/D;SACJ;IACL,CAAC;IAEO,wBAAwB,CAC5B,OAAe,EACf,gBAAwB,EACxB,SAAqB,EACrB,KAA4B,EAC5B,qBAAqC;QAErC,IAAI,qBAAqB,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,EAAE;YACtD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QACD,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;QACnC,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,IAAI,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtD,MAAM,KAAK,GAAG,OAAO,CAAC,YAAa,CAAC,KAAK,CAAC;YAC1C,IAAI,KAAK,EAAE;gBACP,MAAM,qBAAqB,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChJ,gBAAgB,CAAC,SAAS,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;gBAC3D,uEAAuE;gBACvE,qBAAqB,CAAC,6BAA6B,CAAC,GAAG,CAAC,GAAG,EAAE;oBACzD,KAAK,CAAC,IAAI,EAAE,CAAC;gBACjB,CAAC,CAAC,CAAC;gBACH,qBAAqB,CAAC,+BAA+B,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC3D,KAAK,CAAC,KAAK,EAAE,CAAC;gBAClB,CAAC,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport type { AnimationGroup } from \"core/Animations/animationGroup\";\r\nimport { AnimationEvent } from \"core/Animations/animationEvent\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\nimport { Sound } from \"core/Audio/sound\";\r\nimport { WeightedSound } from \"core/Audio/weightedsound\";\r\n\r\nimport type { IArrayItem, IScene, INode, IAnimation } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\nimport type { IMSFTAudioEmitter_Clip, IMSFTAudioEmitter_Emitter, IMSFTAudioEmitter_EmittersReference, IMSFTAudioEmitter_AnimationEvent } from \"babylonjs-gltf2interface\";\r\nimport { IMSFTAudioEmitter_AnimationEventAction } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"MSFT_audio_emitter\";\r\n\r\ninterface ILoaderClip extends IMSFTAudioEmitter_Clip, IArrayItem {\r\n    _objectURL?: Promise<string>;\r\n}\r\n\r\ninterface ILoaderEmitter extends IMSFTAudioEmitter_Emitter, IArrayItem {\r\n    _babylonData?: {\r\n        sound?: WeightedSound;\r\n        loaded: Promise<void>;\r\n    };\r\n    _babylonSounds: Sound[];\r\n}\r\n\r\ninterface IMSFTAudioEmitter {\r\n    clips: ILoaderClip[];\r\n    emitters: ILoaderEmitter[];\r\n}\r\n\r\ninterface ILoaderAnimationEvent extends IMSFTAudioEmitter_AnimationEvent, IArrayItem {}\r\n\r\ninterface ILoaderAnimationEvents {\r\n    events: ILoaderAnimationEvent[];\r\n}\r\n\r\n/**\r\n * [Specification](https://github.com/najadojo/glTF/blob/MSFT_audio_emitter/extensions/2.0/Vendor/MSFT_audio_emitter/README.md)\r\n * !!! Experimental Extension Subject to Changes !!!\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class MSFT_audio_emitter implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n    private _clips: Array<ILoaderClip>;\r\n    private _emitters: Array<ILoaderEmitter>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n        (this._clips as any) = null;\r\n        (this._emitters as any) = null;\r\n    }\r\n\r\n    /** @internal */\r\n    public onLoading(): void {\r\n        const extensions = this._loader.gltf.extensions;\r\n        if (extensions && extensions[this.name]) {\r\n            const extension = extensions[this.name] as IMSFTAudioEmitter;\r\n\r\n            this._clips = extension.clips;\r\n            this._emitters = extension.emitters;\r\n\r\n            ArrayItem.Assign(this._clips);\r\n            ArrayItem.Assign(this._emitters);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadSceneAsync(context: string, scene: IScene): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IMSFTAudioEmitter_EmittersReference>(context, scene, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n\r\n            promises.push(this._loader.loadSceneAsync(context, scene));\r\n\r\n            for (const emitterIndex of extension.emitters) {\r\n                const emitter = ArrayItem.Get(`${extensionContext}/emitters`, this._emitters, emitterIndex);\r\n                if (\r\n                    emitter.refDistance != undefined ||\r\n                    emitter.maxDistance != undefined ||\r\n                    emitter.rolloffFactor != undefined ||\r\n                    emitter.distanceModel != undefined ||\r\n                    emitter.innerAngle != undefined ||\r\n                    emitter.outerAngle != undefined\r\n                ) {\r\n                    throw new Error(`${extensionContext}: Direction or Distance properties are not allowed on emitters attached to a scene`);\r\n                }\r\n\r\n                promises.push(this._loadEmitterAsync(`${extensionContext}/emitters/${emitter.index}`, emitter));\r\n            }\r\n\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadNodeAsync(context: string, node: INode, assign: (babylonTransformNode: TransformNode) => void): Nullable<Promise<TransformNode>> {\r\n        return GLTFLoader.LoadExtensionAsync<IMSFTAudioEmitter_EmittersReference, TransformNode>(context, node, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n\r\n            return this._loader\r\n                .loadNodeAsync(extensionContext, node, (babylonMesh) => {\r\n                    for (const emitterIndex of extension.emitters) {\r\n                        const emitter = ArrayItem.Get(`${extensionContext}/emitters`, this._emitters, emitterIndex);\r\n                        promises.push(\r\n                            this._loadEmitterAsync(`${extensionContext}/emitters/${emitter.index}`, emitter).then(() => {\r\n                                for (const sound of emitter._babylonSounds) {\r\n                                    sound.attachToMesh(babylonMesh);\r\n                                    if (emitter.innerAngle != undefined || emitter.outerAngle != undefined) {\r\n                                        sound.setLocalDirectionToMesh(Vector3.Forward());\r\n                                        sound.setDirectionalCone(\r\n                                            2 * Tools.ToDegrees(emitter.innerAngle == undefined ? Math.PI : emitter.innerAngle),\r\n                                            2 * Tools.ToDegrees(emitter.outerAngle == undefined ? Math.PI : emitter.outerAngle),\r\n                                            0\r\n                                        );\r\n                                    }\r\n                                }\r\n                            })\r\n                        );\r\n                    }\r\n\r\n                    assign(babylonMesh);\r\n                })\r\n                .then((babylonMesh) => {\r\n                    return Promise.all(promises).then(() => {\r\n                        return babylonMesh;\r\n                    });\r\n                });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadAnimationAsync(context: string, animation: IAnimation): Nullable<Promise<AnimationGroup>> {\r\n        return GLTFLoader.LoadExtensionAsync<ILoaderAnimationEvents, AnimationGroup>(context, animation, this.name, (extensionContext, extension) => {\r\n            return this._loader.loadAnimationAsync(context, animation).then((babylonAnimationGroup) => {\r\n                const promises = new Array<Promise<any>>();\r\n\r\n                ArrayItem.Assign(extension.events);\r\n                for (const event of extension.events) {\r\n                    promises.push(this._loadAnimationEventAsync(`${extensionContext}/events/${event.index}`, context, animation, event, babylonAnimationGroup));\r\n                }\r\n\r\n                return Promise.all(promises).then(() => {\r\n                    return babylonAnimationGroup;\r\n                });\r\n            });\r\n        });\r\n    }\r\n\r\n    private _loadClipAsync(context: string, clip: ILoaderClip): Promise<string> {\r\n        if (clip._objectURL) {\r\n            return clip._objectURL;\r\n        }\r\n\r\n        let promise: Promise<ArrayBufferView>;\r\n        if (clip.uri) {\r\n            promise = this._loader.loadUriAsync(context, clip, clip.uri);\r\n        } else {\r\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._loader.gltf.bufferViews, clip.bufferView);\r\n            promise = this._loader.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView);\r\n        }\r\n\r\n        clip._objectURL = promise.then((data) => {\r\n            return URL.createObjectURL(new Blob([data], { type: clip.mimeType }));\r\n        });\r\n\r\n        return clip._objectURL;\r\n    }\r\n\r\n    private _loadEmitterAsync(context: string, emitter: ILoaderEmitter): Promise<void> {\r\n        emitter._babylonSounds = emitter._babylonSounds || [];\r\n        if (!emitter._babylonData) {\r\n            const clipPromises = new Array<Promise<any>>();\r\n            const name = emitter.name || `emitter${emitter.index}`;\r\n            const options = {\r\n                loop: false,\r\n                autoplay: false,\r\n                volume: emitter.volume == undefined ? 1 : emitter.volume,\r\n            };\r\n\r\n            for (let i = 0; i < emitter.clips.length; i++) {\r\n                const clipContext = `/extensions/${this.name}/clips`;\r\n                const clip = ArrayItem.Get(clipContext, this._clips, emitter.clips[i].clip);\r\n                clipPromises.push(\r\n                    this._loadClipAsync(`${clipContext}/${emitter.clips[i].clip}`, clip).then((objectURL: string) => {\r\n                        const sound = (emitter._babylonSounds[i] = new Sound(name, objectURL, this._loader.babylonScene, null, options));\r\n                        sound.refDistance = emitter.refDistance || 1;\r\n                        sound.maxDistance = emitter.maxDistance || 256;\r\n                        sound.rolloffFactor = emitter.rolloffFactor || 1;\r\n                        sound.distanceModel = emitter.distanceModel || \"exponential\";\r\n                    })\r\n                );\r\n            }\r\n\r\n            const promise = Promise.all(clipPromises).then(() => {\r\n                const weights = emitter.clips.map((clip) => {\r\n                    return clip.weight || 1;\r\n                });\r\n                const weightedSound = new WeightedSound(emitter.loop || false, emitter._babylonSounds, weights);\r\n                if (emitter.innerAngle) {\r\n                    weightedSound.directionalConeInnerAngle = 2 * Tools.ToDegrees(emitter.innerAngle);\r\n                }\r\n                if (emitter.outerAngle) {\r\n                    weightedSound.directionalConeOuterAngle = 2 * Tools.ToDegrees(emitter.outerAngle);\r\n                }\r\n                if (emitter.volume) {\r\n                    weightedSound.volume = emitter.volume;\r\n                }\r\n                emitter._babylonData!.sound = weightedSound;\r\n            });\r\n\r\n            emitter._babylonData = {\r\n                loaded: promise,\r\n            };\r\n        }\r\n\r\n        return emitter._babylonData.loaded;\r\n    }\r\n\r\n    private _getEventAction(\r\n        context: string,\r\n        sound: WeightedSound,\r\n        action: IMSFTAudioEmitter_AnimationEventAction,\r\n        time: number,\r\n        startOffset?: number\r\n    ): (currentFrame: number) => void {\r\n        switch (action) {\r\n            case IMSFTAudioEmitter_AnimationEventAction.play: {\r\n                return (currentFrame: number) => {\r\n                    const frameOffset = (startOffset || 0) + (currentFrame - time);\r\n                    sound.play(frameOffset);\r\n                };\r\n            }\r\n            case IMSFTAudioEmitter_AnimationEventAction.stop: {\r\n                return () => {\r\n                    sound.stop();\r\n                };\r\n            }\r\n            case IMSFTAudioEmitter_AnimationEventAction.pause: {\r\n                return () => {\r\n                    sound.pause();\r\n                };\r\n            }\r\n            default: {\r\n                throw new Error(`${context}: Unsupported action ${action}`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _loadAnimationEventAsync(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        event: ILoaderAnimationEvent,\r\n        babylonAnimationGroup: AnimationGroup\r\n    ): Promise<void> {\r\n        if (babylonAnimationGroup.targetedAnimations.length == 0) {\r\n            return Promise.resolve();\r\n        }\r\n        const babylonAnimation = babylonAnimationGroup.targetedAnimations[0];\r\n        const emitterIndex = event.emitter;\r\n        const emitter = ArrayItem.Get(`/extensions/${this.name}/emitters`, this._emitters, emitterIndex);\r\n        return this._loadEmitterAsync(context, emitter).then(() => {\r\n            const sound = emitter._babylonData!.sound;\r\n            if (sound) {\r\n                const babylonAnimationEvent = new AnimationEvent(event.time, this._getEventAction(context, sound, event.action, event.time, event.startOffset));\r\n                babylonAnimation.animation.addEvent(babylonAnimationEvent);\r\n                // Make sure all started audio stops when this animation is terminated.\r\n                babylonAnimationGroup.onAnimationGroupEndObservable.add(() => {\r\n                    sound.stop();\r\n                });\r\n                babylonAnimationGroup.onAnimationGroupPauseObservable.add(() => {\r\n                    sound.pause();\r\n                });\r\n            }\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new MSFT_audio_emitter(loader));\r\n"]}