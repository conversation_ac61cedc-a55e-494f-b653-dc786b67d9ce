{"version": 3, "file": "fireMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/fire/fireMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,2CAA6B;AAE/H,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,IAAI,EAAE,qCAAuB;AAEtC,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAC1D,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAGpD,OAAO,iBAAiB,CAAC;AACzB,OAAO,eAAe,CAAC;AACvB,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,mBAAoB,SAAQ,eAAe;IAsB7C;QACI,KAAK,EAAE,CAAC;QAtBL,YAAO,GAAG,KAAK,CAAC;QAChB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,iBAAY,GAAG,KAAK,CAAC;QACrB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAG,KAAK,CAAC;QACpB,iBAAY,GAAG,CAAC,CAAC;QACjB,yBAAoB,GAAG,CAAC,CAAC;QACzB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,YAAa,SAAQ,YAAY;IAyB1C,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAThB,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGnC,UAAK,GAAG,GAAG,CAAC;QAEX,mBAAc,GAAG,IAAI,MAAM,EAAE,CAAC;QAC9B,cAAS,GAAW,CAAC,CAAC;IAI9B,CAAC;IAEM,iBAAiB;QACpB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;SACvD;QAED,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,WAAW;QACX,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE;oBACjC,OAAO,KAAK,CAAC;iBAChB;qBAAM;oBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;oBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;iBAC1B;aACJ;SACJ;QAED,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAExD,QAAQ;QACR,IAAI,OAAO,CAAC,aAAa,EAAE;YACvB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,gBAAgB,CAAC;YAC/D,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;SAC9G;QAED,kDAAkD;QAClD,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEvE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,uBAAuB;YACvB,MAAM,UAAU,GAAG,MAAM,CAAC;YAE1B,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,eAAe;gBACf,QAAQ;gBACR,eAAe;gBACf,OAAO;gBACP,MAAM;gBACN,OAAO;aACV,CAAC;YACF,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAE/B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACV;gBACI,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,EAAE;gBACvB,QAAQ,EAAE;oBACN,gBAAgB;oBAChB,OAAO;oBACP,mBAAmB;oBACnB,gBAAgB;iBACnB;gBACD,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,IAAI;gBACrB,qBAAqB,EAAE,CAAC;gBACxB,yBAAyB,EAAE,IAAI;aAClC,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,WAAW;YACX,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAEtE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACjH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBAEvF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC5E,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aACzE;YAED,aAAa;YACb,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/C,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAEjG,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC3E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,OAAO;QACP,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAEpD,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAEjD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,cAAc;QACjB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QACD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAChH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACzC;QACD,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,OAAO,CAAC,kBAA4B;QACvC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;SAClC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACrC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAe,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACxG,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;QACxD,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC/D,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;SAC1E;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;SAChF;QAED,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;SAC1E;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEtD,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC9D,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAE9B,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAE9B,QAAQ,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;QAExB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACtC,QAAQ,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;QAClD,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QAEtC,IAAI,MAAM,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpF;QAED,IAAI,MAAM,CAAC,kBAAkB,EAAE;YAC3B,QAAQ,CAAC,kBAAkB,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SAC1F;QAED,IAAI,MAAM,CAAC,eAAe,EAAE;YACxB,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;SACpF;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;AA7WG;IADC,kBAAkB,CAAC,gBAAgB,CAAC;qDACU;AAE/C;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACR;AAG7C;IADC,kBAAkB,CAAC,mBAAmB,CAAC;wDACU;AAElD;IADC,gBAAgB,CAAC,kCAAkC,CAAC;uDACL;AAGhD;IADC,kBAAkB,CAAC,gBAAgB,CAAC;qDACU;AAE/C;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACR;AAG7C;IADC,iBAAiB,CAAC,SAAS,CAAC;kDACa;AAG1C;IADC,SAAS,EAAE;2CACO;AA6VvB,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { serializeAsTexture, serialize, expandToProperty, serializeAsColor3, SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { Tags } from \"core/Misc/tags\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { MaterialFlags } from \"core/Materials/materialFlags\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\n\r\nimport \"./fire.fragment\";\r\nimport \"./fire.vertex\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass FireMaterialDefines extends MaterialDefines {\r\n    public DIFFUSE = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public UV1 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public BonesPerMesh = 0;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class FireMaterial extends PushMaterial {\r\n    @serializeAsTexture(\"diffuseTexture\")\r\n    private _diffuseTexture: Nullable<BaseTexture>;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"distortionTexture\")\r\n    private _distortionTexture: Nullable<BaseTexture>;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public distortionTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"opacityTexture\")\r\n    private _opacityTexture: Nullable<BaseTexture>;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public opacityTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsColor3(\"diffuse\")\r\n    public diffuseColor = new Color3(1, 1, 1);\r\n\r\n    @serialize()\r\n    public speed = 1.0;\r\n\r\n    private _scaledDiffuse = new Color3();\r\n    private _lastTime: number = 0;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return true;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new FireMaterialDefines();\r\n        }\r\n\r\n        const defines = <FireMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Textures\r\n        if (defines._areTexturesDirty) {\r\n            defines._needUVs = false;\r\n            if (this._diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                if (!this._diffuseTexture.isReady()) {\r\n                    return false;\r\n                } else {\r\n                    defines._needUVs = true;\r\n                    defines.DIFFUSE = true;\r\n                }\r\n            }\r\n        }\r\n\r\n        defines.ALPHATEST = this._opacityTexture ? true : false;\r\n\r\n        // Misc.\r\n        if (defines._areMiscDirty) {\r\n            defines.POINTSIZE = this.pointsCloud || scene.forcePointsCloud;\r\n            defines.FOG = scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE && this.fogEnabled;\r\n        }\r\n\r\n        // Values that need to be evaluated on every frame\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, false, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            // Legacy browser patch\r\n            const shaderName = \"fire\";\r\n\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"vDiffuseInfos\",\r\n                \"mBones\",\r\n                \"diffuseMatrix\",\r\n                // Fire\r\n                \"time\",\r\n                \"speed\",\r\n            ];\r\n            addClipPlaneUniforms(uniforms);\r\n\r\n            const join = defines.toString();\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    {\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: [],\r\n                        samplers: [\r\n                            \"diffuseSampler\",\r\n                            // Fire\r\n                            \"distortionSampler\",\r\n                            \"opacitySampler\",\r\n                        ],\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: null,\r\n                        maxSimultaneousLights: 4,\r\n                        transformFeedbackVaryings: null,\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <FireMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, this._activeEffect);\r\n\r\n        if (this._mustRebind(scene, effect)) {\r\n            // Textures\r\n            if (this._diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                this._activeEffect.setTexture(\"diffuseSampler\", this._diffuseTexture);\r\n\r\n                this._activeEffect.setFloat2(\"vDiffuseInfos\", this._diffuseTexture.coordinatesIndex, this._diffuseTexture.level);\r\n                this._activeEffect.setMatrix(\"diffuseMatrix\", this._diffuseTexture.getTextureMatrix());\r\n\r\n                this._activeEffect.setTexture(\"distortionSampler\", this._distortionTexture);\r\n                this._activeEffect.setTexture(\"opacitySampler\", this._opacityTexture);\r\n            }\r\n\r\n            // Clip plane\r\n            bindClipPlane(this._activeEffect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        this._activeEffect.setColor4(\"vDiffuseColor\", this._scaledDiffuse, this.alpha * mesh.visibility);\r\n\r\n        // View\r\n        if (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        // Time\r\n        this._lastTime += scene.getEngine().getDeltaTime();\r\n        this._activeEffect.setFloat(\"time\", this._lastTime);\r\n\r\n        // Speed\r\n        this._activeEffect.setFloat(\"speed\", this.speed);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results = [];\r\n\r\n        if (this._diffuseTexture && this._diffuseTexture.animations && this._diffuseTexture.animations.length > 0) {\r\n            results.push(this._diffuseTexture);\r\n        }\r\n        if (this._distortionTexture && this._distortionTexture.animations && this._distortionTexture.animations.length > 0) {\r\n            results.push(this._distortionTexture);\r\n        }\r\n        if (this._opacityTexture && this._opacityTexture.animations && this._opacityTexture.animations.length > 0) {\r\n            results.push(this._opacityTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._diffuseTexture) {\r\n            activeTextures.push(this._diffuseTexture);\r\n        }\r\n\r\n        if (this._distortionTexture) {\r\n            activeTextures.push(this._distortionTexture);\r\n        }\r\n\r\n        if (this._opacityTexture) {\r\n            activeTextures.push(this._opacityTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._distortionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._opacityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"FireMaterial\";\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        if (this._diffuseTexture) {\r\n            this._diffuseTexture.dispose();\r\n        }\r\n        if (this._distortionTexture) {\r\n            this._distortionTexture.dispose();\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public clone(name: string): FireMaterial {\r\n        return SerializationHelper.Clone<FireMaterial>(() => new FireMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.FireMaterial\";\r\n        serializationObject.diffuseColor = this.diffuseColor.asArray();\r\n        serializationObject.speed = this.speed;\r\n\r\n        if (this._diffuseTexture) {\r\n            serializationObject._diffuseTexture = this._diffuseTexture.serialize();\r\n        }\r\n\r\n        if (this._distortionTexture) {\r\n            serializationObject._distortionTexture = this._distortionTexture.serialize();\r\n        }\r\n\r\n        if (this._opacityTexture) {\r\n            serializationObject._opacityTexture = this._opacityTexture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): FireMaterial {\r\n        const material = new FireMaterial(source.name, scene);\r\n\r\n        material.diffuseColor = Color3.FromArray(source.diffuseColor);\r\n        material.speed = source.speed;\r\n\r\n        material.alpha = source.alpha;\r\n\r\n        material.id = source.id;\r\n\r\n        Tags.AddTagsTo(material, source.tags);\r\n        material.backFaceCulling = source.backFaceCulling;\r\n        material.wireframe = source.wireframe;\r\n\r\n        if (source._diffuseTexture) {\r\n            material._diffuseTexture = Texture.Parse(source._diffuseTexture, scene, rootUrl);\r\n        }\r\n\r\n        if (source._distortionTexture) {\r\n            material._distortionTexture = Texture.Parse(source._distortionTexture, scene, rootUrl);\r\n        }\r\n\r\n        if (source._opacityTexture) {\r\n            material._opacityTexture = Texture.Parse(source._opacityTexture, scene, rootUrl);\r\n        }\r\n\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FireMaterial\", FireMaterial);\r\n"]}