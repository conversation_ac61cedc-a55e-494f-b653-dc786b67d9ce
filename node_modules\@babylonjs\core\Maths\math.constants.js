/**
 * <PERSON><PERSON> used to convert a value to gamma space
 * @ignorenaming
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
export const ToGammaSpace = 1 / 2.2;
/**
 * Constant used to convert a value to linear space
 * @ignorenaming
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
export const ToLinearSpace = 2.2;
/**
 * Constant Golden Ratio value in Babylon.js
 * @ignorenaming
 */
export const PHI = (1 + Math.sqrt(5)) / 2;
/**
 * Constant used to define the minimal number value in Babylon.js
 * @ignorenaming
 */
// eslint-disable-next-line @typescript-eslint/naming-convention
const Epsilon = 0.001;
export { Epsilon };
//# sourceMappingURL=math.constants.js.map