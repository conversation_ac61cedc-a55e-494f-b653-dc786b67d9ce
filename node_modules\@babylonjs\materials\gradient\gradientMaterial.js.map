{"version": 3, "file": "gradientMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/gradient/gradientMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,2CAA6B;AAE3G,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAG/C,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAE/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAEpD,OAAO,qBAAqB,CAAC;AAC7B,OAAO,mBAAmB,CAAC;AAC3B,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,uBAAwB,SAAQ,eAAe;IAwBjD;QACI,KAAK,EAAE,CAAC;QAxBL,aAAQ,GAAG,KAAK,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,iBAAY,GAAG,KAAK,CAAC;QACrB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,WAAM,GAAG,KAAK,CAAC;QACf,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAmC9C,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAlCf,2BAAsB,GAAG,CAAC,CAAC;QAInC,yCAAyC;QAElC,aAAQ,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAG/B,kBAAa,GAAG,GAAG,CAAC;QAE3B,0CAA0C;QAEnC,gBAAW,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGlC,qBAAgB,GAAG,GAAG,CAAC;QAE9B,kBAAkB;QAEX,WAAM,GAAG,CAAC,CAAC;QAGX,UAAK,GAAG,GAAG,CAAC;QAGZ,eAAU,GAAG,GAAG,CAAC;QAGhB,qBAAgB,GAAG,KAAK,CAAC;IAMjC,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG,IAAI,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;IACvF,CAAC;IAEM,gBAAgB;QACnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,uBAAuB,EAAE,CAAC;SAC3D;QAED,MAAM,OAAO,GAA4B,OAAO,CAAC,eAAe,CAAC;QACjE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAExI,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/I,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEzC,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEvE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAE7D,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,uBAAuB;YACvB,MAAM,UAAU,GAAG,UAAU,CAAC;YAC9B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAEhC,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,QAAQ;gBACR,YAAY;gBACZ,OAAO;aACV,CAAC;YACF,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;YAE3C,cAAc,CAAC,8BAA8B,CAAyB;gBAClE,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE;aAChD,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAA4B,OAAO,CAAC,eAAe,CAAC;QACjE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEjD,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,aAAa;YACb,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEnC,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACnG;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC3E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,cAAc;QACjB,OAAO,EAAE,CAAC;IACd,CAAC;IAEM,OAAO,CAAC,kBAA4B;QACvC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC9F,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,0BAA0B,CAAC;QAC5D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,UAAU;IACH,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC7G,CAAC;CACJ;AA3QG;IADC,SAAS,CAAC,uBAAuB,CAAC;gEACA;AAEnC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;+DACd;AAIrC;IADC,iBAAiB,EAAE;kDACkB;AAGtC;IADC,SAAS,EAAE;uDACe;AAI3B;IADC,iBAAiB,EAAE;qDACqB;AAGzC;IADC,SAAS,EAAE;0DACkB;AAI9B;IADC,SAAS,EAAE;gDACM;AAGlB;IADC,SAAS,EAAE;+CACO;AAGnB;IADC,SAAS,EAAE;oDACY;AAGxB;IADC,SAAS,CAAC,iBAAiB,CAAC;0DACI;AAEjC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;yDACnB;AA8OpC,aAAa,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { serialize, expandToProperty, serializeAsColor3, SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport type { IEffectCreationOptions } from \"core/Materials/effect\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nimport \"./gradient.fragment\";\r\nimport \"./gradient.vertex\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass GradientMaterialDefines extends MaterialDefines {\r\n    public EMISSIVE = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public NORMAL = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class GradientMaterial extends PushMaterial {\r\n    @serialize(\"maxSimultaneousLights\")\r\n    private _maxSimultaneousLights = 4;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights: number;\r\n\r\n    // The gradient top color, red by default\r\n    @serializeAsColor3()\r\n    public topColor = new Color3(1, 0, 0);\r\n\r\n    @serialize()\r\n    public topColorAlpha = 1.0;\r\n\r\n    // The gradient top color, blue by default\r\n    @serializeAsColor3()\r\n    public bottomColor = new Color3(0, 0, 1);\r\n\r\n    @serialize()\r\n    public bottomColorAlpha = 1.0;\r\n\r\n    // Gradient offset\r\n    @serialize()\r\n    public offset = 0;\r\n\r\n    @serialize()\r\n    public scale = 1.0;\r\n\r\n    @serialize()\r\n    public smoothness = 1.0;\r\n\r\n    @serialize(\"disableLighting\")\r\n    private _disableLighting = false;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting: boolean;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return this.alpha < 1.0 || this.topColorAlpha < 1.0 || this.bottomColorAlpha < 1.0;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return true;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new GradientMaterialDefines();\r\n        }\r\n\r\n        const defines = <GradientMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, this.pointsCloud, this.fogEnabled, this._shouldTurnAlphaTestOn(mesh), defines);\r\n\r\n        defines._needNormals = MaterialHelper.PrepareDefinesForLights(scene, mesh, defines, false, this._maxSimultaneousLights, this._disableLighting);\r\n\r\n        defines.EMISSIVE = this._disableLighting;\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, false, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            MaterialHelper.HandleFallbacksForShadows(defines, fallbacks);\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (defines.UV2) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            // Legacy browser patch\r\n            const shaderName = \"gradient\";\r\n            const join = defines.toString();\r\n\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vLightsType\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"mBones\",\r\n                \"topColor\",\r\n                \"bottomColor\",\r\n                \"offset\",\r\n                \"smoothness\",\r\n                \"scale\",\r\n            ];\r\n            addClipPlaneUniforms(uniforms);\r\n            const samplers: string[] = [];\r\n            const uniformBuffers = new Array<string>();\r\n\r\n            MaterialHelper.PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: 4,\r\n            });\r\n\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: { maxSimultaneousLights: 4 },\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <GradientMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, effect);\r\n\r\n        if (this._mustRebind(scene, effect)) {\r\n            // Clip plane\r\n            bindClipPlane(effect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        if (scene.lightsEnabled && !this.disableLighting) {\r\n            MaterialHelper.BindLights(scene, mesh, this._activeEffect, defines, this.maxSimultaneousLights);\r\n        }\r\n\r\n        // View\r\n        if (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._activeEffect.setColor4(\"topColor\", this.topColor, this.topColorAlpha);\r\n        this._activeEffect.setColor4(\"bottomColor\", this.bottomColor, this.bottomColorAlpha);\r\n        this._activeEffect.setFloat(\"offset\", this.offset);\r\n        this._activeEffect.setFloat(\"scale\", this.scale);\r\n        this._activeEffect.setFloat(\"smoothness\", this.smoothness);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public getAnimatables(): IAnimatable[] {\r\n        return [];\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public clone(name: string): GradientMaterial {\r\n        return SerializationHelper.Clone(() => new GradientMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.GradientMaterial\";\r\n        return serializationObject;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"GradientMaterial\";\r\n    }\r\n\r\n    // Statics\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): GradientMaterial {\r\n        return SerializationHelper.Parse(() => new GradientMaterial(source.name, scene), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.GradientMaterial\", GradientMaterial);\r\n"]}