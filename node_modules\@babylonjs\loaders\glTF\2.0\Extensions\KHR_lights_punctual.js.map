{"version": 3, "file": "KHR_lights_punctual.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_lights_punctual.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,6CAA+B;AACjD,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,gBAAgB,EAAE,mDAAqC;AAChE,OAAO,EAAE,UAAU,EAAE,6CAA+B;AACpD,OAAO,EAAE,SAAS,EAAE,4CAA8B;AAClD,OAAO,EAAE,KAAK,EAAE,wCAA0B;AAO1C,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAEtD,MAAM,IAAI,GAAG,qBAAqB,CAAC;AAEnC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,UAAU;IAenB;;OAEG;IACH,YAAY,MAAkB;QAjB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAexB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,SAAS;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAQ,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC;YAChC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAClC;IACL,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe,EAAE,IAAW,EAAE,MAAqD;QACpG,OAAO,UAAU,CAAC,kBAAkB,CAAmD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC7I,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;gBAC7D,IAAI,YAAmB,CAAC;gBAExB,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC7E,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;gBAE5C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBAElF,QAAQ,KAAK,CAAC,IAAI,EAAE;oBAChB,gEAA4C,CAAC,CAAC;wBAC1C,YAAY,GAAG,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBACzF,MAAM;qBACT;oBACD,oDAAsC,CAAC,CAAC;wBACpC,YAAY,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC/E,MAAM;qBACT;oBACD,kDAAqC,CAAC,CAAC;wBACnC,MAAM,gBAAgB,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAClH,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;wBACxF,gBAAgB,CAAC,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;wBACnF,YAAY,GAAG,gBAAgB,CAAC;wBAChC,MAAM;qBACT;oBACD,OAAO,CAAC,CAAC;wBACL,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,sBAAsB,GAAG,KAAK,CAAC;wBACzD,MAAM,IAAI,KAAK,CAAC,GAAG,gBAAgB,yBAAyB,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;qBAC9E;iBACJ;gBAED,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBAC7D,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBACzD,KAAK,CAAC,aAAa,GAAG,YAAY,CAAC;gBAEnC,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC,YAAY,CAAC;gBAC9C,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACpF,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC5E,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC/E,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC;gBAElC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAE/C,UAAU,CAAC,kBAAkB,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;gBAE9D,MAAM,CAAC,WAAW,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { DirectionalLight } from \"core/Lights/directionalLight\";\r\nimport { PointLight } from \"core/Lights/pointLight\";\r\nimport { SpotLight } from \"core/Lights/spotLight\";\r\nimport { Light } from \"core/Lights/light\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\n\r\nimport type { IKHRLightsPunctual_LightReference } from \"babylonjs-gltf2interface\";\r\nimport { KHRLightsPunctual_LightType } from \"babylonjs-gltf2interface\";\r\nimport type { INode, IKHRLightsPunctual_Light } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\n\r\nconst NAME = \"KHR_lights_punctual\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_lights_punctual/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_lights implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /** hidden */\r\n    private _loader: GLTFLoader;\r\n    private _lights?: IKHRLightsPunctual_Light[];\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n        delete this._lights;\r\n    }\r\n\r\n    /** @internal */\r\n    public onLoading(): void {\r\n        const extensions = this._loader.gltf.extensions;\r\n        if (extensions && extensions[this.name]) {\r\n            const extension = extensions[this.name] as any;\r\n            this._lights = extension.lights;\r\n            ArrayItem.Assign(this._lights);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadNodeAsync(context: string, node: INode, assign: (babylonTransformNode: TransformNode) => void): Nullable<Promise<TransformNode>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRLightsPunctual_LightReference, TransformNode>(context, node, this.name, (extensionContext, extension) => {\r\n            return this._loader.loadNodeAsync(context, node, (babylonMesh) => {\r\n                let babylonLight: Light;\r\n\r\n                const light = ArrayItem.Get(extensionContext, this._lights, extension.light);\r\n                const name = light.name || babylonMesh.name;\r\n\r\n                this._loader.babylonScene._blockEntityCollection = !!this._loader._assetContainer;\r\n\r\n                switch (light.type) {\r\n                    case KHRLightsPunctual_LightType.DIRECTIONAL: {\r\n                        babylonLight = new DirectionalLight(name, Vector3.Backward(), this._loader.babylonScene);\r\n                        break;\r\n                    }\r\n                    case KHRLightsPunctual_LightType.POINT: {\r\n                        babylonLight = new PointLight(name, Vector3.Zero(), this._loader.babylonScene);\r\n                        break;\r\n                    }\r\n                    case KHRLightsPunctual_LightType.SPOT: {\r\n                        const babylonSpotLight = new SpotLight(name, Vector3.Zero(), Vector3.Backward(), 0, 1, this._loader.babylonScene);\r\n                        babylonSpotLight.angle = ((light.spot && light.spot.outerConeAngle) || Math.PI / 4) * 2;\r\n                        babylonSpotLight.innerAngle = ((light.spot && light.spot.innerConeAngle) || 0) * 2;\r\n                        babylonLight = babylonSpotLight;\r\n                        break;\r\n                    }\r\n                    default: {\r\n                        this._loader.babylonScene._blockEntityCollection = false;\r\n                        throw new Error(`${extensionContext}: Invalid light type (${light.type})`);\r\n                    }\r\n                }\r\n\r\n                babylonLight._parentContainer = this._loader._assetContainer;\r\n                this._loader.babylonScene._blockEntityCollection = false;\r\n                light._babylonLight = babylonLight;\r\n\r\n                babylonLight.falloffType = Light.FALLOFF_GLTF;\r\n                babylonLight.diffuse = light.color ? Color3.FromArray(light.color) : Color3.White();\r\n                babylonLight.intensity = light.intensity == undefined ? 1 : light.intensity;\r\n                babylonLight.range = light.range == undefined ? Number.MAX_VALUE : light.range;\r\n                babylonLight.parent = babylonMesh;\r\n\r\n                this._loader._babylonLights.push(babylonLight);\r\n\r\n                GLTFLoader.AddPointerMetadata(babylonLight, extensionContext);\r\n\r\n                assign(babylonMesh);\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_lights(loader));\r\n"]}