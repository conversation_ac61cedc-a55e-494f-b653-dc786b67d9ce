{"version": 3, "file": "KHR_materials_ior.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_ior.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,mBAAmB,CAAC;AAEjC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,iBAAiB;IAuB1B;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAmB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACjH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YAC1F,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,uBAAuB,CAAC,OAAe,EAAE,UAA4B,EAAE,eAAyB;QACpG,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,IAAI,UAAU,CAAC,GAAG,KAAK,SAAS,EAAE;YAC9B,eAAe,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,CAAC;SACtD;aAAM;YACH,eAAe,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,CAAC;SACtE;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;;AA3DD;;GAEG;AACqB,8BAAY,GAAG,GAAG,CAAC;AA2D/C,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsIor } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_ior\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_ior/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_ior implements IGLTFLoaderExtension {\r\n    /**\r\n     * Default ior Value from the spec.\r\n     */\r\n    private static readonly _DEFAULT_IOR = 1.5;\r\n\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 180;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsIor>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadIorPropertiesAsync(extensionContext, extension, babylonMaterial));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadIorPropertiesAsync(context: string, properties: IKHRMaterialsIor, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        if (properties.ior !== undefined) {\r\n            babylonMaterial.indexOfRefraction = properties.ior;\r\n        } else {\r\n            babylonMaterial.indexOfRefraction = KHR_materials_ior._DEFAULT_IOR;\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_ior(loader));\r\n"]}