/**
 * Enum that determines the text-wrapping mode to use.
 */
export var InspectableType;
(function (InspectableType) {
    /**
     * Checkbox for booleans
     */
    InspectableType[InspectableType["Checkbox"] = 0] = "Checkbox";
    /**
     * Sliders for numbers
     */
    InspectableType[InspectableType["Slider"] = 1] = "Slider";
    /**
     * Vector3
     */
    InspectableType[InspectableType["Vector3"] = 2] = "Vector3";
    /**
     * Quaternions
     */
    InspectableType[InspectableType["Quaternion"] = 3] = "Quaternion";
    /**
     * Color3
     */
    InspectableType[InspectableType["Color3"] = 4] = "Color3";
    /**
     * String
     */
    InspectableType[InspectableType["String"] = 5] = "String";
    /**
     * Button
     */
    InspectableType[InspectableType["Button"] = 6] = "Button";
    /**
     * Options
     */
    InspectableType[InspectableType["Options"] = 7] = "Options";
    /**
     * Tab
     */
    InspectableType[InspectableType["Tab"] = 8] = "Tab";
    /**
     * File button
     */
    InspectableType[InspectableType["FileButton"] = 9] = "FileButton";
    /**
     * Vector2
     */
    InspectableType[InspectableType["Vector2"] = 10] = "Vector2";
})(InspectableType || (InspectableType = {}));
//# sourceMappingURL=iInspectable.js.map