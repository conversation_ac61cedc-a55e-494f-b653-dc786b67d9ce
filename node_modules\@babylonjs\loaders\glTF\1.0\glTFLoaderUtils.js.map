{"version": 3, "file": "glTFLoaderUtils.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/1.0/glTFLoaderUtils.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAG9G,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,6CAA+B;AAC3E,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAI1D;;;;GAIG;AACH,MAAM,OAAO,SAAS;IAClB;;;;;;;OAOG;IACI,MAAM,CAAC,SAAS,CAAC,KAAY,EAAE,MAAY,EAAE,SAAkC,EAAE,WAAmB,EAAE,cAAuC;QAChJ,IAAI,GAAG,GAAqB,IAAI,CAAC;QAEjC,IAAI,SAAS,CAAC,QAAQ,KAAK,OAAO,EAAE;YAChC,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;SACjC;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,YAAY,EAAE;YAC5C,GAAG,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;SACrC;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,MAAM,EAAE;YACtC,GAAG,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;SAC/B;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,2BAA2B,EAAE;YAC3D,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;SAC5F;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE;YAC3C,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SACjE;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,qBAAqB,EAAE;YACrD,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;SACtE;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,cAAc,EAAE;YAC9C,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC;SAC1C;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,aAAa,EAAE;YAC7C,GAAG,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,CAAC;SACxC;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,mBAAmB,EAAE;YACnD,GAAG,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,CAAC;SAC9C;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,kBAAkB,EAAE;YAClD,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;SAC1E;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,4BAA4B,EAAE;YAC5D,GAAG,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;SAC/E;aAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,uBAAuB,EAAE;YACvD,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC;SAC5D;QAED,IAAI,GAAG,EAAE;YACL,QAAQ,SAAS,CAAC,IAAI,EAAE;gBACpB,KAAK,cAAc,CAAC,UAAU;oBAC1B,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;oBACrE,MAAM;gBACV,KAAK,cAAc,CAAC,UAAU;oBAC1B,cAAc,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;oBACrE,MAAM;gBACV,KAAK,cAAc,CAAC,UAAU;oBAC1B,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;oBAC3C,MAAM;gBACV;oBACI,MAAM;aACb;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,UAAU,CAAC,cAAuC,EAAE,OAAe,EAAE,KAAU,EAAE,IAAY;QACvG,QAAQ,IAAI,EAAE;YACV,KAAK,cAAc,CAAC,KAAK;gBACrB,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACxC,OAAO,IAAI,CAAC;YAChB,KAAK,cAAc,CAAC,UAAU;gBAC1B,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7D,OAAO,IAAI,CAAC;YAChB,KAAK,cAAc,CAAC,UAAU;gBAC1B,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7D,OAAO,IAAI,CAAC;YAChB,KAAK,cAAc,CAAC,UAAU;gBAC1B,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7D,OAAO,IAAI,CAAC;YAChB;gBACI,OAAO,KAAK,CAAC;SACpB;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY;QAClC,QAAQ,IAAI,EAAE;YACV,KAAK,gBAAgB,CAAC,aAAa;gBAC/B,OAAO,OAAO,CAAC,iBAAiB,CAAC;YACrC,KAAK,gBAAgB,CAAC,eAAe;gBACjC,OAAO,OAAO,CAAC,kBAAkB,CAAC;YACtC,KAAK,gBAAgB,CAAC,MAAM;gBACxB,OAAO,OAAO,CAAC,gBAAgB,CAAC;YACpC;gBACI,OAAO,OAAO,CAAC,gBAAgB,CAAC;SACvC;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,qBAAqB,CAAC,QAAuB;QACvD,wEAAwE;QACxE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE3B,QAAQ,IAAI,EAAE;YACV,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,EAAE,CAAC;YACd;gBACI,OAAO,CAAC,CAAC;SAChB;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,oBAAoB,CAAC,IAAY;QAC3C,QAAQ,IAAI,EAAE;YACV,KAAK,kBAAkB,CAAC,MAAM,CAAC;YAC/B,KAAK,kBAAkB,CAAC,qBAAqB,CAAC;YAC9C,KAAK,kBAAkB,CAAC,oBAAoB;gBACxC,OAAO,OAAO,CAAC,sBAAsB,CAAC;YAC1C,KAAK,kBAAkB,CAAC,OAAO,CAAC;YAChC,KAAK,kBAAkB,CAAC,sBAAsB;gBAC1C,OAAO,OAAO,CAAC,oBAAoB,CAAC;YACxC;gBACI,OAAO,OAAO,CAAC,qBAAqB,CAAC;SAC5C;IACL,CAAC;IAEM,MAAM,CAAC,uBAAuB,CACjC,WAAyB,EACzB,UAA2B,EAC3B,UAAkB,EAClB,UAAkB,EAClB,aAA6B;QAE7B,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC;QAEhD,MAAM,gBAAgB,GAAG,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC1E,IAAI,UAAU,GAAG,UAAU,GAAG,gBAAgB,CAAC,UAAU,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SACpD;QAED,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;QACvC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC;QAE1C,QAAQ,aAAa,EAAE;YACnB,KAAK,cAAc,CAAC,IAAI;gBACpB,OAAO,IAAI,SAAS,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YACzD,KAAK,cAAc,CAAC,aAAa;gBAC7B,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAC1D,KAAK,cAAc,CAAC,KAAK;gBACrB,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAC1D,KAAK,cAAc,CAAC,cAAc;gBAC9B,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YAC3D;gBACI,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;SAC/D;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,qBAAqB,CAAC,WAAyB,EAAE,QAAuB;QAClF,MAAM,UAAU,GAAoB,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACjF,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC9E,OAAO,SAAS,CAAC,uBAAuB,CAAC,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAqB;QAClD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE;YAC7B,MAAM,IAAI,MAAM,CAAC,YAAY,CAAO,IAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACjD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,kBAAkB,CAAC,KAAY;QACzC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;YAC7B,MAAM,CAAC,YAAY,CAAC,iCAAiC,CAAC,GAAG;gBACrD,wBAAwB;gBACxB,EAAE;gBACF,yBAAyB;gBACzB,0BAA0B;gBAC1B,EAAE;gBACF,0BAA0B;gBAC1B,EAAE;gBACF,iBAAiB;gBACjB,GAAG;gBACH,iEAAiE;gBACjE,GAAG;aACN,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,MAAM,CAAC,YAAY,CAAC,gCAAgC,CAAC,GAAG;gBACpD,wBAAwB;gBACxB,EAAE;gBACF,0BAA0B;gBAC1B,EAAE;gBACF,iBAAiB;gBACjB,GAAG;gBACH,gCAAgC;gBAChC,GAAG;aACN,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEb,MAAM,UAAU,GAAG;gBACf,MAAM,EAAE,qBAAqB;gBAC7B,QAAQ,EAAE,qBAAqB;aAClC,CAAC;YAEF,MAAM,OAAO,GAAG;gBACZ,UAAU,EAAE,CAAC,UAAU,CAAC;gBACxB,QAAQ,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,CAAC;gBACnD,QAAQ,EAAE,IAAI,KAAK,EAAU;gBAC7B,iBAAiB,EAAE,KAAK;aAC3B,CAAC;YAEF,SAAS,CAAC,gBAAgB,GAAG,IAAI,cAAc,CAAC,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YACnG,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;SACtF;QAED,OAAO,SAAS,CAAC,gBAAgB,CAAC;IACtC,CAAC;;AAED,4BAA4B;AACb,0BAAgB,GAA6B,IAAI,CAAC", "sourcesContent": ["import type { IGLTFTechniqueParameter, IGLTFAccessor, IGLTFRuntime, IGLTFBufferView } from \"./glTFLoaderInterfaces\";\r\nimport { EParameterType, ETextureWrapMode, ETextureFilterType, EComponentType } from \"./glTFLoaderInterfaces\";\r\n\r\nimport type { Nullable } from \"core/types\";\r\nimport { Vector2, Vector3, Vector4, Matrix } from \"core/Maths/math.vector\";\r\nimport { Color4 } from \"core/Maths/math.color\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport { ShaderMaterial } from \"core/Materials/shaderMaterial\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { Node } from \"core/node\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * Utils functions for GLTF\r\n * @internal\r\n * @deprecated\r\n */\r\nexport class GLTFUtils {\r\n    /**\r\n     * Sets the given \"parameter\" matrix\r\n     * @param scene the Scene object\r\n     * @param source the source node where to pick the matrix\r\n     * @param parameter the GLTF technique parameter\r\n     * @param uniformName the name of the shader's uniform\r\n     * @param shaderMaterial the shader material\r\n     */\r\n    public static SetMatrix(scene: Scene, source: Node, parameter: IGLTFTechniqueParameter, uniformName: string, shaderMaterial: ShaderMaterial | Effect): void {\r\n        let mat: Nullable<Matrix> = null;\r\n\r\n        if (parameter.semantic === \"MODEL\") {\r\n            mat = source.getWorldMatrix();\r\n        } else if (parameter.semantic === \"PROJECTION\") {\r\n            mat = scene.getProjectionMatrix();\r\n        } else if (parameter.semantic === \"VIEW\") {\r\n            mat = scene.getViewMatrix();\r\n        } else if (parameter.semantic === \"MODELVIEWINVERSETRANSPOSE\") {\r\n            mat = Matrix.Transpose(source.getWorldMatrix().multiply(scene.getViewMatrix()).invert());\r\n        } else if (parameter.semantic === \"MODELVIEW\") {\r\n            mat = source.getWorldMatrix().multiply(scene.getViewMatrix());\r\n        } else if (parameter.semantic === \"MODELVIEWPROJECTION\") {\r\n            mat = source.getWorldMatrix().multiply(scene.getTransformMatrix());\r\n        } else if (parameter.semantic === \"MODELINVERSE\") {\r\n            mat = source.getWorldMatrix().invert();\r\n        } else if (parameter.semantic === \"VIEWINVERSE\") {\r\n            mat = scene.getViewMatrix().invert();\r\n        } else if (parameter.semantic === \"PROJECTIONINVERSE\") {\r\n            mat = scene.getProjectionMatrix().invert();\r\n        } else if (parameter.semantic === \"MODELVIEWINVERSE\") {\r\n            mat = source.getWorldMatrix().multiply(scene.getViewMatrix()).invert();\r\n        } else if (parameter.semantic === \"MODELVIEWPROJECTIONINVERSE\") {\r\n            mat = source.getWorldMatrix().multiply(scene.getTransformMatrix()).invert();\r\n        } else if (parameter.semantic === \"MODELINVERSETRANSPOSE\") {\r\n            mat = Matrix.Transpose(source.getWorldMatrix().invert());\r\n        }\r\n\r\n        if (mat) {\r\n            switch (parameter.type) {\r\n                case EParameterType.FLOAT_MAT2:\r\n                    shaderMaterial.setMatrix2x2(uniformName, Matrix.GetAsMatrix2x2(mat));\r\n                    break;\r\n                case EParameterType.FLOAT_MAT3:\r\n                    shaderMaterial.setMatrix3x3(uniformName, Matrix.GetAsMatrix3x3(mat));\r\n                    break;\r\n                case EParameterType.FLOAT_MAT4:\r\n                    shaderMaterial.setMatrix(uniformName, mat);\r\n                    break;\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the given \"parameter\" matrix\r\n     * @param shaderMaterial the shader material\r\n     * @param uniform the name of the shader's uniform\r\n     * @param value the value of the uniform\r\n     * @param type the uniform's type (EParameterType FLOAT, VEC2, VEC3 or VEC4)\r\n     */\r\n    public static SetUniform(shaderMaterial: ShaderMaterial | Effect, uniform: string, value: any, type: number): boolean {\r\n        switch (type) {\r\n            case EParameterType.FLOAT:\r\n                shaderMaterial.setFloat(uniform, value);\r\n                return true;\r\n            case EParameterType.FLOAT_VEC2:\r\n                shaderMaterial.setVector2(uniform, Vector2.FromArray(value));\r\n                return true;\r\n            case EParameterType.FLOAT_VEC3:\r\n                shaderMaterial.setVector3(uniform, Vector3.FromArray(value));\r\n                return true;\r\n            case EParameterType.FLOAT_VEC4:\r\n                shaderMaterial.setVector4(uniform, Vector4.FromArray(value));\r\n                return true;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the wrap mode of the texture\r\n     * @param mode the mode value\r\n     */\r\n    public static GetWrapMode(mode: number): number {\r\n        switch (mode) {\r\n            case ETextureWrapMode.CLAMP_TO_EDGE:\r\n                return Texture.CLAMP_ADDRESSMODE;\r\n            case ETextureWrapMode.MIRRORED_REPEAT:\r\n                return Texture.MIRROR_ADDRESSMODE;\r\n            case ETextureWrapMode.REPEAT:\r\n                return Texture.WRAP_ADDRESSMODE;\r\n            default:\r\n                return Texture.WRAP_ADDRESSMODE;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the byte stride giving an accessor\r\n     * @param accessor the GLTF accessor objet\r\n     */\r\n    public static GetByteStrideFromType(accessor: IGLTFAccessor): number {\r\n        // Needs this function since \"byteStride\" isn't requiered in glTF format\r\n        const type = accessor.type;\r\n\r\n        switch (type) {\r\n            case \"VEC2\":\r\n                return 2;\r\n            case \"VEC3\":\r\n                return 3;\r\n            case \"VEC4\":\r\n                return 4;\r\n            case \"MAT2\":\r\n                return 4;\r\n            case \"MAT3\":\r\n                return 9;\r\n            case \"MAT4\":\r\n                return 16;\r\n            default:\r\n                return 1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the texture filter mode giving a mode value\r\n     * @param mode the filter mode value\r\n     */\r\n    public static GetTextureFilterMode(mode: number): ETextureFilterType {\r\n        switch (mode) {\r\n            case ETextureFilterType.LINEAR:\r\n            case ETextureFilterType.LINEAR_MIPMAP_NEAREST:\r\n            case ETextureFilterType.LINEAR_MIPMAP_LINEAR:\r\n                return Texture.TRILINEAR_SAMPLINGMODE;\r\n            case ETextureFilterType.NEAREST:\r\n            case ETextureFilterType.NEAREST_MIPMAP_NEAREST:\r\n                return Texture.NEAREST_SAMPLINGMODE;\r\n            default:\r\n                return Texture.BILINEAR_SAMPLINGMODE;\r\n        }\r\n    }\r\n\r\n    public static GetBufferFromBufferView(\r\n        gltfRuntime: IGLTFRuntime,\r\n        bufferView: IGLTFBufferView,\r\n        byteOffset: number,\r\n        byteLength: number,\r\n        componentType: EComponentType\r\n    ): ArrayBufferView {\r\n        byteOffset = bufferView.byteOffset + byteOffset;\r\n\r\n        const loadedBufferView = gltfRuntime.loadedBufferViews[bufferView.buffer];\r\n        if (byteOffset + byteLength > loadedBufferView.byteLength) {\r\n            throw new Error(\"Buffer access is out of range\");\r\n        }\r\n\r\n        const buffer = loadedBufferView.buffer;\r\n        byteOffset += loadedBufferView.byteOffset;\r\n\r\n        switch (componentType) {\r\n            case EComponentType.BYTE:\r\n                return new Int8Array(buffer, byteOffset, byteLength);\r\n            case EComponentType.UNSIGNED_BYTE:\r\n                return new Uint8Array(buffer, byteOffset, byteLength);\r\n            case EComponentType.SHORT:\r\n                return new Int16Array(buffer, byteOffset, byteLength);\r\n            case EComponentType.UNSIGNED_SHORT:\r\n                return new Uint16Array(buffer, byteOffset, byteLength);\r\n            default:\r\n                return new Float32Array(buffer, byteOffset, byteLength);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns a buffer from its accessor\r\n     * @param gltfRuntime the GLTF runtime\r\n     * @param accessor the GLTF accessor\r\n     */\r\n    public static GetBufferFromAccessor(gltfRuntime: IGLTFRuntime, accessor: IGLTFAccessor): any {\r\n        const bufferView: IGLTFBufferView = gltfRuntime.bufferViews[accessor.bufferView];\r\n        const byteLength = accessor.count * GLTFUtils.GetByteStrideFromType(accessor);\r\n        return GLTFUtils.GetBufferFromBufferView(gltfRuntime, bufferView, accessor.byteOffset, byteLength, accessor.componentType);\r\n    }\r\n\r\n    /**\r\n     * Decodes a buffer view into a string\r\n     * @param view the buffer view\r\n     */\r\n    public static DecodeBufferToText(view: ArrayBufferView): string {\r\n        let result = \"\";\r\n        const length = view.byteLength;\r\n\r\n        for (let i = 0; i < length; ++i) {\r\n            result += String.fromCharCode((<any>view)[i]);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Returns the default material of gltf. Related to\r\n     * https://github.com/KhronosGroup/glTF/tree/master/specification/1.0#appendix-a-default-material\r\n     * @param scene the Babylon.js scene\r\n     */\r\n    public static GetDefaultMaterial(scene: Scene): ShaderMaterial {\r\n        if (!GLTFUtils._DefaultMaterial) {\r\n            Effect.ShadersStore[\"GLTFDefaultMaterialVertexShader\"] = [\r\n                \"precision highp float;\",\r\n                \"\",\r\n                \"uniform mat4 worldView;\",\r\n                \"uniform mat4 projection;\",\r\n                \"\",\r\n                \"attribute vec3 position;\",\r\n                \"\",\r\n                \"void main(void)\",\r\n                \"{\",\r\n                \"    gl_Position = projection * worldView * vec4(position, 1.0);\",\r\n                \"}\",\r\n            ].join(\"\\n\");\r\n\r\n            Effect.ShadersStore[\"GLTFDefaultMaterialPixelShader\"] = [\r\n                \"precision highp float;\",\r\n                \"\",\r\n                \"uniform vec4 u_emission;\",\r\n                \"\",\r\n                \"void main(void)\",\r\n                \"{\",\r\n                \"    gl_FragColor = u_emission;\",\r\n                \"}\",\r\n            ].join(\"\\n\");\r\n\r\n            const shaderPath = {\r\n                vertex: \"GLTFDefaultMaterial\",\r\n                fragment: \"GLTFDefaultMaterial\",\r\n            };\r\n\r\n            const options = {\r\n                attributes: [\"position\"],\r\n                uniforms: [\"worldView\", \"projection\", \"u_emission\"],\r\n                samplers: new Array<string>(),\r\n                needAlphaBlending: false,\r\n            };\r\n\r\n            GLTFUtils._DefaultMaterial = new ShaderMaterial(\"GLTFDefaultMaterial\", scene, shaderPath, options);\r\n            GLTFUtils._DefaultMaterial.setColor4(\"u_emission\", new Color4(0.5, 0.5, 0.5, 1.0));\r\n        }\r\n\r\n        return GLTFUtils._DefaultMaterial;\r\n    }\r\n\r\n    // The GLTF default material\r\n    private static _DefaultMaterial: Nullable<ShaderMaterial> = null;\r\n}\r\n"]}