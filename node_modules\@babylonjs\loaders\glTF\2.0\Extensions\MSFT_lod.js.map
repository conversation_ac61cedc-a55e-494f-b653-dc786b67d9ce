{"version": 3, "file": "MSFT_lod.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/MSFT_lod.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,2CAA6B;AAClD,OAAO,EAAE,QAAQ,EAAE,yCAA2B;AAO9C,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAGtD,MAAM,IAAI,GAAG,UAAU,CAAC;AAQxB;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,QAAQ;IAiDjB;;OAEG;IACH,YAAY,MAAkB;QAnD9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAEnB;;WAEG;QACI,kBAAa,GAAG,EAAE,CAAC;QAE1B;;;;WAIG;QACI,+BAA0B,GAAG,IAAI,UAAU,EAAU,CAAC;QAE7D;;;;WAIG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAU,CAAC;QAIzD,gBAAW,GAAG,IAAI,KAAK,EAAe,CAAC;QAEvC,kBAAa,GAAqB,IAAI,CAAC;QACvC,oBAAe,GAAG,IAAI,KAAK,EAAkB,CAAC;QAC9C,qBAAgB,GAAG,IAAI,KAAK,EAAuB,CAAC;QACpD,oBAAe,GAAG,IAAI,KAAK,EAAe,CAAC;QAE3C,sBAAiB,GAAqB,IAAI,CAAC;QAC3C,wBAAmB,GAAG,IAAI,KAAK,EAAkB,CAAC;QAClD,yBAAoB,GAAG,IAAI,KAAK,EAAuB,CAAC;QACxD,wBAAmB,GAAG,IAAI,KAAK,EAAe,CAAC;QAMnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;QAE7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;IAC5C,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YACxE,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACnE,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,YAAY,QAAQ,EAAE,CAAC,CAAC;oBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;iBACnD;gBAED,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAE1D,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/C,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,YAAY,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;oBACjE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;oBACxD,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE;wBAChC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;qBAC5C;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChD;QAED,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;YAC5E,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACvE,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,gBAAgB,QAAQ,EAAE,CAAC,CAAC;oBAC/D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,CAAC,CAAC;iBACvD;gBAED,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAE9D,IAAI,QAAQ,KAAK,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACnD,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,gBAAgB,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;oBACrE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;oBAC5D,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;wBACpC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;qBAChD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,OAAe,EAAE,KAAa;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QACzC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe,EAAE,IAAW,EAAE,MAAqD;QACpG,OAAO,UAAU,CAAC,kBAAkB,CAA0B,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACpH,IAAI,YAAoC,CAAC;YAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;YAC/F,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC;YAE5C,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBAC3D,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;oBAC9B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;iBACrF;gBAED,MAAM,UAAU,GAAG,CAAC,oBAAmC,EAAE,EAAE;oBACvD,MAAM,CAAC,oBAAoB,CAAC,CAAC;oBAC7B,oBAAoB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC3C,CAAC,CAAC;gBAEF,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;oBAC5G,IAAI,QAAQ,KAAK,CAAC,EAAE;wBAChB,iDAAiD;wBACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;wBAC/C,IAAI,eAAe,CAAC,qBAAqB,EAAE;4BACvC,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;4BAClE,OAAO,eAAe,CAAC,qBAAqB,CAAC;yBAChD;qBACJ;oBAED,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC7B,OAAO,WAAW,CAAC;gBACvB,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAExE,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,YAAY,GAAG,OAAO,CAAC;iBAC1B;qBAAM;oBACH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACjD;aACJ;YAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,YAAa,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,kBAAkB,CACrB,OAAe,EACf,QAAmB,EACnB,WAA2B,EAC3B,eAAuB,EACvB,MAA2C;QAE3C,0DAA0D;QAC1D,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,OAAO,IAAI,CAAC;SACf;QAED,OAAO,UAAU,CAAC,kBAAkB,CAAqB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACnH,IAAI,YAA+B,CAAC;YAEpC,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;YAC3G,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,gBAAgB,EAAE,CAAC,CAAC;YAE5C,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBAC/D,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAE3C,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;iBACrC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;qBACvB,kBAAkB,CAAC,cAAc,WAAW,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,eAAe,EAAE,EAAE;oBAClH,IAAI,QAAQ,KAAK,CAAC,EAAE;wBAChB,MAAM,CAAC,eAAe,CAAC,CAAC;qBAC3B;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,CAAC,eAAe,EAAE,EAAE;oBACtB,IAAI,QAAQ,KAAK,CAAC,EAAE;wBAChB,MAAM,CAAC,eAAe,CAAC,CAAC;wBAExB,iCAAiC;wBACjC,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,KAAM,CAAC;wBAC1D,IAAI,eAAe,CAAC,eAAe,CAAC,EAAE;4BAClC,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC;4BAC3E,OAAO,eAAe,CAAC,eAAe,CAAC,CAAC;yBAC3C;qBACJ;oBAED,OAAO,eAAe,CAAC;gBAC3B,CAAC,CAAC,CAAC;gBAEP,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEhF,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,YAAY,GAAG,OAAO,CAAC;iBAC1B;qBAAM;oBACH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC9B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrD;aACJ;YAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YACxB,OAAO,YAAa,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAe,EAAE,QAAmB,EAAE,GAAW;QAClE,+DAA+D;QAC/D,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;YAChD,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,IAAI,QAAQ,EAAQ,CAAC;YACxG,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;aAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACxC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;YACpD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,IAAI,QAAQ,EAAQ,CAAC;YAChH,OAAO,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBAChE,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;SACN;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAe,EAAE,MAAe,EAAE,UAAkB,EAAE,UAAkB;QAC3F,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;gBACnB,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,iEAAiE,CAAC,CAAC;aAChG;YAED,MAAM,SAAS,GAAG,CAAC,UAA8B,EAAE,QAAgB,EAAE,EAAE;gBACnE,MAAM,KAAK,GAAG,UAAU,CAAC;gBACzB,MAAM,GAAG,GAAG,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;gBACnC,IAAI,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,SAAS,EAAE;oBACX,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBACnD,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;iBAChD;qBAAM;oBACH,SAAS,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,QAAQ,EAAE,EAAE,CAAC;oBAC/D,UAAU,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;iBACpC;gBAED,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC1C,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACnG,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAE7B,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,EAAE;gBAC7B,OAAO,SAAS,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;aAC9D;iBAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;gBACxC,OAAO,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACtE;iBAAM;gBACH,OAAO,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;aACzC;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,cAAc,CAAC,UAA8B,EAAE,QAAgB;QACnE,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,SAAS,EAAE;YACX,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC;YAC/E,IAAI,CAAC,OAAO,CAAC,GAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAClF,CAAC,IAAI,EAAE,EAAE;gBACL,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnC,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;gBACN,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC,CACJ,CAAC;SACL;IACL,CAAC;IAED;;;;;;OAMG;IACK,QAAQ,CAAI,OAAe,EAAE,QAAW,EAAE,KAA+B,EAAE,GAAa;QAC5F,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC9D;QAED,MAAM,UAAU,GAAG,IAAI,KAAK,EAAK,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;YACtC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,EAAE;gBAC1C,OAAO,UAAU,CAAC;aACrB;SACJ;QAED,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,qBAAqB,CAAC,oBAAmC;QAC7D,MAAM,gBAAgB,GAAG,IAAI,KAAK,EAAY,CAAC;QAC/C,MAAM,eAAe,GAAI,oBAA6B,CAAC,QAAQ,CAAC;QAChE,IAAI,eAAe,EAAE;YACjB,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC1C;QACD,KAAK,MAAM,WAAW,IAAI,oBAAoB,CAAC,cAAc,EAAE,EAAE;YAC7D,IAAI,WAAW,CAAC,QAAQ,EAAE;gBACtB,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;aAC/C;SACJ;QAED,oBAAoB,CAAC,OAAO,EAAE,CAAC;QAE/B,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC,CAAC,CAAC;QACnK,IAAI,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;IACtD,CAAC;IAEO,iBAAiB,CAAC,gBAA4B;QAClD,MAAM,eAAe,GAAwC,EAAE,CAAC;QAEhE,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;YAC5C,KAAK,MAAM,cAAc,IAAI,eAAe,CAAC,iBAAiB,EAAE,EAAE;gBAC9D,eAAe,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC;aAC7D;YAED,eAAe,CAAC,OAAO,EAAE,CAAC;SAC7B;QAED,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE;YACpC,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE;gBAC/D,IAAI,eAAe,CAAC,UAAU,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE;oBACvD,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;iBACpC;aACJ;SACJ;QAED,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE;YACpC,eAAe,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;SACvC;IACL,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Observable } from \"core/Misc/observable\";\r\nimport { Deferred } from \"core/Misc/deferred\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { INode, IMaterial, IBuffer, IScene } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\nimport type { IProperty, IMSFTLOD } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"MSFT_lod\";\r\n\r\ninterface IBufferInfo {\r\n    start: number;\r\n    end: number;\r\n    loaded: Deferred<ArrayBufferView>;\r\n}\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Vendor/MSFT_lod/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class MSFT_lod implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 100;\r\n\r\n    /**\r\n     * Maximum number of LODs to load, starting from the lowest LOD.\r\n     */\r\n    public maxLODsToLoad = 10;\r\n\r\n    /**\r\n     * Observable raised when all node LODs of one level are loaded.\r\n     * The event data is the index of the loaded LOD starting from zero.\r\n     * Dispose the loader to cancel the loading of the next level of LODs.\r\n     */\r\n    public onNodeLODsLoadedObservable = new Observable<number>();\r\n\r\n    /**\r\n     * Observable raised when all material LODs of one level are loaded.\r\n     * The event data is the index of the loaded LOD starting from zero.\r\n     * Dispose the loader to cancel the loading of the next level of LODs.\r\n     */\r\n    public onMaterialLODsLoadedObservable = new Observable<number>();\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    private _bufferLODs = new Array<IBufferInfo>();\r\n\r\n    private _nodeIndexLOD: Nullable<number> = null;\r\n    private _nodeSignalLODs = new Array<Deferred<void>>();\r\n    private _nodePromiseLODs = new Array<Array<Promise<any>>>();\r\n    private _nodeBufferLODs = new Array<IBufferInfo>();\r\n\r\n    private _materialIndexLOD: Nullable<number> = null;\r\n    private _materialSignalLODs = new Array<Deferred<void>>();\r\n    private _materialPromiseLODs = new Array<Array<Promise<any>>>();\r\n    private _materialBufferLODs = new Array<IBufferInfo>();\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n\r\n        this._nodeIndexLOD = null;\r\n        this._nodeSignalLODs.length = 0;\r\n        this._nodePromiseLODs.length = 0;\r\n        this._nodeBufferLODs.length = 0;\r\n\r\n        this._materialIndexLOD = null;\r\n        this._materialSignalLODs.length = 0;\r\n        this._materialPromiseLODs.length = 0;\r\n        this._materialBufferLODs.length = 0;\r\n\r\n        this.onMaterialLODsLoadedObservable.clear();\r\n        this.onNodeLODsLoadedObservable.clear();\r\n    }\r\n\r\n    /** @internal */\r\n    public onReady(): void {\r\n        for (let indexLOD = 0; indexLOD < this._nodePromiseLODs.length; indexLOD++) {\r\n            const promise = Promise.all(this._nodePromiseLODs[indexLOD]).then(() => {\r\n                if (indexLOD !== 0) {\r\n                    this._loader.endPerformanceCounter(`Node LOD ${indexLOD}`);\r\n                    this._loader.log(`Loaded node LOD ${indexLOD}`);\r\n                }\r\n\r\n                this.onNodeLODsLoadedObservable.notifyObservers(indexLOD);\r\n\r\n                if (indexLOD !== this._nodePromiseLODs.length - 1) {\r\n                    this._loader.startPerformanceCounter(`Node LOD ${indexLOD + 1}`);\r\n                    this._loadBufferLOD(this._nodeBufferLODs, indexLOD + 1);\r\n                    if (this._nodeSignalLODs[indexLOD]) {\r\n                        this._nodeSignalLODs[indexLOD].resolve();\r\n                    }\r\n                }\r\n            });\r\n\r\n            this._loader._completePromises.push(promise);\r\n        }\r\n\r\n        for (let indexLOD = 0; indexLOD < this._materialPromiseLODs.length; indexLOD++) {\r\n            const promise = Promise.all(this._materialPromiseLODs[indexLOD]).then(() => {\r\n                if (indexLOD !== 0) {\r\n                    this._loader.endPerformanceCounter(`Material LOD ${indexLOD}`);\r\n                    this._loader.log(`Loaded material LOD ${indexLOD}`);\r\n                }\r\n\r\n                this.onMaterialLODsLoadedObservable.notifyObservers(indexLOD);\r\n\r\n                if (indexLOD !== this._materialPromiseLODs.length - 1) {\r\n                    this._loader.startPerformanceCounter(`Material LOD ${indexLOD + 1}`);\r\n                    this._loadBufferLOD(this._materialBufferLODs, indexLOD + 1);\r\n                    if (this._materialSignalLODs[indexLOD]) {\r\n                        this._materialSignalLODs[indexLOD].resolve();\r\n                    }\r\n                }\r\n            });\r\n\r\n            this._loader._completePromises.push(promise);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadSceneAsync(context: string, scene: IScene): Nullable<Promise<void>> {\r\n        const promise = this._loader.loadSceneAsync(context, scene);\r\n        this._loadBufferLOD(this._bufferLODs, 0);\r\n        return promise;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadNodeAsync(context: string, node: INode, assign: (babylonTransformNode: TransformNode) => void): Nullable<Promise<TransformNode>> {\r\n        return GLTFLoader.LoadExtensionAsync<IMSFTLOD, TransformNode>(context, node, this.name, (extensionContext, extension) => {\r\n            let firstPromise: Promise<TransformNode>;\r\n\r\n            const nodeLODs = this._getLODs(extensionContext, node, this._loader.gltf.nodes, extension.ids);\r\n            this._loader.logOpen(`${extensionContext}`);\r\n\r\n            for (let indexLOD = 0; indexLOD < nodeLODs.length; indexLOD++) {\r\n                const nodeLOD = nodeLODs[indexLOD];\r\n\r\n                if (indexLOD !== 0) {\r\n                    this._nodeIndexLOD = indexLOD;\r\n                    this._nodeSignalLODs[indexLOD] = this._nodeSignalLODs[indexLOD] || new Deferred();\r\n                }\r\n\r\n                const assignWrap = (babylonTransformNode: TransformNode) => {\r\n                    assign(babylonTransformNode);\r\n                    babylonTransformNode.setEnabled(false);\r\n                };\r\n\r\n                const promise = this._loader.loadNodeAsync(`/nodes/${nodeLOD.index}`, nodeLOD, assignWrap).then((babylonMesh) => {\r\n                    if (indexLOD !== 0) {\r\n                        // TODO: should not rely on _babylonTransformNode\r\n                        const previousNodeLOD = nodeLODs[indexLOD - 1];\r\n                        if (previousNodeLOD._babylonTransformNode) {\r\n                            this._disposeTransformNode(previousNodeLOD._babylonTransformNode);\r\n                            delete previousNodeLOD._babylonTransformNode;\r\n                        }\r\n                    }\r\n\r\n                    babylonMesh.setEnabled(true);\r\n                    return babylonMesh;\r\n                });\r\n\r\n                this._nodePromiseLODs[indexLOD] = this._nodePromiseLODs[indexLOD] || [];\r\n\r\n                if (indexLOD === 0) {\r\n                    firstPromise = promise;\r\n                } else {\r\n                    this._nodeIndexLOD = null;\r\n                    this._nodePromiseLODs[indexLOD].push(promise);\r\n                }\r\n            }\r\n\r\n            this._loader.logClose();\r\n            return firstPromise!;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadMaterialAsync(\r\n        context: string,\r\n        material: IMaterial,\r\n        babylonMesh: Nullable<Mesh>,\r\n        babylonDrawMode: number,\r\n        assign: (babylonMaterial: Material) => void\r\n    ): Nullable<Promise<Material>> {\r\n        // Don't load material LODs if already loading a node LOD.\r\n        if (this._nodeIndexLOD) {\r\n            return null;\r\n        }\r\n\r\n        return GLTFLoader.LoadExtensionAsync<IMSFTLOD, Material>(context, material, this.name, (extensionContext, extension) => {\r\n            let firstPromise: Promise<Material>;\r\n\r\n            const materialLODs = this._getLODs(extensionContext, material, this._loader.gltf.materials, extension.ids);\r\n            this._loader.logOpen(`${extensionContext}`);\r\n\r\n            for (let indexLOD = 0; indexLOD < materialLODs.length; indexLOD++) {\r\n                const materialLOD = materialLODs[indexLOD];\r\n\r\n                if (indexLOD !== 0) {\r\n                    this._materialIndexLOD = indexLOD;\r\n                }\r\n\r\n                const promise = this._loader\r\n                    ._loadMaterialAsync(`/materials/${materialLOD.index}`, materialLOD, babylonMesh, babylonDrawMode, (babylonMaterial) => {\r\n                        if (indexLOD === 0) {\r\n                            assign(babylonMaterial);\r\n                        }\r\n                    })\r\n                    .then((babylonMaterial) => {\r\n                        if (indexLOD !== 0) {\r\n                            assign(babylonMaterial);\r\n\r\n                            // TODO: should not rely on _data\r\n                            const previousDataLOD = materialLODs[indexLOD - 1]._data!;\r\n                            if (previousDataLOD[babylonDrawMode]) {\r\n                                this._disposeMaterials([previousDataLOD[babylonDrawMode].babylonMaterial]);\r\n                                delete previousDataLOD[babylonDrawMode];\r\n                            }\r\n                        }\r\n\r\n                        return babylonMaterial;\r\n                    });\r\n\r\n                this._materialPromiseLODs[indexLOD] = this._materialPromiseLODs[indexLOD] || [];\r\n\r\n                if (indexLOD === 0) {\r\n                    firstPromise = promise;\r\n                } else {\r\n                    this._materialIndexLOD = null;\r\n                    this._materialPromiseLODs[indexLOD].push(promise);\r\n                }\r\n            }\r\n\r\n            this._loader.logClose();\r\n            return firstPromise!;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadUriAsync(context: string, property: IProperty, uri: string): Nullable<Promise<ArrayBufferView>> {\r\n        // Defer the loading of uris if loading a node or material LOD.\r\n        if (this._nodeIndexLOD !== null) {\r\n            this._loader.log(`deferred`);\r\n            const previousIndexLOD = this._nodeIndexLOD - 1;\r\n            this._nodeSignalLODs[previousIndexLOD] = this._nodeSignalLODs[previousIndexLOD] || new Deferred<void>();\r\n            return this._nodeSignalLODs[this._nodeIndexLOD - 1].promise.then(() => {\r\n                return this._loader.loadUriAsync(context, property, uri);\r\n            });\r\n        } else if (this._materialIndexLOD !== null) {\r\n            this._loader.log(`deferred`);\r\n            const previousIndexLOD = this._materialIndexLOD - 1;\r\n            this._materialSignalLODs[previousIndexLOD] = this._materialSignalLODs[previousIndexLOD] || new Deferred<void>();\r\n            return this._materialSignalLODs[previousIndexLOD].promise.then(() => {\r\n                return this._loader.loadUriAsync(context, property, uri);\r\n            });\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadBufferAsync(context: string, buffer: IBuffer, byteOffset: number, byteLength: number): Nullable<Promise<ArrayBufferView>> {\r\n        if (this._loader.parent.useRangeRequests && !buffer.uri) {\r\n            if (!this._loader.bin) {\r\n                throw new Error(`${context}: Uri is missing or the binary glTF is missing its binary chunk`);\r\n            }\r\n\r\n            const loadAsync = (bufferLODs: Array<IBufferInfo>, indexLOD: number) => {\r\n                const start = byteOffset;\r\n                const end = start + byteLength - 1;\r\n                let bufferLOD = bufferLODs[indexLOD];\r\n                if (bufferLOD) {\r\n                    bufferLOD.start = Math.min(bufferLOD.start, start);\r\n                    bufferLOD.end = Math.max(bufferLOD.end, end);\r\n                } else {\r\n                    bufferLOD = { start: start, end: end, loaded: new Deferred() };\r\n                    bufferLODs[indexLOD] = bufferLOD;\r\n                }\r\n\r\n                return bufferLOD.loaded.promise.then((data) => {\r\n                    return new Uint8Array(data.buffer, data.byteOffset + byteOffset - bufferLOD.start, byteLength);\r\n                });\r\n            };\r\n\r\n            this._loader.log(`deferred`);\r\n\r\n            if (this._nodeIndexLOD !== null) {\r\n                return loadAsync(this._nodeBufferLODs, this._nodeIndexLOD);\r\n            } else if (this._materialIndexLOD !== null) {\r\n                return loadAsync(this._materialBufferLODs, this._materialIndexLOD);\r\n            } else {\r\n                return loadAsync(this._bufferLODs, 0);\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _loadBufferLOD(bufferLODs: Array<IBufferInfo>, indexLOD: number): void {\r\n        const bufferLOD = bufferLODs[indexLOD];\r\n        if (bufferLOD) {\r\n            this._loader.log(`Loading buffer range [${bufferLOD.start}-${bufferLOD.end}]`);\r\n            this._loader.bin!.readAsync(bufferLOD.start, bufferLOD.end - bufferLOD.start + 1).then(\r\n                (data) => {\r\n                    bufferLOD.loaded.resolve(data);\r\n                },\r\n                (error) => {\r\n                    bufferLOD.loaded.reject(error);\r\n                }\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets an array of LOD properties from lowest to highest.\r\n     * @param context\r\n     * @param property\r\n     * @param array\r\n     * @param ids\r\n     */\r\n    private _getLODs<T>(context: string, property: T, array: ArrayLike<T> | undefined, ids: number[]): T[] {\r\n        if (this.maxLODsToLoad <= 0) {\r\n            throw new Error(\"maxLODsToLoad must be greater than zero\");\r\n        }\r\n\r\n        const properties = new Array<T>();\r\n\r\n        for (let i = ids.length - 1; i >= 0; i--) {\r\n            properties.push(ArrayItem.Get(`${context}/ids/${ids[i]}`, array, ids[i]));\r\n            if (properties.length === this.maxLODsToLoad) {\r\n                return properties;\r\n            }\r\n        }\r\n\r\n        properties.push(property);\r\n        return properties;\r\n    }\r\n\r\n    private _disposeTransformNode(babylonTransformNode: TransformNode): void {\r\n        const babylonMaterials = new Array<Material>();\r\n        const babylonMaterial = (babylonTransformNode as Mesh).material;\r\n        if (babylonMaterial) {\r\n            babylonMaterials.push(babylonMaterial);\r\n        }\r\n        for (const babylonMesh of babylonTransformNode.getChildMeshes()) {\r\n            if (babylonMesh.material) {\r\n                babylonMaterials.push(babylonMesh.material);\r\n            }\r\n        }\r\n\r\n        babylonTransformNode.dispose();\r\n\r\n        const babylonMaterialsToDispose = babylonMaterials.filter((babylonMaterial) => this._loader.babylonScene.meshes.every((mesh) => mesh.material != babylonMaterial));\r\n        this._disposeMaterials(babylonMaterialsToDispose);\r\n    }\r\n\r\n    private _disposeMaterials(babylonMaterials: Material[]): void {\r\n        const babylonTextures: { [uniqueId: number]: BaseTexture } = {};\r\n\r\n        for (const babylonMaterial of babylonMaterials) {\r\n            for (const babylonTexture of babylonMaterial.getActiveTextures()) {\r\n                babylonTextures[babylonTexture.uniqueId] = babylonTexture;\r\n            }\r\n\r\n            babylonMaterial.dispose();\r\n        }\r\n\r\n        for (const uniqueId in babylonTextures) {\r\n            for (const babylonMaterial of this._loader.babylonScene.materials) {\r\n                if (babylonMaterial.hasTexture(babylonTextures[uniqueId])) {\r\n                    delete babylonTextures[uniqueId];\r\n                }\r\n            }\r\n        }\r\n\r\n        for (const uniqueId in babylonTextures) {\r\n            babylonTextures[uniqueId].dispose();\r\n        }\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new MSFT_lod(loader));\r\n"]}