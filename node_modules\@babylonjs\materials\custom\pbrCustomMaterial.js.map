{"version": 3, "file": "pbrCustomMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/custom/pbrCustomMaterial.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAG7D,OAAO,EAAE,aAAa,EAAE,0CAA4B;AACpD,OAAO,EAAE,iBAAiB,EAAE,gEAAkD;AAG9E,MAAM,OAAO,iBAAiB;IAC1B,gBAAe,CAAC;CAuCnB;AAED,MAAM,OAAO,iBAAkB,SAAQ,WAAW;IAcvC,eAAe,CAAC,IAAsB,EAAE,MAAc;QACzD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBACxC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACjB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC1D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE;oBACzB,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBACzD;aACJ;SACJ;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBACxC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC1G,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;aACJ;SACJ;IACL,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,GAAa;QAC5C,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACnD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;iBAC5D;aACJ;SACJ;QACD,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACnD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;iBAC5D;aACJ;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAEM,OAAO,CACV,UAAkB,EAClB,QAAkB,EAClB,cAAwB,EACxB,QAAkB,EAClB,OAAmC,EACnC,UAAqB,EACrB,OAAyC;QAEzC,IAAI,OAAO,EAAE;YACT,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;YACnD,OAAO,CAAC,gBAAgB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;gBACtD,IAAI,IAAI,KAAK,QAAQ,EAAE;oBACnB,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;iBACnE;gBACD,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACxC,GAAG,CAAC,WAAW,GAAG,oBAAoB,CAAC;gBACvC,GAAG,CAAC,WAAW,EAAE,CAAC;gBAClB,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;YAC5E,CAAC,CAAC;SACL;QAED,IAAI,UAAU,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAClC,MAAM,IAAI,GAAW,SAAS,GAAG,iBAAiB,CAAC,aAAa,CAAC;QAEjE,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACvB,IAAI,CAAC,CAAC,EAAE;gBACJ,OAAO;aACV;YACD,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,IAAI;gBACA,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACtB;YAAC,OAAO,CAAC,EAAE,GAAE;QAClB,CAAC,CAAC;QAEF,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;aACpK,OAAO,CACJ,mCAAmC,EACnC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CACjJ;aACA,OAAO,CAAC,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;aACvH,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAE,CAAC;aACtJ,OAAO,CAAC,qCAAqC,EAAE,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,CAAC;aAChJ,OAAO,CAAC,gCAAgC,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvH,IAAI,IAAI,CAAC,WAAW,CAAC,6BAA6B,EAAE;YAChD,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC,OAAO,CAC3F,uCAAuC,EACvC,IAAI,CAAC,WAAW,CAAC,6BAA6B,CACjD,CAAC;SACL;QAED,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CACnE,+BAA+B,EAC/B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CACzE;aACI,OAAO,CAAC,oCAAoC,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;aAC7H,OAAO,CACJ,qCAAqC,EACrC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CACrJ;aACA,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;aACxI,OAAO,CAAC,sCAAsC,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;aACrI,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;aACxI,OAAO,CACJ,kDAAkD,EAClD,IAAI,CAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,iCAAiC,CAAC,CAAC,CAAC,EAAE,CAC/G;aACA,OAAO,CAAC,6CAA6C,EAAE,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,4BAA4B,CAAC,CAAC,CAAC,EAAE,CAAC;aAC1J,OAAO,CACJ,sDAAsD,EACtD,IAAI,CAAC,WAAW,CAAC,qCAAqC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,qCAAqC,CAAC,CAAC,CAAC,EAAE,CACvH;aACA,OAAO,CAAC,0CAA0C,EAAE,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,CAAC;aACjJ,OAAO,CAAC,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE7H,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE;YACtC,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,OAAO,CACzF,oCAAoC,EACpC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACvC,CAAC;SACL;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC3C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAE3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,kCAAkC,EAAE,MAAM,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAC5I,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,iCAAiC,EAAE,MAAM,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAC1I,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,0CAA0C,EAAE,MAAM,CAAC,oBAAoB,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAChK,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,IAAY,EAAE,KAAU;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;YAC/B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;aAC/D;iBAAM;gBACG,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;aAC/D;SACJ;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;SAC/B;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,oBAAoB,CAAC,UAAkB;QAC1C,IAAI,CAAC,WAAW,CAAC,oBAAoB,GAAG,UAAU,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,UAAkB;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,sBAAsB,CAAC,UAAkB;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,qBAAqB,CAAC,UAAkB;QAC3C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,sBAAsB,CAAC,UAAkB;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,iCAAiC,CAAC,UAAkB;QACvD,IAAI,CAAC,WAAW,CAAC,iCAAiC,GAAG,UAAU,CAAC;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,UAAkB;QAClD,IAAI,CAAC,WAAW,CAAC,4BAA4B,GAAG,UAAU,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,mBAAmB,CAAC,UAAkB;QACzC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,qCAAqC,CAAC,UAAkB;QAC3D,IAAI,CAAC,WAAW,CAAC,qCAAqC,GAAG,UAAU,CAAC;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,yBAAyB,CAAC,UAAkB;QAC/C,IAAI,CAAC,WAAW,CAAC,yBAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,UAAkB;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,UAAkB;QAClC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,UAAU,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,UAAkB;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,UAAkB;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,6BAA6B,CAAC,UAAkB;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,2BAA2B,CAAC,UAAkB;QACjD,IAAI,CAAC,WAAW,CAAC,2BAA2B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,6BAA6B,CAAC,UAAkB;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC;QAC5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;;AAxSa,+BAAa,GAAG,CAAC,CAAC;AA2SpC,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport type { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { ShaderCodeInliner } from \"core/Engines/Processors/shaderCodeInliner\";\r\nimport type { ICustomShaderNameResolveOptions } from \"core/Materials/material\";\r\n\r\nexport class ShaderAlebdoParts {\r\n    constructor() {}\r\n\r\n    public Fragment_Begin: string;\r\n    public Fragment_Definitions: string;\r\n    public Fragment_MainBegin: string;\r\n    public Fragment_MainEnd: string;\r\n\r\n    // albedoColor\r\n    public Fragment_Custom_Albedo: string;\r\n    // lights\r\n    public Fragment_Before_Lights: string;\r\n    // roughness\r\n    public Fragment_Custom_MetallicRoughness: string;\r\n    // microsurface\r\n    public Fragment_Custom_MicroSurface: string;\r\n    // fog\r\n    public Fragment_Before_Fog: string;\r\n    // alpha\r\n    public Fragment_Custom_Alpha: string;\r\n    // color composition\r\n    public Fragment_Before_FinalColorComposition: string;\r\n    // frag color\r\n    public Fragment_Before_FragColor: string;\r\n\r\n    public Vertex_Begin: string;\r\n    public Vertex_Definitions: string;\r\n    public Vertex_MainBegin: string;\r\n\r\n    // positionUpdated\r\n    public Vertex_Before_PositionUpdated: string;\r\n\r\n    // normalUpdated\r\n    public Vertex_Before_NormalUpdated: string;\r\n\r\n    // worldPosComputed\r\n    public Vertex_After_WorldPosComputed: string;\r\n\r\n    // mainEnd\r\n    public Vertex_MainEnd: string;\r\n}\r\n\r\nexport class PBRCustomMaterial extends PBRMaterial {\r\n    public static ShaderIndexer = 1;\r\n    public CustomParts: ShaderAlebdoParts;\r\n    _isCreatedShader: boolean;\r\n    _createdShaderName: string;\r\n    _customUniform: string[];\r\n    _newUniforms: string[];\r\n    _newUniformInstances: { [name: string]: any };\r\n    _newSamplerInstances: { [name: string]: Texture };\r\n    _customAttributes: string[];\r\n\r\n    public FragmentShader: string;\r\n    public VertexShader: string;\r\n\r\n    public AttachAfterBind(mesh: Mesh | undefined, effect: Effect) {\r\n        if (this._newUniformInstances) {\r\n            for (const el in this._newUniformInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"vec2\") {\r\n                    effect.setVector2(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec3\") {\r\n                    effect.setVector3(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec4\") {\r\n                    effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"mat4\") {\r\n                    effect.setMatrix(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"float\") {\r\n                    effect.setFloat(ea[1], this._newUniformInstances[el]);\r\n                }\r\n            }\r\n        }\r\n        if (this._newSamplerInstances) {\r\n            for (const el in this._newSamplerInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"sampler2D\" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {\r\n                    effect.setTexture(ea[1], this._newSamplerInstances[el]);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public ReviewUniform(name: string, arr: string[]): string[] {\r\n        if (name == \"uniform\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") == -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        if (name == \"sampler\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") != -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        return arr;\r\n    }\r\n\r\n    public Builder(\r\n        shaderName: string,\r\n        uniforms: string[],\r\n        uniformBuffers: string[],\r\n        samplers: string[],\r\n        defines: MaterialDefines | string[],\r\n        attributes?: string[],\r\n        options?: ICustomShaderNameResolveOptions\r\n    ): string {\r\n        if (options) {\r\n            const currentProcessing = options.processFinalCode;\r\n            options.processFinalCode = (type: string, code: string) => {\r\n                if (type === \"vertex\") {\r\n                    return currentProcessing ? currentProcessing(type, code) : code;\r\n                }\r\n                const sci = new ShaderCodeInliner(code);\r\n                sci.inlineToken = \"#define pbr_inline\";\r\n                sci.processCode();\r\n                return currentProcessing ? currentProcessing(type, sci.code) : sci.code;\r\n            };\r\n        }\r\n\r\n        if (attributes && this._customAttributes && this._customAttributes.length > 0) {\r\n            attributes.push(...this._customAttributes);\r\n        }\r\n\r\n        this.ReviewUniform(\"uniform\", uniforms);\r\n        this.ReviewUniform(\"sampler\", samplers);\r\n\r\n        if (this._isCreatedShader) {\r\n            return this._createdShaderName;\r\n        }\r\n        this._isCreatedShader = false;\r\n\r\n        PBRCustomMaterial.ShaderIndexer++;\r\n        const name: string = \"custom_\" + PBRCustomMaterial.ShaderIndexer;\r\n\r\n        const fn_afterBind = this._afterBind.bind(this);\r\n        this._afterBind = (m, e) => {\r\n            if (!e) {\r\n                return;\r\n            }\r\n            this.AttachAfterBind(m, e);\r\n            try {\r\n                fn_afterBind(m, e);\r\n            } catch (e) {}\r\n        };\r\n\r\n        Effect.ShadersStore[name + \"VertexShader\"] = this.VertexShader.replace(\"#define CUSTOM_VERTEX_BEGIN\", this.CustomParts.Vertex_Begin ? this.CustomParts.Vertex_Begin : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_VERTEX_DEFINITIONS\",\r\n                (this._customUniform ? this._customUniform.join(\"\\n\") : \"\") + (this.CustomParts.Vertex_Definitions ? this.CustomParts.Vertex_Definitions : \"\")\r\n            )\r\n            .replace(\"#define CUSTOM_VERTEX_MAIN_BEGIN\", this.CustomParts.Vertex_MainBegin ? this.CustomParts.Vertex_MainBegin : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_UPDATE_POSITION\", this.CustomParts.Vertex_Before_PositionUpdated ? this.CustomParts.Vertex_Before_PositionUpdated : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_UPDATE_NORMAL\", this.CustomParts.Vertex_Before_NormalUpdated ? this.CustomParts.Vertex_Before_NormalUpdated : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_MAIN_END\", this.CustomParts.Vertex_MainEnd ? this.CustomParts.Vertex_MainEnd : \"\");\r\n\r\n        if (this.CustomParts.Vertex_After_WorldPosComputed) {\r\n            Effect.ShadersStore[name + \"VertexShader\"] = Effect.ShadersStore[name + \"VertexShader\"].replace(\r\n                \"#define CUSTOM_VERTEX_UPDATE_WORLDPOS\",\r\n                this.CustomParts.Vertex_After_WorldPosComputed\r\n            );\r\n        }\r\n\r\n        Effect.ShadersStore[name + \"PixelShader\"] = this.FragmentShader.replace(\r\n            \"#define CUSTOM_FRAGMENT_BEGIN\",\r\n            this.CustomParts.Fragment_Begin ? this.CustomParts.Fragment_Begin : \"\"\r\n        )\r\n            .replace(\"#define CUSTOM_FRAGMENT_MAIN_BEGIN\", this.CustomParts.Fragment_MainBegin ? this.CustomParts.Fragment_MainBegin : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_FRAGMENT_DEFINITIONS\",\r\n                (this._customUniform ? this._customUniform.join(\"\\n\") : \"\") + (this.CustomParts.Fragment_Definitions ? this.CustomParts.Fragment_Definitions : \"\")\r\n            )\r\n            .replace(\"#define CUSTOM_FRAGMENT_UPDATE_ALBEDO\", this.CustomParts.Fragment_Custom_Albedo ? this.CustomParts.Fragment_Custom_Albedo : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_UPDATE_ALPHA\", this.CustomParts.Fragment_Custom_Alpha ? this.CustomParts.Fragment_Custom_Alpha : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_BEFORE_LIGHTS\", this.CustomParts.Fragment_Before_Lights ? this.CustomParts.Fragment_Before_Lights : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_FRAGMENT_UPDATE_METALLICROUGHNESS\",\r\n                this.CustomParts.Fragment_Custom_MetallicRoughness ? this.CustomParts.Fragment_Custom_MetallicRoughness : \"\"\r\n            )\r\n            .replace(\"#define CUSTOM_FRAGMENT_UPDATE_MICROSURFACE\", this.CustomParts.Fragment_Custom_MicroSurface ? this.CustomParts.Fragment_Custom_MicroSurface : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION\",\r\n                this.CustomParts.Fragment_Before_FinalColorComposition ? this.CustomParts.Fragment_Before_FinalColorComposition : \"\"\r\n            )\r\n            .replace(\"#define CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR\", this.CustomParts.Fragment_Before_FragColor ? this.CustomParts.Fragment_Before_FragColor : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_MAIN_END\", this.CustomParts.Fragment_MainEnd ? this.CustomParts.Fragment_MainEnd : \"\");\r\n\r\n        if (this.CustomParts.Fragment_Before_Fog) {\r\n            Effect.ShadersStore[name + \"PixelShader\"] = Effect.ShadersStore[name + \"PixelShader\"].replace(\r\n                \"#define CUSTOM_FRAGMENT_BEFORE_FOG\",\r\n                this.CustomParts.Fragment_Before_Fog\r\n            );\r\n        }\r\n\r\n        this._isCreatedShader = true;\r\n        this._createdShaderName = name;\r\n\r\n        return name;\r\n    }\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this.CustomParts = new ShaderAlebdoParts();\r\n        this.customShaderNameResolve = this.Builder;\r\n\r\n        this.FragmentShader = Effect.ShadersStore[\"pbrPixelShader\"];\r\n        this.VertexShader = Effect.ShadersStore[\"pbrVertexShader\"];\r\n\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockAlbedoOpacity>/g, Effect.IncludesShadersStore[\"pbrBlockAlbedoOpacity\"]);\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockReflectivity>/g, Effect.IncludesShadersStore[\"pbrBlockReflectivity\"]);\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockFinalColorComposition>/g, Effect.IncludesShadersStore[\"pbrBlockFinalColorComposition\"]);\r\n    }\r\n\r\n    public AddUniform(name: string, kind: string, param: any): PBRCustomMaterial {\r\n        if (!this._customUniform) {\r\n            this._customUniform = new Array();\r\n            this._newUniforms = new Array();\r\n            this._newSamplerInstances = {};\r\n            this._newUniformInstances = {};\r\n        }\r\n        if (param) {\r\n            if (kind.indexOf(\"sampler\") != -1) {\r\n                (<any>this._newSamplerInstances)[kind + \"-\" + name] = param;\r\n            } else {\r\n                (<any>this._newUniformInstances)[kind + \"-\" + name] = param;\r\n            }\r\n        }\r\n        this._customUniform.push(\"uniform \" + kind + \" \" + name + \";\");\r\n        this._newUniforms.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    public AddAttribute(name: string): PBRCustomMaterial {\r\n        if (!this._customAttributes) {\r\n            this._customAttributes = [];\r\n        }\r\n\r\n        this._customAttributes.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Begin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Definitions(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_MainBegin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_Albedo(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Albedo = shaderPart.replace(\"result\", \"surfaceAlbedo\");\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_Alpha(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace(\"result\", \"alpha\");\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_Lights(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_Lights = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_MetallicRoughness(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_MetallicRoughness = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_MicroSurface(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_MicroSurface = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_Fog(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_Fog = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_FinalColorComposition(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_FinalColorComposition = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_FragColor(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace(\"result\", \"color\");\r\n        return this;\r\n    }\r\n\r\n    public Fragment_MainEnd(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Begin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Definitions(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_MainBegin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Before_PositionUpdated(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace(\"result\", \"positionUpdated\");\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Before_NormalUpdated(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace(\"result\", \"normalUpdated\");\r\n        return this;\r\n    }\r\n\r\n    public Vertex_After_WorldPosComputed(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_MainEnd(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRCustomMaterial\", PBRCustomMaterial);\r\n"]}