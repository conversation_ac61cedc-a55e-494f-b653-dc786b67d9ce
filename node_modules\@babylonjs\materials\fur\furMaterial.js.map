{"version": 3, "file": "furMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/fur/furMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,2CAA6B;AAEnJ,OAAO,EAAE,OAAO,EAAE,6CAA+B;AACjD,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,IAAI,EAAE,qCAAuB;AAEtC,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAC1D,OAAO,EAAE,cAAc,EAAE,6DAA+C;AAExE,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AACpD,OAAO,EAAE,eAAe,EAAE,qDAAuC;AAEjE,OAAO,gBAAgB,CAAC;AACxB,OAAO,cAAc,CAAC;AACtB,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,kBAAmB,SAAQ,eAAe;IA0B5C;QACI,KAAK,EAAE,CAAC;QA1BL,YAAO,GAAG,KAAK,CAAC;QAChB,cAAS,GAAG,KAAK,CAAC;QAClB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,iBAAY,GAAG,KAAK,CAAC;QACrB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,WAAM,GAAG,KAAK,CAAC;QACf,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,cAAS,GAAG,KAAK,CAAC;QAClB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,WAAY,SAAQ,YAAY;IA4DzC,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAjDhB,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGnC,cAAS,GAAW,CAAC,CAAC;QAGtB,aAAQ,GAAW,CAAC,CAAC;QAGrB,aAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAGxC,cAAS,GAAW,GAAG,CAAC;QAGxB,eAAU,GAAW,EAAE,CAAC;QAGxB,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGlC,aAAQ,GAAW,GAAG,CAAC;QAGvB,eAAU,GAAW,EAAE,CAAC;QAGxB,iBAAY,GAAW,GAAG,CAAC;QAK1B,qBAAgB,GAAG,KAAK,CAAC;QAKzB,2BAAsB,GAAG,CAAC,CAAC;QAK5B,iBAAY,GAAY,IAAI,CAAC;QAI5B,aAAQ,GAAW,CAAC,CAAC;IAI7B,CAAC;IAGD,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAAe;QAC9B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAC5B,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,SAAS;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,SAAS,GAAgB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAExD,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC/C,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;YAC3C,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;SAC1C;IACL,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,kBAAkB,EAAE,CAAC;SACtD;QAED,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,WAAW;QACX,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,cAAc,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC5D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE;wBAChC,OAAO,KAAK,CAAC;qBAChB;yBAAM;wBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;qBAC1B;iBACJ;gBACD,IAAI,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,0BAA0B,EAAE;oBACnE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE;wBAC/B,OAAO,KAAK,CAAC;qBAChB;yBAAM;wBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;qBAC5B;iBACJ;aACJ;SACJ;QAED,aAAa;QACb,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,CAAC,SAAS,EAAE;YACzC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,iBAAiB,EAAE,CAAC;SAC/B;QAED,QAAQ;QACR,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAExI,SAAS;QACT,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/I,kDAAkD;QAClD,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEtE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAEzF,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,uBAAuB;YACvB,MAAM,UAAU,GAAG,KAAK,CAAC;YACzB,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,eAAe;gBACf,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,eAAe;gBACf,QAAQ;gBACR,eAAe;gBACf,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,cAAc;aACjB,CAAC;YACF,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/B,MAAM,QAAQ,GAAG,CAAC,gBAAgB,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAEnE,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;YAE3C,cAAc,CAAC,8BAA8B,CAAyB;gBAClE,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;aACpD,CAAC,CAAC;YAEH,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACzE,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,KAAK,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;YACpC,WAAW;YACX,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAEtE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACjH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC1F;YAED,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;aACvE;YAED,aAAa;YACb,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/C,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/F,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACnG;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC3E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAE/D,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5E,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAEtD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;SAChE;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,cAAc;QACjB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACpG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACrC;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACjG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACpC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC5C;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YACjC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE;YACjC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,OAAO,CAAC,kBAA4B;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SACjC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAErC,IAAI,GAAG,EAAE;oBACL,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;iBACnC;gBACD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aAC7B;SACJ;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACzF,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,qBAAqB,CAAC;QAEvD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1D,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SACrD;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,UAAU;IACH,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE9G,IAAI,MAAM,CAAC,cAAc,IAAI,QAAQ,CAAC,YAAY,EAAE;YAChD,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACxB,MAAM,UAAU,GAAS,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBACpE,IAAI,UAAU,EAAE;oBACZ,MAAM,UAAU,GAAG,WAAW,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;oBACrE,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;oBACjC,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;iBACtD;YACL,CAAC,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEM,MAAM,CAAC,eAAe,CAAC,IAAY,EAAE,KAAY;QACpD,wBAAwB;QACxB,MAAM,OAAO,GAAG,IAAI,cAAc,CAAC,aAAa,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;YAC5B,OAAO,CAAC,SAAS,GAAG,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC;YACrH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC7G;QAED,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACtB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAEzC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,6EAA6E;IAC7E,0CAA0C;IAC1C,sCAAsC;IAC/B,MAAM,CAAC,UAAU,CAAC,UAAgB,EAAE,OAAe;QACtD,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC;QAC5B,MAAM,GAAG,GAA6B,UAAU,CAAC,QAAQ,CAAC;QAC1D,IAAI,CAAC,CAAC;QAEN,IAAI,CAAC,CAAC,GAAG,YAAY,WAAW,CAAC,EAAE;YAC/B,MAAM,wDAAwD,CAAC;SAClE;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,EAAE,EAAE;YAC1B,MAAM,SAAS,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YACvE,UAAU,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;YAE9C,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;YACpC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAClC,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YACtC,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YACtC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAClC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;YAClC,SAAS,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,CAAC;YAC9C,SAAS,CAAC,SAAS,GAAG,CAAC,GAAG,OAAO,CAAC;YAClC,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YACtC,SAAS,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC;YAC1C,SAAS,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;YAChC,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;YAEtC,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,CAAS,CAAC;YAEjE,UAAU,CAAC,QAAQ,GAAG,SAAS,CAAC;YAChC,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YAC1C,UAAU,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YACrC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC3B;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC;SACjC;QAEa,UAAU,CAAC,QAAS,CAAC,OAAO,GAAG,MAAM,CAAC;QAEpD,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAtgBG;IADC,kBAAkB,CAAC,gBAAgB,CAAC;oDACA;AAErC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;mDAClB;AAGnC;IADC,kBAAkB,CAAC,eAAe,CAAC;mDACA;AAEpC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;kDACnB;AAGlC;IADC,iBAAiB,EAAE;iDACsB;AAG1C;IADC,SAAS,EAAE;8CACiB;AAG7B;IADC,SAAS,EAAE;6CACgB;AAG5B;IADC,iBAAiB,EAAE;6CAC2B;AAG/C;IADC,SAAS,EAAE;8CACmB;AAG/B;IADC,SAAS,EAAE;+CACmB;AAG/B;IADC,kBAAkB,EAAE;+CACoB;AAGzC;IADC,SAAS,EAAE;6CACkB;AAG9B;IADC,SAAS,EAAE;+CACmB;AAG/B;IADC,SAAS,EAAE;iDACsB;AAKlC;IADC,SAAS,CAAC,iBAAiB,CAAC;qDACI;AAEjC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;oDACnB;AAGhC;IADC,SAAS,CAAC,uBAAuB,CAAC;2DACA;AAEnC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;0DACd;AAGrC;IADC,SAAS,EAAE;iDACwB;AAWpC;IADC,SAAS,EAAE;0CAGX;AAucL,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { serializeAsVector3, serializeAsTexture, serialize, expandToProperty, serializeAsColor3, SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport { Tags } from \"core/Misc/tags\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport { DynamicTexture } from \"core/Materials/Textures/dynamicTexture\";\r\nimport type { IEffectCreationOptions } from \"core/Materials/effect\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { MaterialFlags } from \"core/Materials/materialFlags\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\n\r\nimport \"./fur.fragment\";\r\nimport \"./fur.vertex\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass FurMaterialDefines extends MaterialDefines {\r\n    public DIFFUSE = false;\r\n    public HEIGHTMAP = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public NORMAL = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public HIGHLEVEL = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class FurMaterial extends PushMaterial {\r\n    @serializeAsTexture(\"diffuseTexture\")\r\n    private _diffuseTexture: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture: BaseTexture;\r\n\r\n    @serializeAsTexture(\"heightTexture\")\r\n    private _heightTexture: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public heightTexture: BaseTexture;\r\n\r\n    @serializeAsColor3()\r\n    public diffuseColor = new Color3(1, 1, 1);\r\n\r\n    @serialize()\r\n    public furLength: number = 1;\r\n\r\n    @serialize()\r\n    public furAngle: number = 0;\r\n\r\n    @serializeAsColor3()\r\n    public furColor = new Color3(0.44, 0.21, 0.02);\r\n\r\n    @serialize()\r\n    public furOffset: number = 0.0;\r\n\r\n    @serialize()\r\n    public furSpacing: number = 12;\r\n\r\n    @serializeAsVector3()\r\n    public furGravity = new Vector3(0, 0, 0);\r\n\r\n    @serialize()\r\n    public furSpeed: number = 100;\r\n\r\n    @serialize()\r\n    public furDensity: number = 20;\r\n\r\n    @serialize()\r\n    public furOcclusion: number = 0.0;\r\n\r\n    public furTexture: DynamicTexture;\r\n\r\n    @serialize(\"disableLighting\")\r\n    private _disableLighting = false;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting: boolean;\r\n\r\n    @serialize(\"maxSimultaneousLights\")\r\n    private _maxSimultaneousLights = 4;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights: number;\r\n\r\n    @serialize()\r\n    public highLevelFur: boolean = true;\r\n\r\n    public _meshes: AbstractMesh[];\r\n\r\n    private _furTime: number = 0;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    @serialize()\r\n    public get furTime() {\r\n        return this._furTime;\r\n    }\r\n\r\n    public set furTime(furTime: number) {\r\n        this._furTime = furTime;\r\n    }\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return this.alpha < 1.0;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    public updateFur(): void {\r\n        for (let i = 1; i < this._meshes.length; i++) {\r\n            const offsetFur = <FurMaterial>this._meshes[i].material;\r\n\r\n            offsetFur.furLength = this.furLength;\r\n            offsetFur.furAngle = this.furAngle;\r\n            offsetFur.furGravity = this.furGravity;\r\n            offsetFur.furSpacing = this.furSpacing;\r\n            offsetFur.furSpeed = this.furSpeed;\r\n            offsetFur.furColor = this.furColor;\r\n            offsetFur.diffuseTexture = this.diffuseTexture;\r\n            offsetFur.furTexture = this.furTexture;\r\n            offsetFur.highLevelFur = this.highLevelFur;\r\n            offsetFur.furTime = this.furTime;\r\n            offsetFur.furDensity = this.furDensity;\r\n        }\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new FurMaterialDefines();\r\n        }\r\n\r\n        const defines = <FurMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Textures\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this.diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (!this.diffuseTexture.isReady()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needUVs = true;\r\n                        defines.DIFFUSE = true;\r\n                    }\r\n                }\r\n                if (this.heightTexture && engine.getCaps().maxVertexTextureImageUnits) {\r\n                    if (!this.heightTexture.isReady()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needUVs = true;\r\n                        defines.HEIGHTMAP = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // High level\r\n        if (this.highLevelFur !== defines.HIGHLEVEL) {\r\n            defines.HIGHLEVEL = true;\r\n            defines.markAsUnprocessed();\r\n        }\r\n\r\n        // Misc.\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, this.pointsCloud, this.fogEnabled, this._shouldTurnAlphaTestOn(mesh), defines);\r\n\r\n        // Lights\r\n        defines._needNormals = MaterialHelper.PrepareDefinesForLights(scene, mesh, defines, false, this._maxSimultaneousLights, this._disableLighting);\r\n\r\n        // Values that need to be evaluated on every frame\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, true, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            MaterialHelper.HandleFallbacksForShadows(defines, fallbacks, this.maxSimultaneousLights);\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (defines.UV2) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            // Legacy browser patch\r\n            const shaderName = \"fur\";\r\n            const join = defines.toString();\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vLightsType\",\r\n                \"vDiffuseColor\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"vDiffuseInfos\",\r\n                \"mBones\",\r\n                \"diffuseMatrix\",\r\n                \"furLength\",\r\n                \"furAngle\",\r\n                \"furColor\",\r\n                \"furOffset\",\r\n                \"furGravity\",\r\n                \"furTime\",\r\n                \"furSpacing\",\r\n                \"furDensity\",\r\n                \"furOcclusion\",\r\n            ];\r\n            addClipPlaneUniforms(uniforms);\r\n            const samplers = [\"diffuseSampler\", \"heightTexture\", \"furTexture\"];\r\n\r\n            const uniformBuffers = new Array<string>();\r\n\r\n            MaterialHelper.PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: this.maxSimultaneousLights,\r\n            });\r\n\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <FurMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, this._activeEffect);\r\n\r\n        if (scene.getCachedMaterial() !== this) {\r\n            // Textures\r\n            if (this._diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                this._activeEffect.setTexture(\"diffuseSampler\", this._diffuseTexture);\r\n\r\n                this._activeEffect.setFloat2(\"vDiffuseInfos\", this._diffuseTexture.coordinatesIndex, this._diffuseTexture.level);\r\n                this._activeEffect.setMatrix(\"diffuseMatrix\", this._diffuseTexture.getTextureMatrix());\r\n            }\r\n\r\n            if (this._heightTexture) {\r\n                this._activeEffect.setTexture(\"heightTexture\", this._heightTexture);\r\n            }\r\n\r\n            // Clip plane\r\n            bindClipPlane(this._activeEffect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        this._activeEffect.setColor4(\"vDiffuseColor\", this.diffuseColor, this.alpha * mesh.visibility);\r\n\r\n        if (scene.lightsEnabled && !this.disableLighting) {\r\n            MaterialHelper.BindLights(scene, mesh, this._activeEffect, defines, this.maxSimultaneousLights);\r\n        }\r\n\r\n        // View\r\n        if (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._activeEffect.setFloat(\"furLength\", this.furLength);\r\n        this._activeEffect.setFloat(\"furAngle\", this.furAngle);\r\n        this._activeEffect.setColor4(\"furColor\", this.furColor, 1.0);\r\n\r\n        if (this.highLevelFur) {\r\n            this._activeEffect.setVector3(\"furGravity\", this.furGravity);\r\n            this._activeEffect.setFloat(\"furOffset\", this.furOffset);\r\n            this._activeEffect.setFloat(\"furSpacing\", this.furSpacing);\r\n            this._activeEffect.setFloat(\"furDensity\", this.furDensity);\r\n            this._activeEffect.setFloat(\"furOcclusion\", this.furOcclusion);\r\n\r\n            this._furTime += this.getScene().getEngine().getDeltaTime() / this.furSpeed;\r\n            this._activeEffect.setFloat(\"furTime\", this._furTime);\r\n\r\n            this._activeEffect.setTexture(\"furTexture\", this.furTexture);\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results = [];\r\n\r\n        if (this.diffuseTexture && this.diffuseTexture.animations && this.diffuseTexture.animations.length > 0) {\r\n            results.push(this.diffuseTexture);\r\n        }\r\n\r\n        if (this.heightTexture && this.heightTexture.animations && this.heightTexture.animations.length > 0) {\r\n            results.push(this.heightTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._diffuseTexture) {\r\n            activeTextures.push(this._diffuseTexture);\r\n        }\r\n\r\n        if (this._heightTexture) {\r\n            activeTextures.push(this._heightTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (this.diffuseTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._heightTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        if (this.diffuseTexture) {\r\n            this.diffuseTexture.dispose();\r\n        }\r\n\r\n        if (this._meshes) {\r\n            for (let i = 1; i < this._meshes.length; i++) {\r\n                const mat = this._meshes[i].material;\r\n\r\n                if (mat) {\r\n                    mat.dispose(forceDisposeEffect);\r\n                }\r\n                this._meshes[i].dispose();\r\n            }\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public clone(name: string): FurMaterial {\r\n        return SerializationHelper.Clone(() => new FurMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.FurMaterial\";\r\n\r\n        if (this._meshes) {\r\n            serializationObject.sourceMeshName = this._meshes[0].name;\r\n            serializationObject.quality = this._meshes.length;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"FurMaterial\";\r\n    }\r\n\r\n    // Statics\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): FurMaterial {\r\n        const material = SerializationHelper.Parse(() => new FurMaterial(source.name, scene), source, scene, rootUrl);\r\n\r\n        if (source.sourceMeshName && material.highLevelFur) {\r\n            scene.executeWhenReady(() => {\r\n                const sourceMesh = <Mesh>scene.getMeshByName(source.sourceMeshName);\r\n                if (sourceMesh) {\r\n                    const furTexture = FurMaterial.GenerateTexture(\"Fur Texture\", scene);\r\n                    material.furTexture = furTexture;\r\n                    FurMaterial.FurifyMesh(sourceMesh, source.quality);\r\n                }\r\n            });\r\n        }\r\n\r\n        return material;\r\n    }\r\n\r\n    public static GenerateTexture(name: string, scene: Scene): DynamicTexture {\r\n        // Generate fur textures\r\n        const texture = new DynamicTexture(\"FurTexture \" + name, 256, scene, true);\r\n        const context = texture.getContext();\r\n\r\n        for (let i = 0; i < 20000; ++i) {\r\n            context.fillStyle = \"rgba(255, \" + Math.floor(Math.random() * 255) + \", \" + Math.floor(Math.random() * 255) + \", 1)\";\r\n            context.fillRect(Math.random() * texture.getSize().width, Math.random() * texture.getSize().height, 2, 2);\r\n        }\r\n\r\n        texture.update(false);\r\n        texture.wrapU = Texture.WRAP_ADDRESSMODE;\r\n        texture.wrapV = Texture.WRAP_ADDRESSMODE;\r\n\r\n        return texture;\r\n    }\r\n\r\n    // Creates and returns an array of meshes used as shells for the Fur Material\r\n    // that can be disposed later in your code\r\n    // The quality is in interval [0, 100]\r\n    public static FurifyMesh(sourceMesh: Mesh, quality: number): Mesh[] {\r\n        const meshes = [sourceMesh];\r\n        const mat: FurMaterial = <FurMaterial>sourceMesh.material;\r\n        let i;\r\n\r\n        if (!(mat instanceof FurMaterial)) {\r\n            throw \"The material of the source mesh must be a Fur Material\";\r\n        }\r\n\r\n        for (i = 1; i < quality; i++) {\r\n            const offsetFur = new FurMaterial(mat.name + i, sourceMesh.getScene());\r\n            sourceMesh.getScene().materials.pop();\r\n            Tags.EnableFor(offsetFur);\r\n            Tags.AddTagsTo(offsetFur, \"furShellMaterial\");\r\n\r\n            offsetFur.furLength = mat.furLength;\r\n            offsetFur.furAngle = mat.furAngle;\r\n            offsetFur.furGravity = mat.furGravity;\r\n            offsetFur.furSpacing = mat.furSpacing;\r\n            offsetFur.furSpeed = mat.furSpeed;\r\n            offsetFur.furColor = mat.furColor;\r\n            offsetFur.diffuseTexture = mat.diffuseTexture;\r\n            offsetFur.furOffset = i / quality;\r\n            offsetFur.furTexture = mat.furTexture;\r\n            offsetFur.highLevelFur = mat.highLevelFur;\r\n            offsetFur.furTime = mat.furTime;\r\n            offsetFur.furDensity = mat.furDensity;\r\n\r\n            const offsetMesh = sourceMesh.clone(sourceMesh.name + i) as Mesh;\r\n\r\n            offsetMesh.material = offsetFur;\r\n            offsetMesh.skeleton = sourceMesh.skeleton;\r\n            offsetMesh.position = Vector3.Zero();\r\n            meshes.push(offsetMesh);\r\n        }\r\n\r\n        for (i = 1; i < meshes.length; i++) {\r\n            meshes[i].parent = sourceMesh;\r\n        }\r\n\r\n        (<FurMaterial>sourceMesh.material)._meshes = meshes;\r\n\r\n        return meshes;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FurMaterial\", FurMaterial);\r\n"]}