{"name": "@babylonjs/materials", "version": "5.57.1", "main": "index.js", "module": "index.js", "types": "index.d.ts", "files": ["**/*.js", "**/*.d.ts", "**/*.map", "readme.md", "license.md"], "scripts": {"build": "npm run clean && npm run compile", "clean": "rimraf dist && rimraf *.tsbuildinfo && rimraf \"./**/*.!(md|json|build.json)\"", "compile": "tsc -b tsconfig.build.json", "postcompile": "build-tools -c add-js-to-es6"}, "devDependencies": {"@babylonjs/core": "^5.57.1", "@dev/build-tools": "^1.0.0", "@lts/materials": "^1.0.0", "rimraf": "^3.0.2", "typescript": "^4.4.4"}, "peerDependencies": {"@babylonjs/core": "^5.22.0"}, "keywords": ["3D", "javascript", "html5", "webgl", "babylon.js"], "license": "Apache-2.0", "esnext": "index.js", "type": "module", "sideEffects": true, "homepage": "https://www.babylonjs.com", "repository": {"type": "git", "url": "https://github.com/BabylonJS/Babylon.js.git"}, "bugs": {"url": "https://github.com/BabylonJS/Babylon.js/issues"}}