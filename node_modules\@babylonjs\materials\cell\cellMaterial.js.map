{"version": 3, "file": "cellMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/cell/cellMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,2CAA6B;AAE/H,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAG/C,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAGpD,OAAO,iBAAiB,CAAC;AACzB,OAAO,eAAe,CAAC;AACvB,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,mBAAoB,SAAQ,eAAe;IA2B7C;QACI,KAAK,EAAE,CAAC;QA3BL,YAAO,GAAG,KAAK,CAAC;QAChB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,WAAM,GAAG,KAAK,CAAC;QACf,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,UAAK,GAAG,IAAI,CAAC;QACb,uBAAkB,GAAG,IAAI,CAAC;QAC1B,cAAS,GAAG,IAAI,CAAC;QACjB,iBAAY,GAAG,KAAK,CAAC;QACrB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,YAAa,SAAQ,YAAY;IAwB1C,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAlBhB,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGnC,sBAAiB,GAAY,KAAK,CAAC;QAKlC,qBAAgB,GAAG,KAAK,CAAC;QAKzB,2BAAsB,GAAG,CAAC,CAAC;IAMnC,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAC5B,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;SACvD;QAED,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,WAAW;QACX,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE;wBACjC,OAAO,KAAK,CAAC;qBAChB;yBAAM;wBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;qBAC1B;iBACJ;aACJ;SACJ;QAED,aAAa;QACb,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC;QAE3C,QAAQ;QACR,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAExI,SAAS;QACT,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/I,kDAAkD;QAClD,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEtE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAEzF,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,MAAM,UAAU,GAAG,MAAM,CAAC;YAC1B,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,eAAe;gBACf,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,eAAe;gBACf,QAAQ;gBACR,eAAe;aAClB,CAAC;YACF,MAAM,QAAQ,GAAG,CAAC,gBAAgB,CAAC,CAAC;YACpC,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;YAE3C,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/B,cAAc,CAAC,8BAA8B,CAAyB;gBAClE,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;aACpD,CAAC,CAAC;YACH,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE;aAC7E,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,WAAW;YACX,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAEtE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACjH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC1F;YAED,aAAa;YACb,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/C,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/F,SAAS;QACT,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACpG;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC3E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,cAAc;QACjB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACvG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SACtC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC7C;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,eAAe,KAAK,OAAO,CAAC;IAC5C,CAAC;IAEM,OAAO,CAAC,kBAA4B;QACvC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;SAClC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAe,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACxG,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;QACxD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACH,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACzG,CAAC;CACJ;AA7SG;IADC,kBAAkB,CAAC,gBAAgB,CAAC;qDACA;AAErC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDAClB;AAGnC;IADC,iBAAiB,CAAC,SAAS,CAAC;kDACa;AAG1C;IADC,SAAS,CAAC,kBAAkB,CAAC;uDACY;AAE1C;IADC,gBAAgB,CAAC,kCAAkC,CAAC;sDACpB;AAGjC;IADC,SAAS,CAAC,iBAAiB,CAAC;sDACI;AAEjC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;qDACnB;AAGhC;IADC,SAAS,CAAC,uBAAuB,CAAC;4DACA;AAEnC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;2DACd;AA2RzC,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { serializeAsTexture, serialize, expandToProperty, serializeAsColor3, SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { IEffectCreationOptions } from \"core/Materials/effect\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { MaterialFlags } from \"core/Materials/materialFlags\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\n\r\nimport \"./cell.fragment\";\r\nimport \"./cell.vertex\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass CellMaterialDefines extends MaterialDefines {\r\n    public DIFFUSE = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public NORMAL = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public NDOTL = true;\r\n    public CUSTOMUSERLIGHTING = true;\r\n    public CELLBASIC = true;\r\n    public DEPTHPREPASS = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class CellMaterial extends PushMaterial {\r\n    @serializeAsTexture(\"diffuseTexture\")\r\n    private _diffuseTexture: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture: BaseTexture;\r\n\r\n    @serializeAsColor3(\"diffuse\")\r\n    public diffuseColor = new Color3(1, 1, 1);\r\n\r\n    @serialize(\"computeHighLevel\")\r\n    public _computeHighLevel: boolean = false;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public computeHighLevel: boolean;\r\n\r\n    @serialize(\"disableLighting\")\r\n    private _disableLighting = false;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting: boolean;\r\n\r\n    @serialize(\"maxSimultaneousLights\")\r\n    private _maxSimultaneousLights = 4;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights: number;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return this.alpha < 1.0;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new CellMaterialDefines();\r\n        }\r\n\r\n        const defines = <CellMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Textures\r\n        if (defines._areTexturesDirty) {\r\n            defines._needUVs = false;\r\n            if (scene.texturesEnabled) {\r\n                if (this._diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (!this._diffuseTexture.isReady()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needUVs = true;\r\n                        defines.DIFFUSE = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // High level\r\n        defines.CELLBASIC = !this.computeHighLevel;\r\n\r\n        // Misc.\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, this.pointsCloud, this.fogEnabled, this._shouldTurnAlphaTestOn(mesh), defines);\r\n\r\n        // Lights\r\n        defines._needNormals = MaterialHelper.PrepareDefinesForLights(scene, mesh, defines, false, this._maxSimultaneousLights, this._disableLighting);\r\n\r\n        // Values that need to be evaluated on every frame\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, true, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            MaterialHelper.HandleFallbacksForShadows(defines, fallbacks, this.maxSimultaneousLights);\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (defines.UV2) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            const shaderName = \"cell\";\r\n            const join = defines.toString();\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vLightsType\",\r\n                \"vDiffuseColor\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"vDiffuseInfos\",\r\n                \"mBones\",\r\n                \"diffuseMatrix\",\r\n            ];\r\n            const samplers = [\"diffuseSampler\"];\r\n            const uniformBuffers = new Array<string>();\r\n\r\n            addClipPlaneUniforms(uniforms);\r\n            MaterialHelper.PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: this.maxSimultaneousLights,\r\n            });\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: { maxSimultaneousLights: this.maxSimultaneousLights - 1 },\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <CellMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, this._activeEffect);\r\n\r\n        if (this._mustRebind(scene, effect)) {\r\n            // Textures\r\n            if (this._diffuseTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                this._activeEffect.setTexture(\"diffuseSampler\", this._diffuseTexture);\r\n\r\n                this._activeEffect.setFloat2(\"vDiffuseInfos\", this._diffuseTexture.coordinatesIndex, this._diffuseTexture.level);\r\n                this._activeEffect.setMatrix(\"diffuseMatrix\", this._diffuseTexture.getTextureMatrix());\r\n            }\r\n\r\n            // Clip plane\r\n            bindClipPlane(this._activeEffect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        this._activeEffect.setColor4(\"vDiffuseColor\", this.diffuseColor, this.alpha * mesh.visibility);\r\n\r\n        // Lights\r\n        if (scene.lightsEnabled && !this.disableLighting) {\r\n            MaterialHelper.BindLights(scene, mesh, this._activeEffect, defines, this._maxSimultaneousLights);\r\n        }\r\n\r\n        // View\r\n        if (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results = [];\r\n\r\n        if (this._diffuseTexture && this._diffuseTexture.animations && this._diffuseTexture.animations.length > 0) {\r\n            results.push(this._diffuseTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._diffuseTexture) {\r\n            activeTextures.push(this._diffuseTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        return this._diffuseTexture === texture;\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        if (this._diffuseTexture) {\r\n            this._diffuseTexture.dispose();\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"CellMaterial\";\r\n    }\r\n\r\n    public clone(name: string): CellMaterial {\r\n        return SerializationHelper.Clone<CellMaterial>(() => new CellMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.CellMaterial\";\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): CellMaterial {\r\n        return SerializationHelper.Parse(() => new CellMaterial(source.name, scene), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.CellMaterial\", CellMaterial);\r\n"]}