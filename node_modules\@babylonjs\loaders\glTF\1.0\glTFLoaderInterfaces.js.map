{"version": 3, "file": "glTFLoaderInterfaces.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/1.0/glTFLoaderInterfaces.ts"], "names": [], "mappings": "AAQA;;;GAGG;AACH,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACtB,sDAAW,CAAA;IACX,wEAAoB,CAAA;IACpB,wDAAY,CAAA;IACZ,0EAAqB,CAAA;IACrB,wDAAY,CAAA;AAChB,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,WAGX;AAHD,WAAY,WAAW;IACnB,yDAAgB,CAAA;IAChB,qDAAc,CAAA;AAClB,CAAC,EAHW,WAAW,KAAX,WAAW,QAGtB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAsBX;AAtBD,WAAY,cAAc;IACtB,sDAAW,CAAA;IACX,wEAAoB,CAAA;IACpB,wDAAY,CAAA;IACZ,0EAAqB,CAAA;IACrB,oDAAU,CAAA;IACV,sEAAmB,CAAA;IACnB,wDAAY,CAAA;IACZ,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;IAClB,+DAAgB,CAAA;IAChB,+DAAgB,CAAA;IAChB,+DAAgB,CAAA;IAChB,uDAAY,CAAA;IACZ,iEAAiB,CAAA;IACjB,iEAAiB,CAAA;IACjB,iEAAiB,CAAA;IACjB,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;IAClB,mEAAkB,CAAA;AACtB,CAAC,EAtBW,cAAc,KAAd,cAAc,QAsBzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,gBAIX;AAJD,WAAY,gBAAgB;IACxB,6EAAqB,CAAA;IACrB,iFAAuB,CAAA;IACvB,+DAAc,CAAA;AAClB,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,QAI3B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,kBAOX;AAPD,WAAY,kBAAkB;IAC1B,oEAAc,CAAA;IACd,kEAAa,CAAA;IACb,kGAA6B,CAAA;IAC7B,gGAA4B,CAAA;IAC5B,gGAA4B,CAAA;IAC5B,8FAA2B,CAAA;AAC/B,CAAC,EAPW,kBAAkB,KAAlB,kBAAkB,QAO7B;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,cAMX;AAND,WAAY,cAAc;IACtB,wDAAY,CAAA;IACZ,oDAAU,CAAA;IACV,sDAAW,CAAA;IACX,gEAAgB,CAAA;IAChB,4EAAsB,CAAA;AAC1B,CAAC,EANW,cAAc,KAAd,cAAc,QAMzB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,YAIX;AAJD,WAAY,YAAY;IACpB,oDAAY,CAAA;IACZ,kDAAW,CAAA;IACX,sEAAqB,CAAA;AACzB,CAAC,EAJW,YAAY,KAAZ,YAAY,QAIvB;AAED,gBAAgB;AAChB,MAAM,CAAN,IAAY,iBAgBX;AAhBD,WAAY,iBAAiB;IACzB,yDAAQ,CAAA;IACR,uDAAO,CAAA;IACP,qEAAe,CAAA;IACf,yFAAyB,CAAA;IACzB,qEAAe,CAAA;IACf,yFAAyB,CAAA;IACzB,qEAAe,CAAA;IACf,yFAAyB,CAAA;IACzB,qEAAe,CAAA;IACf,yFAAyB,CAAA;IACzB,iFAAsB,CAAA;IACtB,qGAAgC,CAAA;IAChC,iFAAsB,CAAA;IACtB,qGAAgC,CAAA;IAChC,uFAAwB,CAAA;AAC5B,CAAC,EAhBW,iBAAiB,KAAjB,iBAAiB,QAgB5B", "sourcesContent": ["import type { AssetContainer } from \"core/assetContainer\";\r\nimport type { Bone } from \"core/Bones/bone\";\r\nimport type { Skeleton } from \"core/Bones/skeleton\";\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { Node } from \"core/node\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Nullable } from \"core/types\";\r\n\r\n/**\r\n * Enums\r\n * @internal\r\n */\r\nexport enum EComponentType {\r\n    BYTE = 5120,\r\n    UNSIGNED_BYTE = 5121,\r\n    SHORT = 5122,\r\n    UNSIGNED_SHORT = 5123,\r\n    FLOAT = 5126,\r\n}\r\n\r\n/** @internal */\r\nexport enum EShaderType {\r\n    FRAGMENT = 35632,\r\n    VERTEX = 35633,\r\n}\r\n\r\n/** @internal */\r\nexport enum EParameterType {\r\n    BYTE = 5120,\r\n    UNSIGNED_BYTE = 5121,\r\n    SHORT = 5122,\r\n    UNSIGNED_SHORT = 5123,\r\n    INT = 5124,\r\n    UNSIGNED_INT = 5125,\r\n    FLOAT = 5126,\r\n    FLOAT_VEC2 = 35664,\r\n    FLOAT_VEC3 = 35665,\r\n    FLOAT_VEC4 = 35666,\r\n    INT_VEC2 = 35667,\r\n    INT_VEC3 = 35668,\r\n    INT_VEC4 = 35669,\r\n    BOOL = 35670,\r\n    BOOL_VEC2 = 35671,\r\n    BOOL_VEC3 = 35672,\r\n    BOOL_VEC4 = 35673,\r\n    FLOAT_MAT2 = 35674,\r\n    FLOAT_MAT3 = 35675,\r\n    FLOAT_MAT4 = 35676,\r\n    SAMPLER_2D = 35678,\r\n}\r\n\r\n/** @internal */\r\nexport enum ETextureWrapMode {\r\n    CLAMP_TO_EDGE = 33071,\r\n    MIRRORED_REPEAT = 33648,\r\n    REPEAT = 10497,\r\n}\r\n\r\n/** @internal */\r\nexport enum ETextureFilterType {\r\n    NEAREST = 9728,\r\n    LINEAR = 9728,\r\n    NEAREST_MIPMAP_NEAREST = 9984,\r\n    LINEAR_MIPMAP_NEAREST = 9985,\r\n    NEAREST_MIPMAP_LINEAR = 9986,\r\n    LINEAR_MIPMAP_LINEAR = 9987,\r\n}\r\n\r\n/** @internal */\r\nexport enum ETextureFormat {\r\n    ALPHA = 6406,\r\n    RGB = 6407,\r\n    RGBA = 6408,\r\n    LUMINANCE = 6409,\r\n    LUMINANCE_ALPHA = 6410,\r\n}\r\n\r\n/** @internal */\r\nexport enum ECullingType {\r\n    FRONT = 1028,\r\n    BACK = 1029,\r\n    FRONT_AND_BACK = 1032,\r\n}\r\n\r\n/** @internal */\r\nexport enum EBlendingFunction {\r\n    ZERO = 0,\r\n    ONE = 1,\r\n    SRC_COLOR = 768,\r\n    ONE_MINUS_SRC_COLOR = 769,\r\n    DST_COLOR = 774,\r\n    ONE_MINUS_DST_COLOR = 775,\r\n    SRC_ALPHA = 770,\r\n    ONE_MINUS_SRC_ALPHA = 771,\r\n    DST_ALPHA = 772,\r\n    ONE_MINUS_DST_ALPHA = 773,\r\n    CONSTANT_COLOR = 32769,\r\n    ONE_MINUS_CONSTANT_COLOR = 32770,\r\n    CONSTANT_ALPHA = 32771,\r\n    ONE_MINUS_CONSTANT_ALPHA = 32772,\r\n    SRC_ALPHA_SATURATE = 776,\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFProperty {\r\n    extensions?: { [key: string]: any };\r\n    extras?: Object;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFChildRootProperty extends IGLTFProperty {\r\n    name?: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAccessor extends IGLTFChildRootProperty {\r\n    bufferView: string;\r\n    byteOffset: number;\r\n    byteStride: number;\r\n    count: number;\r\n    type: string;\r\n    componentType: EComponentType;\r\n\r\n    max?: number[];\r\n    min?: number[];\r\n    name?: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFBufferView extends IGLTFChildRootProperty {\r\n    buffer: string;\r\n    byteOffset: number;\r\n    byteLength: number;\r\n    byteStride: number;\r\n\r\n    target?: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFBuffer extends IGLTFChildRootProperty {\r\n    uri: string;\r\n\r\n    byteLength?: number;\r\n    type?: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFShader extends IGLTFChildRootProperty {\r\n    uri: string;\r\n    type: EShaderType;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFProgram extends IGLTFChildRootProperty {\r\n    attributes: string[];\r\n    fragmentShader: string;\r\n    vertexShader: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTechniqueParameter {\r\n    type: number;\r\n\r\n    count?: number;\r\n    semantic?: string;\r\n    node?: string;\r\n    value?: number | boolean | string | Array<any>;\r\n    source?: string;\r\n\r\n    babylonValue?: any;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTechniqueCommonProfile {\r\n    lightingModel: string;\r\n    texcoordBindings: Object;\r\n\r\n    parameters?: Array<any>;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTechniqueStatesFunctions {\r\n    blendColor?: number[];\r\n    blendEquationSeparate?: number[];\r\n    blendFuncSeparate?: number[];\r\n    colorMask: boolean[];\r\n    cullFace: number[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTechniqueStates {\r\n    enable: number[];\r\n    functions: IGLTFTechniqueStatesFunctions;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTechnique extends IGLTFChildRootProperty {\r\n    parameters: { [key: string]: IGLTFTechniqueParameter };\r\n    program: string;\r\n\r\n    attributes: { [key: string]: string };\r\n    uniforms: { [key: string]: string };\r\n    states: IGLTFTechniqueStates;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFMaterial extends IGLTFChildRootProperty {\r\n    technique?: string;\r\n    values: string[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFMeshPrimitive extends IGLTFProperty {\r\n    attributes: { [key: string]: string };\r\n    indices: string;\r\n    material: string;\r\n\r\n    mode?: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFMesh extends IGLTFChildRootProperty {\r\n    primitives: IGLTFMeshPrimitive[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFImage extends IGLTFChildRootProperty {\r\n    uri: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFSampler extends IGLTFChildRootProperty {\r\n    magFilter?: number;\r\n    minFilter?: number;\r\n    wrapS?: number;\r\n    wrapT?: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFTexture extends IGLTFChildRootProperty {\r\n    sampler: string;\r\n    source: string;\r\n\r\n    format?: ETextureFormat;\r\n    internalFormat?: ETextureFormat;\r\n    target?: number;\r\n    type?: number;\r\n\r\n    // Babylon.js values (optimize)\r\n    babylonTexture?: Texture;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAmbienLight {\r\n    color?: number[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFDirectionalLight {\r\n    color?: number[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFPointLight {\r\n    color?: number[];\r\n    constantAttenuation?: number;\r\n    linearAttenuation?: number;\r\n    quadraticAttenuation?: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFSpotLight {\r\n    color?: number[];\r\n    constantAttenuation?: number;\r\n    fallOfAngle?: number;\r\n    fallOffExponent?: number;\r\n    linearAttenuation?: number;\r\n    quadraticAttenuation?: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFLight extends IGLTFChildRootProperty {\r\n    type: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFCameraOrthographic {\r\n    xmag: number;\r\n    ymag: number;\r\n    zfar: number;\r\n    znear: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFCameraPerspective {\r\n    aspectRatio: number;\r\n    yfov: number;\r\n    zfar: number;\r\n    znear: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFCamera extends IGLTFChildRootProperty {\r\n    type: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAnimationChannelTarget {\r\n    id: string;\r\n    path: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAnimationChannel {\r\n    sampler: string;\r\n    target: IGLTFAnimationChannelTarget;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAnimationSampler {\r\n    input: string;\r\n    output: string;\r\n\r\n    interpolation?: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFAnimation extends IGLTFChildRootProperty {\r\n    channels?: IGLTFAnimationChannel[];\r\n    parameters?: { [key: string]: string };\r\n    samplers?: { [key: string]: IGLTFAnimationSampler };\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFNodeInstanceSkin {\r\n    skeletons: string[];\r\n    skin: string;\r\n    meshes: string[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFSkins extends IGLTFChildRootProperty {\r\n    bindShapeMatrix: number[];\r\n    inverseBindMatrices: string;\r\n    jointNames: string[];\r\n\r\n    babylonSkeleton?: Skeleton;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFNode extends IGLTFChildRootProperty {\r\n    camera?: string;\r\n    children: string[];\r\n    skin?: string;\r\n    jointName?: string;\r\n    light?: string;\r\n    matrix: number[];\r\n    mesh?: string;\r\n    meshes?: string[];\r\n    rotation?: number[];\r\n    scale?: number[];\r\n    translation?: number[];\r\n\r\n    // Babylon.js values (optimize)\r\n    babylonNode?: Node;\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFScene extends IGLTFChildRootProperty {\r\n    nodes: string[];\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFRuntime {\r\n    extensions: { [key: string]: any };\r\n    accessors: { [key: string]: IGLTFAccessor };\r\n    buffers: { [key: string]: IGLTFBuffer };\r\n    bufferViews: { [key: string]: IGLTFBufferView };\r\n    meshes: { [key: string]: IGLTFMesh };\r\n    lights: { [key: string]: IGLTFLight };\r\n    cameras: { [key: string]: IGLTFCamera };\r\n    nodes: { [key: string]: IGLTFNode };\r\n    images: { [key: string]: IGLTFImage };\r\n    textures: { [key: string]: IGLTFTexture };\r\n    shaders: { [key: string]: IGLTFShader };\r\n    programs: { [key: string]: IGLTFProgram };\r\n    samplers: { [key: string]: IGLTFSampler };\r\n    techniques: { [key: string]: IGLTFTechnique };\r\n    materials: { [key: string]: IGLTFMaterial };\r\n    animations: { [key: string]: IGLTFAnimation };\r\n    skins: { [key: string]: IGLTFSkins };\r\n\r\n    currentScene?: Object;\r\n    scenes: { [key: string]: IGLTFScene }; // v1.1\r\n\r\n    extensionsUsed: string[];\r\n    extensionsRequired?: string[]; // v1.1\r\n\r\n    buffersCount: number;\r\n    shaderscount: number;\r\n\r\n    scene: Scene;\r\n    rootUrl: string;\r\n\r\n    loadedBufferCount: number;\r\n    loadedBufferViews: { [name: string]: ArrayBufferView };\r\n\r\n    loadedShaderCount: number;\r\n\r\n    importOnlyMeshes: boolean;\r\n    importMeshesNames?: string[];\r\n\r\n    dummyNodes: Node[];\r\n\r\n    assetContainer: Nullable<AssetContainer>;\r\n}\r\n\r\n/** @internal */\r\nexport interface INodeToRoot {\r\n    bone: Bone;\r\n    node: IGLTFNode;\r\n    id: string;\r\n}\r\n\r\n/** @internal */\r\nexport interface IJointNode {\r\n    node: IGLTFNode;\r\n    id: string;\r\n}\r\n"]}