/**
 * Enum used to define system values e.g. values automatically provided by the system
 */
export var NodeMaterialSystemValues;
(function (NodeMaterialSystemValues) {
    /** World */
    NodeMaterialSystemValues[NodeMaterialSystemValues["World"] = 1] = "World";
    /** View */
    NodeMaterialSystemValues[NodeMaterialSystemValues["View"] = 2] = "View";
    /** Projection */
    NodeMaterialSystemValues[NodeMaterialSystemValues["Projection"] = 3] = "Projection";
    /** ViewProjection */
    NodeMaterialSystemValues[NodeMaterialSystemValues["ViewProjection"] = 4] = "ViewProjection";
    /** WorldView */
    NodeMaterialSystemValues[NodeMaterialSystemValues["WorldView"] = 5] = "WorldView";
    /** WorldViewProjection */
    NodeMaterialSystemValues[NodeMaterialSystemValues["WorldViewProjection"] = 6] = "WorldViewProjection";
    /** CameraPosition */
    NodeMaterialSystemValues[NodeMaterialSystemValues["CameraPosition"] = 7] = "CameraPosition";
    /** Fog Color */
    NodeMaterialSystemValues[NodeMaterialSystemValues["FogColor"] = 8] = "FogColor";
    /** Delta time */
    NodeMaterialSystemValues[NodeMaterialSystemValues["DeltaTime"] = 9] = "DeltaTime";
    /** Camera parameters */
    NodeMaterialSystemValues[NodeMaterialSystemValues["CameraParameters"] = 10] = "CameraParameters";
    /** Material alpha */
    NodeMaterialSystemValues[NodeMaterialSystemValues["MaterialAlpha"] = 11] = "MaterialAlpha";
})(NodeMaterialSystemValues || (NodeMaterialSystemValues = {}));
//# sourceMappingURL=nodeMaterialSystemValues.js.map