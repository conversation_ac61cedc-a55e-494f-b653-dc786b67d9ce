{"version": 3, "file": "cell.fragment.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/cell/cell.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,mEAAqD;AACrD,4EAA8D;AAC9D,uEAAyD;AACzD,2EAA6D;AAC7D,4EAA8D;AAC9D,gFAAkE;AAClE,0EAA4D;AAC5D,qEAAuD;AACvD,gEAAkD;AAClD,iEAAmD;AACnD,+DAAiD;AACjD,gFAAkE;AAElE,MAAM,IAAI,GAAG,iBAAiB,CAAC;AAC/B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2Gb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,eAAe,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/helperFunctions\";\nimport \"core/Shaders/ShadersInclude/lightFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightUboDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/shadowsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragment\";\nimport \"core/Shaders/ShadersInclude/depthPrePass\";\nimport \"core/Shaders/ShadersInclude/lightFragment\";\nimport \"core/Shaders/ShadersInclude/fogFragment\";\nimport \"core/Shaders/ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"cellPixelShader\";\nconst shader = `precision highp float;\runiform vec4 vEyePosition;\runiform vec4 vDiffuseColor;\rvarying vec3 vPositionW;\r#ifdef NORMAL\nvarying vec3 vNormalW;\r#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\r#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;\runiform sampler2D diffuseSampler;\runiform vec2 vDiffuseInfos;\r#endif\n#include<clipPlaneFragmentDeclaration>\n#include<fogFragmentDeclaration>\nvec3 computeCustomDiffuseLighting(lightingInfo info,vec3 diffuseBase,float shadow)\r{\rdiffuseBase=info.diffuse*shadow;\r#ifdef CELLBASIC\nfloat level=1.0;\rif (info.ndl<0.5)\rlevel=0.5;\rdiffuseBase.rgb*vec3(level,level,level);\r#else\nfloat ToonThresholds[4];\rToonThresholds[0]=0.95;\rToonThresholds[1]=0.5;\rToonThresholds[2]=0.2;\rToonThresholds[3]=0.03;\rfloat ToonBrightnessLevels[5];\rToonBrightnessLevels[0]=1.0;\rToonBrightnessLevels[1]=0.8;\rToonBrightnessLevels[2]=0.6;\rToonBrightnessLevels[3]=0.35;\rToonBrightnessLevels[4]=0.2;\rif (info.ndl>ToonThresholds[0])\r{\rdiffuseBase.rgb*=ToonBrightnessLevels[0];\r}\relse if (info.ndl>ToonThresholds[1])\r{\rdiffuseBase.rgb*=ToonBrightnessLevels[1];\r}\relse if (info.ndl>ToonThresholds[2])\r{\rdiffuseBase.rgb*=ToonBrightnessLevels[2];\r}\relse if (info.ndl>ToonThresholds[3])\r{\rdiffuseBase.rgb*=ToonBrightnessLevels[3];\r}\relse\r{\rdiffuseBase.rgb*=ToonBrightnessLevels[4];\r}\r#endif\nreturn max(diffuseBase,vec3(0.2));\r}\r#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void)\r{\r#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);\rvec4 baseColor=vec4(1.,1.,1.,1.);\rvec3 diffuseColor=vDiffuseColor.rgb;\rfloat alpha=vDiffuseColor.a;\r#ifdef DIFFUSE\nbaseColor=texture2D(diffuseSampler,vDiffuseUV);\r#ifdef ALPHATEST\nif (baseColor.a<0.4)\rdiscard;\r#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\r#endif\n#ifdef VERTEXCOLOR\nbaseColor.rgb*=vColor.rgb;\r#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\r#else\nvec3 normalW=vec3(1.0,1.0,1.0);\r#endif\nlightingInfo info;\rvec3 diffuseBase=vec3(0.,0.,0.);\rfloat shadow=1.;\rfloat glossiness=0.;\r#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\r#endif \n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\r#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;\rvec4 color=vec4(finalDiffuse,alpha);\r#include<fogFragment>\ngl_FragColor=color;\r#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const cellPixelShader = { name, shader };\n"]}