{"version": 3, "file": "glTFLoaderAnimation.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/2.0/glTFLoaderAnimation.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,gDAAkC;AACtD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,6CAA+B;AAO7D,gBAAgB;AAChB,MAAM,UAAU,UAAU,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IACxF,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,aAAa,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IAC3F,OAAO,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACpE,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,UAAU,CAAC,MAAa,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IACzF,MAAM,KAAK,GAAG,IAAI,KAAK,CAAS,MAAM,CAAC,gBAAiB,CAAC,CAAC;IAC1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;KACvC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAgB,qBAAqB;IACvC,gBAAgB;IAChB,YAAmC,IAAY,EAAkB,IAAY,EAAkB,QAAoB,EAAkB,SAAkC;QAApI,SAAI,GAAJ,IAAI,CAAQ;QAAkB,SAAI,GAAJ,IAAI,CAAQ;QAAkB,aAAQ,GAAR,QAAQ,CAAY;QAAkB,cAAS,GAAT,SAAS,CAAyB;IAAG,CAAC;IAEjK,eAAe,CAAC,IAAY,EAAE,GAAW,EAAE,IAAW;QAC5D,MAAM,gBAAgB,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,gBAAgB,CAAC;IAC5B,CAAC;CAIJ;AAED,gBAAgB;AAChB,MAAM,OAAO,kCAAmC,SAAQ,qBAAqB;IACzE,gBAAgB;IACT,eAAe,CAAC,MAAa,EAAE,IAAY,EAAE,GAAW,EAAE,IAAW,EAAE,QAA+E;QACzJ,QAAQ,CAAC,MAAM,CAAC,qBAAsB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IACnF,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,2BAA4B,SAAQ,qBAAqB;IAC3D,eAAe,CAAC,MAAa,EAAE,IAAY,EAAE,GAAW,EAAE,IAAW,EAAE,QAA+E;QACzJ,IAAI,MAAM,CAAC,gBAAgB,EAAE;YACzB,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,gBAAgB,EAAE,WAAW,EAAE,EAAE;gBAC5E,MAAM,gBAAgB,GAAG,IAAI,SAAS,CAAC,GAAG,IAAI,IAAI,WAAW,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5F,gBAAgB,CAAC,OAAO,CACpB,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;oBACf,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;oBACjE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC;oBAC7B,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;oBACpE,aAAa,EAAE,GAAG,CAAC,aAAa;iBACnC,CAAC,CAAC,CACN,CAAC;gBAEF,IAAI,MAAM,CAAC,uBAAuB,EAAE;oBAChC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,uBAAuB,EAAE;wBACtD,IAAI,WAAW,CAAC,kBAAkB,EAAE;4BAChC,MAAM,WAAW,GAAG,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;4BAC1E,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC;4BACvD,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;4BACnD,QAAQ,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;yBAChD;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC7B,WAAW,EAAE,CAAC,IAAI,kCAAkC,CAAC,SAAS,CAAC,qBAAqB,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACvH,QAAQ,EAAE,CAAC,IAAI,kCAAkC,CAAC,SAAS,CAAC,wBAAwB,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACpI,KAAK,EAAE,CAAC,IAAI,kCAAkC,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChH,OAAO,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAiB,CAAC,CAAC;CAC3I,CAAC", "sourcesContent": ["import { Animation } from \"core/Animations/animation\";\r\nimport { Quaternion, Vector3 } from \"core/Maths/math.vector\";\r\nimport type { INode } from \"./glTFLoaderInterfaces\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\n\r\n/** @internal */\r\nexport type GetValueFn = (target: any, source: Float32Array, offset: number, scale: number) => any;\r\n\r\n/** @internal */\r\nexport function getVector3(_target: any, source: Float32Array, offset: number, scale: number): Vector3 {\r\n    return Vector3.FromArray(source, offset).scaleInPlace(scale);\r\n}\r\n\r\n/** @internal */\r\nexport function getQuaternion(_target: any, source: Float32Array, offset: number, scale: number): Quaternion {\r\n    return Quaternion.FromArray(source, offset).scaleInPlace(scale);\r\n}\r\n\r\n/** @internal */\r\nexport function getWeights(target: INode, source: Float32Array, offset: number, scale: number): Array<number> {\r\n    const value = new Array<number>(target._numMorphTargets!);\r\n    for (let i = 0; i < value.length; i++) {\r\n        value[i] = source[offset++] * scale;\r\n    }\r\n\r\n    return value;\r\n}\r\n\r\n/** @internal */\r\nexport abstract class AnimationPropertyInfo {\r\n    /** @internal */\r\n    public constructor(public readonly type: number, public readonly name: string, public readonly getValue: GetValueFn, public readonly getStride: (target: any) => number) {}\r\n\r\n    protected _buildAnimation(name: string, fps: number, keys: any[]): Animation {\r\n        const babylonAnimation = new Animation(name, this.name, fps, this.type);\r\n        babylonAnimation.setKeys(keys);\r\n        return babylonAnimation;\r\n    }\r\n\r\n    /** @internal */\r\n    public abstract buildAnimations(target: any, name: string, fps: number, keys: any[], callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void): void;\r\n}\r\n\r\n/** @internal */\r\nexport class TransformNodeAnimationPropertyInfo extends AnimationPropertyInfo {\r\n    /** @internal */\r\n    public buildAnimations(target: INode, name: string, fps: number, keys: any[], callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void): void {\r\n        callback(target._babylonTransformNode!, this._buildAnimation(name, fps, keys));\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WeightAnimationPropertyInfo extends AnimationPropertyInfo {\r\n    public buildAnimations(target: INode, name: string, fps: number, keys: any[], callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void): void {\r\n        if (target._numMorphTargets) {\r\n            for (let targetIndex = 0; targetIndex < target._numMorphTargets; targetIndex++) {\r\n                const babylonAnimation = new Animation(`${name}_${targetIndex}`, this.name, fps, this.type);\r\n                babylonAnimation.setKeys(\r\n                    keys.map((key) => ({\r\n                        frame: key.frame,\r\n                        inTangent: key.inTangent ? key.inTangent[targetIndex] : undefined,\r\n                        value: key.value[targetIndex],\r\n                        outTangent: key.outTangent ? key.outTangent[targetIndex] : undefined,\r\n                        interpolation: key.interpolation,\r\n                    }))\r\n                );\r\n\r\n                if (target._primitiveBabylonMeshes) {\r\n                    for (const babylonMesh of target._primitiveBabylonMeshes) {\r\n                        if (babylonMesh.morphTargetManager) {\r\n                            const morphTarget = babylonMesh.morphTargetManager.getTarget(targetIndex);\r\n                            const babylonAnimationClone = babylonAnimation.clone();\r\n                            morphTarget.animations.push(babylonAnimationClone);\r\n                            callback(morphTarget, babylonAnimationClone);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport const nodeAnimationData = {\r\n    translation: [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_VECTOR3, \"position\", getVector3, () => 3)],\r\n    rotation: [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_QUATERNION, \"rotationQuaternion\", getQuaternion, () => 4)],\r\n    scale: [new TransformNodeAnimationPropertyInfo(Animation.ANIMATIONTYPE_VECTOR3, \"scaling\", getVector3, () => 3)],\r\n    weights: [new WeightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"influence\", getWeights, (target) => target._numMorphTargets!)],\r\n};\r\n"]}