{"version": 3, "file": "KHR_materials_transmission.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_transmission.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAK3C,OAAO,EAAE,mBAAmB,EAAE,kEAAoD;AAElF,OAAO,EAAE,UAAU,EAAE,2CAA6B;AAClD,OAAO,EAAE,SAAS,EAAE,6CAA+B;AACnD,OAAO,EAAE,KAAK,EAAE,sCAAwB;AAgDxC;;GAEG;AACH,MAAM,kBAAkB;IACpB;;OAEG;IACK,MAAM,CAAC,kBAAkB;QAC7B,OAAO;YACH,UAAU,EAAE,IAAI;YAChB,OAAO,EAAE,CAAC;YACV,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC,CAAC;YACvB,uBAAuB,EAAE,SAAS,CAAC,sBAAsB;YACzD,eAAe,EAAE,IAAI;SACxB,CAAC;IACN,CAAC;IAoBD;;;;OAIG;IACH,YAAY,OAA4C,EAAE,KAAY;QAhB9D,wBAAmB,GAAkC,IAAI,CAAC;QAC1D,uBAAkB,GAAmB,EAAE,CAAC;QACxC,4BAAuB,GAAmB,EAAE,CAAC;QAC7C,uBAAkB,GAAuD,EAAE,CAAC;QAchF,IAAI,CAAC,QAAQ,GAAG;YACZ,GAAG,kBAAkB,CAAC,kBAAkB,EAAE;YAC1C,GAAG,OAAO;SACb,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,KAAY,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEvC,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,OAA4C;QAC7D,uEAAuE;QACvE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,EAAE,CAAE,IAAI,CAAC,QAAgB,CAAC,GAAG,CAAC,KAAM,OAAe,CAAC,GAAG,CAAC,CAAC,CAAC;QACtH,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACnB,OAAO;SACV;QAED,MAAM,UAAU,GAAG;YACf,GAAG,IAAI,CAAC,QAAQ;YAChB,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;QAE3B,uCAAuC;QACvC,IACI,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,UAAU;YAC/C,UAAU,CAAC,uBAAuB,KAAK,UAAU,CAAC,uBAAuB;YACzE,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,eAAe;YACzD,CAAC,IAAI,CAAC,mBAAmB,EAC3B;YACE,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;aAAM;YACH,IAAI,CAAC,mBAAmB,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC;YAC5E,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;SACjF;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAEO,2BAA2B,CAAC,QAA4B;QAC5D,IAAI,CAAC,QAAQ,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,QAAQ,YAAY,WAAW,IAAI,QAAQ,CAAC,UAAU,CAAC,mBAAmB,EAAE;YAC5E,OAAO,IAAI,CAAC;SACf;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,QAAQ,CAAC,IAAkB;QAC/B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEtH,0HAA0H;QAC1H,kGAAkG;QAClG,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAChD,IAAI,CAAC,QAAwB,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;gBAC5E,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBACnD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC3C;aACJ;iBAAM;gBACH,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC9C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACtC;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,WAAW,CAAC,IAAkB;QAClC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAChF,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SAC/C;QACD,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SAC1C;IACL,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,8EAA8E;QAC9E,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACnE,0FAA0F;QAC1F,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,+GAA+G;IACvG,sBAAsB,CAAC,IAAkB;QAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,qHAAqH;QACrH,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxE,IAAI,eAAe,EAAE;YACjB,IAAI,IAAI,CAAC,QAAQ,YAAY,WAAW,EAAE;gBACtC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;aACzE;YACD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAClB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBAC7C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3C;iBAAM,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBAC9B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC3C;YACD,gHAAgH;SACnH;aAAM;YACH,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;gBACvB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtC;iBAAM,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtC;SACJ;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB;;QACvB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;SACtC;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB,CAC9C,oBAAoB,EACpB,IAAI,CAAC,QAAQ,CAAC,UAAU,EACxB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,QAAQ,CAAC,eAAe,EAC7B,SAAS,EACT,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CACxC,CAAC;QACF,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACrD,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC9D,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,MAAA,MAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,0CAAE,KAAK,EAAE,mCAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAC1G,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QAC/E,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QACjF,IAAI,CAAC,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QAEzD,IAAI,sCAA+C,CAAC;QAEpD,IAAI,qBAA6B,CAAC;QAClC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE;YACvE,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,GAAG,CAAC;YACvC,sCAAsC,GAAG,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YACrG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC,CAAC;aAC7H;iBAAM;gBACH,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;aACpE;YACD,+GAA+G;YAC/G,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACxE,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,qBAAqB,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,mBAAmB,GAAG,sCAAsC,CAAC;QAC1G,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,IAAkB,EAAE,EAAE;YACxD,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAChD,IAAI,CAAC,QAAwB,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;aAC/E;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,SAAS,CAAC;QAC5C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACnC;QACD,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;IACjC,CAAC;CACJ;AAED,MAAM,IAAI,GAAG,4BAA4B,CAAC;AAE1C;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,0BAA0B;IAkBnC;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,sBAAsB,GAAG,IAAI,CAAC;SAC/C;IACL,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAA4B,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC1H,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAChG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC;YAC5G,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,+BAA+B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB,EAAE,SAAoC;QACzI,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QACD,MAAM,WAAW,GAAG,eAA8B,CAAC;QAEnD,mEAAmE;QACnE,WAAW,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAElD,qFAAqF;QACrF,WAAW,CAAC,UAAU,CAAC,uBAAuB,GAAG,GAAG,CAAC;QAErD,wCAAwC;QACxC,WAAW,CAAC,UAAU,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAExD,IAAI,SAAS,CAAC,kBAAkB,KAAK,SAAS,EAAE;YAC5C,WAAW,CAAC,UAAU,CAAC,mBAAmB,GAAG,SAAS,CAAC,kBAAkB,CAAC;YAC1E,MAAM,KAAK,GAAG,WAAW,CAAC,QAAQ,EAA0C,CAAC;YAC7E,IAAI,WAAW,CAAC,UAAU,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE;gBAC1E,IAAI,kBAAkB,CAAC,EAAE,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;aACtD;SACJ;aAAM;YACH,WAAW,CAAC,UAAU,CAAC,mBAAmB,GAAG,GAAG,CAAC;YACjD,WAAW,CAAC,UAAU,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACnD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,WAAW,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9C,WAAW,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9C,IAAI,SAAS,CAAC,mBAAmB,EAAE;YAC9B,SAAS,CAAC,mBAAoC,CAAC,YAAY,GAAG,IAAI,CAAC;YACpE,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,sBAAsB,EAAE,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,OAAoB,EAAE,EAAE;gBAC/I,WAAW,CAAC,UAAU,CAAC,0BAA0B,GAAG,OAAO,CAAC;gBAC5D,WAAW,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;IACL,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsTransmission } from \"babylonjs-gltf2interface\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"core/Materials/Textures/renderTargetTexture\";\r\nimport type { Observer } from \"core/Misc/observable\";\r\nimport { Observable } from \"core/Misc/observable\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport type { Color4 } from \"core/Maths/math.color\";\r\n\r\ninterface ITransmissionHelperHolder {\r\n    /**\r\n     * @internal\r\n     */\r\n    _transmissionHelper: TransmissionHelper | undefined;\r\n}\r\n\r\ninterface ITransmissionHelperOptions {\r\n    /**\r\n     * The size of the render buffers (default: 1024)\r\n     */\r\n    renderSize: number;\r\n\r\n    /**\r\n     * The number of samples to use when generating the render target texture for opaque meshes (default: 4)\r\n     */\r\n    samples: number;\r\n\r\n    /**\r\n     * Scale to apply when selecting the LOD level to sample the refraction texture (default: 1)\r\n     */\r\n    lodGenerationScale: number;\r\n\r\n    /**\r\n     * Offset to apply when selecting the LOD level to sample the refraction texture (default: -4)\r\n     */\r\n    lodGenerationOffset: number;\r\n\r\n    /**\r\n     * Type of the refraction render target texture (default: TEXTURETYPE_HALF_FLOAT)\r\n     */\r\n    renderTargetTextureType: number;\r\n\r\n    /**\r\n     * Defines if the mipmaps for the refraction render target texture must be generated (default: true)\r\n     */\r\n    generateMipmaps: boolean;\r\n\r\n    /**\r\n     * Clear color of the opaque texture. If not provided, use the scene clear color (which will be converted to linear space).\r\n     * If provided, should be in linear space\r\n     */\r\n    clearColor?: Color4;\r\n}\r\n\r\n/**\r\n * A class to handle setting up the rendering of opaque objects to be shown through transmissive objects.\r\n */\r\nclass TransmissionHelper {\r\n    /**\r\n     * Creates the default options for the helper.\r\n     */\r\n    private static _GetDefaultOptions(): ITransmissionHelperOptions {\r\n        return {\r\n            renderSize: 1024,\r\n            samples: 4,\r\n            lodGenerationScale: 1,\r\n            lodGenerationOffset: -4,\r\n            renderTargetTextureType: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            generateMipmaps: true,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Stores the creation options.\r\n     */\r\n    private readonly _scene: Scene & ITransmissionHelperHolder;\r\n\r\n    private _options: ITransmissionHelperOptions;\r\n\r\n    private _opaqueRenderTarget: Nullable<RenderTargetTexture> = null;\r\n    private _opaqueMeshesCache: AbstractMesh[] = [];\r\n    private _transparentMeshesCache: AbstractMesh[] = [];\r\n    private _materialObservers: { [id: string]: Nullable<Observer<AbstractMesh>> } = {};\r\n\r\n    /**\r\n     * This observable will be notified with any error during the creation of the environment,\r\n     * mainly texture creation errors.\r\n     */\r\n    public onErrorObservable: Observable<{ message?: string; exception?: any }>;\r\n\r\n    /**\r\n     * constructor\r\n     * @param options Defines the options we want to customize the helper\r\n     * @param scene The scene to add the material to\r\n     */\r\n    constructor(options: Partial<ITransmissionHelperOptions>, scene: Scene) {\r\n        this._options = {\r\n            ...TransmissionHelper._GetDefaultOptions(),\r\n            ...options,\r\n        };\r\n        this._scene = scene as any;\r\n        this._scene._transmissionHelper = this;\r\n\r\n        this.onErrorObservable = new Observable();\r\n        this._scene.onDisposeObservable.addOnce(() => {\r\n            this.dispose();\r\n        });\r\n\r\n        this._parseScene();\r\n        this._setupRenderTargets();\r\n    }\r\n\r\n    /**\r\n     * Updates the background according to the new options\r\n     * @param options\r\n     */\r\n    public updateOptions(options: Partial<ITransmissionHelperOptions>) {\r\n        // First check if any options are actually being changed. If not, exit.\r\n        const newValues = Object.keys(options).filter((key: string) => (this._options as any)[key] !== (options as any)[key]);\r\n        if (!newValues.length) {\r\n            return;\r\n        }\r\n\r\n        const newOptions = {\r\n            ...this._options,\r\n            ...options,\r\n        };\r\n\r\n        const oldOptions = this._options;\r\n        this._options = newOptions;\r\n\r\n        // If size changes, recreate everything\r\n        if (\r\n            newOptions.renderSize !== oldOptions.renderSize ||\r\n            newOptions.renderTargetTextureType !== oldOptions.renderTargetTextureType ||\r\n            newOptions.generateMipmaps !== oldOptions.generateMipmaps ||\r\n            !this._opaqueRenderTarget\r\n        ) {\r\n            this._setupRenderTargets();\r\n        } else {\r\n            this._opaqueRenderTarget.samples = newOptions.samples;\r\n            this._opaqueRenderTarget.lodGenerationScale = newOptions.lodGenerationScale;\r\n            this._opaqueRenderTarget.lodGenerationOffset = newOptions.lodGenerationOffset;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the opaque render target texture or null if not available.\r\n     */\r\n    public getOpaqueTarget(): Nullable<Texture> {\r\n        return this._opaqueRenderTarget;\r\n    }\r\n\r\n    private _shouldRenderAsTransmission(material: Nullable<Material>): boolean {\r\n        if (!material) {\r\n            return false;\r\n        }\r\n        if (material instanceof PBRMaterial && material.subSurface.isRefractionEnabled) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private _addMesh(mesh: AbstractMesh): void {\r\n        this._materialObservers[mesh.uniqueId] = mesh.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this));\r\n\r\n        // we need to defer the processing because _addMesh may be called as part as an instance mesh creation, in which case some\r\n        // internal properties are not setup yet, like _sourceMesh (needed when doing mesh.material below)\r\n        Tools.SetImmediate(() => {\r\n            if (this._shouldRenderAsTransmission(mesh.material)) {\r\n                (mesh.material as PBRMaterial).refractionTexture = this._opaqueRenderTarget;\r\n                if (this._transparentMeshesCache.indexOf(mesh) === -1) {\r\n                    this._transparentMeshesCache.push(mesh);\r\n                }\r\n            } else {\r\n                if (this._opaqueMeshesCache.indexOf(mesh) === -1) {\r\n                    this._opaqueMeshesCache.push(mesh);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _removeMesh(mesh: AbstractMesh): void {\r\n        mesh.onMaterialChangedObservable.remove(this._materialObservers[mesh.uniqueId]);\r\n        delete this._materialObservers[mesh.uniqueId];\r\n        let idx = this._transparentMeshesCache.indexOf(mesh);\r\n        if (idx !== -1) {\r\n            this._transparentMeshesCache.splice(idx, 1);\r\n        }\r\n        idx = this._opaqueMeshesCache.indexOf(mesh);\r\n        if (idx !== -1) {\r\n            this._opaqueMeshesCache.splice(idx, 1);\r\n        }\r\n    }\r\n\r\n    private _parseScene(): void {\r\n        this._scene.meshes.forEach(this._addMesh.bind(this));\r\n        // Listen for when a mesh is added to the scene and add it to our cache lists.\r\n        this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this));\r\n        // Listen for when a mesh is removed from to the scene and remove it from our cache lists.\r\n        this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this));\r\n    }\r\n\r\n    // When one of the meshes in the scene has its material changed, make sure that it's in the correct cache list.\r\n    private _onMeshMaterialChanged(mesh: AbstractMesh) {\r\n        const transparentIdx = this._transparentMeshesCache.indexOf(mesh);\r\n        const opaqueIdx = this._opaqueMeshesCache.indexOf(mesh);\r\n\r\n        // If the material is transparent, make sure that it's added to the transparent list and removed from the opaque list\r\n        const useTransmission = this._shouldRenderAsTransmission(mesh.material);\r\n        if (useTransmission) {\r\n            if (mesh.material instanceof PBRMaterial) {\r\n                mesh.material.subSurface.refractionTexture = this._opaqueRenderTarget;\r\n            }\r\n            if (opaqueIdx !== -1) {\r\n                this._opaqueMeshesCache.splice(opaqueIdx, 1);\r\n                this._transparentMeshesCache.push(mesh);\r\n            } else if (transparentIdx === -1) {\r\n                this._transparentMeshesCache.push(mesh);\r\n            }\r\n            // If the material is opaque, make sure that it's added to the opaque list and removed from the transparent list\r\n        } else {\r\n            if (transparentIdx !== -1) {\r\n                this._transparentMeshesCache.splice(transparentIdx, 1);\r\n                this._opaqueMeshesCache.push(mesh);\r\n            } else if (opaqueIdx === -1) {\r\n                this._opaqueMeshesCache.push(mesh);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Setup the render targets according to the specified options.\r\n     */\r\n    private _setupRenderTargets(): void {\r\n        if (this._opaqueRenderTarget) {\r\n            this._opaqueRenderTarget.dispose();\r\n        }\r\n        this._opaqueRenderTarget = new RenderTargetTexture(\r\n            \"opaqueSceneTexture\",\r\n            this._options.renderSize,\r\n            this._scene,\r\n            this._options.generateMipmaps,\r\n            undefined,\r\n            this._options.renderTargetTextureType\r\n        );\r\n        this._opaqueRenderTarget.ignoreCameraViewport = true;\r\n        this._opaqueRenderTarget.renderList = this._opaqueMeshesCache;\r\n        this._opaqueRenderTarget.clearColor = this._options.clearColor?.clone() ?? this._scene.clearColor.clone();\r\n        this._opaqueRenderTarget.gammaSpace = false;\r\n        this._opaqueRenderTarget.lodGenerationScale = this._options.lodGenerationScale;\r\n        this._opaqueRenderTarget.lodGenerationOffset = this._options.lodGenerationOffset;\r\n        this._opaqueRenderTarget.samples = this._options.samples;\r\n\r\n        let sceneImageProcessingapplyByPostProcess: boolean;\r\n\r\n        let saveSceneEnvIntensity: number;\r\n        this._opaqueRenderTarget.onBeforeBindObservable.add((opaqueRenderTarget) => {\r\n            saveSceneEnvIntensity = this._scene.environmentIntensity;\r\n            this._scene.environmentIntensity = 1.0;\r\n            sceneImageProcessingapplyByPostProcess = this._scene.imageProcessingConfiguration.applyByPostProcess;\r\n            if (!this._options.clearColor) {\r\n                this._scene.clearColor.toLinearSpaceToRef(opaqueRenderTarget.clearColor, this._scene.getEngine().useExactSrgbConversions);\r\n            } else {\r\n                opaqueRenderTarget.clearColor.copyFrom(this._options.clearColor);\r\n            }\r\n            // we do not use the applyByPostProcess setter to avoid flagging all the materials as \"image processing dirty\"!\r\n            this._scene.imageProcessingConfiguration._applyByPostProcess = true;\r\n        });\r\n        this._opaqueRenderTarget.onAfterUnbindObservable.add(() => {\r\n            this._scene.environmentIntensity = saveSceneEnvIntensity;\r\n            this._scene.imageProcessingConfiguration._applyByPostProcess = sceneImageProcessingapplyByPostProcess;\r\n        });\r\n\r\n        this._transparentMeshesCache.forEach((mesh: AbstractMesh) => {\r\n            if (this._shouldRenderAsTransmission(mesh.material)) {\r\n                (mesh.material as PBRMaterial).refractionTexture = this._opaqueRenderTarget;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Dispose all the elements created by the Helper.\r\n     */\r\n    public dispose(): void {\r\n        this._scene._transmissionHelper = undefined;\r\n        if (this._opaqueRenderTarget) {\r\n            this._opaqueRenderTarget.dispose();\r\n            this._opaqueRenderTarget = null;\r\n        }\r\n        this._transparentMeshesCache = [];\r\n        this._opaqueMeshesCache = [];\r\n    }\r\n}\r\n\r\nconst NAME = \"KHR_materials_transmission\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_transmission/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_transmission implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 175;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n        if (this.enabled) {\r\n            loader.parent.transparencyAsCoverage = true;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsTransmission>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadTransparentPropertiesAsync(extensionContext, material, babylonMaterial, extension));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadTransparentPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material, extension: IKHRMaterialsTransmission): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n        const pbrMaterial = babylonMaterial as PBRMaterial;\r\n\r\n        // Enables \"refraction\" texture which represents transmitted light.\r\n        pbrMaterial.subSurface.isRefractionEnabled = true;\r\n\r\n        // Since this extension models thin-surface transmission only, we must make IOR = 1.0\r\n        pbrMaterial.subSurface.volumeIndexOfRefraction = 1.0;\r\n\r\n        // Albedo colour will tint transmission.\r\n        pbrMaterial.subSurface.useAlbedoToTintRefraction = true;\r\n\r\n        if (extension.transmissionFactor !== undefined) {\r\n            pbrMaterial.subSurface.refractionIntensity = extension.transmissionFactor;\r\n            const scene = pbrMaterial.getScene() as unknown as ITransmissionHelperHolder;\r\n            if (pbrMaterial.subSurface.refractionIntensity && !scene._transmissionHelper) {\r\n                new TransmissionHelper({}, pbrMaterial.getScene());\r\n            }\r\n        } else {\r\n            pbrMaterial.subSurface.refractionIntensity = 0.0;\r\n            pbrMaterial.subSurface.isRefractionEnabled = false;\r\n            return Promise.resolve();\r\n        }\r\n\r\n        pbrMaterial.subSurface.minimumThickness = 0.0;\r\n        pbrMaterial.subSurface.maximumThickness = 0.0;\r\n        if (extension.transmissionTexture) {\r\n            (extension.transmissionTexture as ITextureInfo).nonColorData = true;\r\n            return this._loader.loadTextureInfoAsync(`${context}/transmissionTexture`, extension.transmissionTexture, undefined).then((texture: BaseTexture) => {\r\n                pbrMaterial.subSurface.refractionIntensityTexture = texture;\r\n                pbrMaterial.subSurface.useGltfStyleTextures = true;\r\n            });\r\n        } else {\r\n            return Promise.resolve();\r\n        }\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_transmission(loader));\r\n"]}