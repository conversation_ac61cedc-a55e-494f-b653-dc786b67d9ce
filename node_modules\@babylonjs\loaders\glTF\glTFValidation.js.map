{"version": 3, "file": "glTFValidation.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/glTF/glTFValidation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,sCAAwB;AASxC,SAAS,aAAa,CAClB,IAA0B,EAC1B,OAAe,EACf,QAAgB,EAChB,mBAA0D;IAE1D,MAAM,OAAO,GAAiC;QAC1C,wBAAwB,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;KACrG,CAAC;IAEF,IAAI,QAAQ,EAAE;QACV,OAAO,CAAC,GAAG,GAAG,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;KACrE;IAED,OAAO,IAAI,YAAY,WAAW,CAAC,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAClJ,CAAC;AAED;;GAEG;AACH,SAAS,UAAU;IACf,MAAM,wBAAwB,GAA2E,EAAE,CAAC;IAE5G,SAAS,GAAG,CAAC,OAAO,EAAE,EAAE;QACpB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,QAAQ,IAAI,CAAC,EAAE,EAAE;YACb,KAAK,MAAM,CAAC,CAAC;gBACT,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxB,MAAM;aACT;YACD,KAAK,UAAU,CAAC,CAAC;gBACb,aAAa,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,CAAC,GAAG,EAAE,EAAE,CACJ,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC5B,MAAM,KAAK,GAAG,wBAAwB,CAAC,MAAM,CAAC;oBAC9C,wBAAwB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;oBACnD,WAAW,CAAC,EAAE,EAAE,EAAE,qBAAqB,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBACvE,CAAC,CAAC,CACT,CAAC,IAAI,CACF,CAAC,KAAK,EAAE,EAAE;oBACN,WAAW,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1D,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;oBACP,WAAW,CAAC,EAAE,EAAE,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC3D,CAAC,CACJ,CAAC;gBACF,MAAM;aACT;YACD,KAAK,6BAA6B,CAAC,CAAC;gBAChC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACzD,MAAM;aACT;YACD,KAAK,4BAA4B,CAAC,CAAC;gBAC/B,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzD,MAAM;aACT;SACJ;IACL,CAAC,CAAC;AACN,CAAC;AAYD;;GAEG;AACH,MAAM,OAAO,cAAc;IAUvB;;;;;;;OAOG;IACI,MAAM,CAAC,aAAa,CACvB,IAA0B,EAC1B,OAAe,EACf,QAAgB,EAChB,mBAA0D;QAE1D,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;YAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACnC,MAAM,aAAa,GAAG,GAAG,aAAa,IAAI,UAAU,KAAK,CAAC;gBAC1D,MAAM,aAAa,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAC,CAAC;gBACzG,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;gBAEzC,MAAM,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;oBAClC,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;oBAC7C,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;oBACjD,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClB,CAAC,CAAC;gBAEF,MAAM,SAAS,GAAG,CAAC,OAAqB,EAAE,EAAE;oBACxC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;oBAC1B,QAAQ,IAAI,CAAC,EAAE,EAAE;wBACb,KAAK,qBAAqB,CAAC,CAAC;4BACxB,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAC9B,CAAC,KAAK,EAAE,EAAE;gCACN,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,6BAA6B,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;4BACxG,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;gCACP,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,4BAA4B,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;4BAChG,CAAC,CACJ,CAAC;4BACF,MAAM;yBACT;wBACD,KAAK,kBAAkB,CAAC,CAAC;4BACrB,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;4BAC7C,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;4BACjD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACpB,MAAM,CAAC,SAAS,EAAE,CAAC;4BACnB,MAAM;yBACT;wBACD,KAAK,iBAAiB,CAAC,CAAC;4BACpB,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;4BAC7C,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;4BACjD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BACpB,MAAM,CAAC,SAAS,EAAE,CAAC;yBACtB;qBACJ;gBACL,CAAC,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC1C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAE9C,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;gBAChE,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;aAC3E;YAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,EAAE;gBACrC,OAAO,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,mBAAmB,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;SACN;IACL,CAAC;;AAhFD;;GAEG;AACW,4BAAa,GAAiC;IACxD,GAAG,EAAE,iDAAiD;CACzD,CAAC", "sourcesContent": ["import type * as GLTF2 from \"babylonjs-gltf2interface\";\r\nimport { Tools } from \"core/Misc/tools\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let GLTFValidator: GLTF2.IGLTFValidator;\r\n\r\n// WorkerGlobalScope\r\ndeclare function importScripts(...urls: string[]): void;\r\ndeclare function postMessage(message: any, transfer?: any[]): void;\r\n\r\nfunction validateAsync(\r\n    data: string | ArrayBuffer,\r\n    rootUrl: string,\r\n    fileName: string,\r\n    getExternalResource: (uri: string) => Promise<ArrayBuffer>\r\n): Promise<GLTF2.IGLTFValidationResults> {\r\n    const options: GLTF2.IGLTFValidationOptions = {\r\n        externalResourceFunction: (uri) => getExternalResource(uri).then((value) => new Uint8Array(value)),\r\n    };\r\n\r\n    if (fileName) {\r\n        options.uri = rootUrl === \"file:\" ? fileName : rootUrl + fileName;\r\n    }\r\n\r\n    return data instanceof ArrayBuffer ? GLTFValidator.validateBytes(new Uint8Array(data), options) : GLTFValidator.validateString(data, options);\r\n}\r\n\r\n/**\r\n * The worker function that gets converted to a blob url to pass into a worker.\r\n */\r\nfunction workerFunc(): void {\r\n    const pendingExternalResources: Array<{ resolve: (data: any) => void; reject: (reason: any) => void }> = [];\r\n\r\n    onmessage = (message) => {\r\n        const data = message.data;\r\n        switch (data.id) {\r\n            case \"init\": {\r\n                importScripts(data.url);\r\n                break;\r\n            }\r\n            case \"validate\": {\r\n                validateAsync(\r\n                    data.data,\r\n                    data.rootUrl,\r\n                    data.fileName,\r\n                    (uri) =>\r\n                        new Promise((resolve, reject) => {\r\n                            const index = pendingExternalResources.length;\r\n                            pendingExternalResources.push({ resolve, reject });\r\n                            postMessage({ id: \"getExternalResource\", index: index, uri: uri });\r\n                        })\r\n                ).then(\r\n                    (value) => {\r\n                        postMessage({ id: \"validate.resolve\", value: value });\r\n                    },\r\n                    (reason) => {\r\n                        postMessage({ id: \"validate.reject\", reason: reason });\r\n                    }\r\n                );\r\n                break;\r\n            }\r\n            case \"getExternalResource.resolve\": {\r\n                pendingExternalResources[data.index].resolve(data.value);\r\n                break;\r\n            }\r\n            case \"getExternalResource.reject\": {\r\n                pendingExternalResources[data.index].reject(data.reason);\r\n                break;\r\n            }\r\n        }\r\n    };\r\n}\r\n\r\n/**\r\n * Configuration for glTF validation\r\n */\r\nexport interface IGLTFValidationConfiguration {\r\n    /**\r\n     * The url of the glTF validator.\r\n     */\r\n    url: string;\r\n}\r\n\r\n/**\r\n * glTF validation\r\n */\r\nexport class GLTFValidation {\r\n    /**\r\n     * The configuration. Defaults to `{ url: \"https://preview.babylonjs.com/gltf_validator.js\" }`.\r\n     */\r\n    public static Configuration: IGLTFValidationConfiguration = {\r\n        url: \"https://preview.babylonjs.com/gltf_validator.js\",\r\n    };\r\n\r\n    private static _LoadScriptPromise: Promise<void>;\r\n\r\n    /**\r\n     * Validate a glTF asset using the glTF-Validator.\r\n     * @param data The JSON of a glTF or the array buffer of a binary glTF\r\n     * @param rootUrl The root url for the glTF\r\n     * @param fileName The file name for the glTF\r\n     * @param getExternalResource The callback to get external resources for the glTF validator\r\n     * @returns A promise that resolves with the glTF validation results once complete\r\n     */\r\n    public static ValidateAsync(\r\n        data: string | ArrayBuffer,\r\n        rootUrl: string,\r\n        fileName: string,\r\n        getExternalResource: (uri: string) => Promise<ArrayBuffer>\r\n    ): Promise<GLTF2.IGLTFValidationResults> {\r\n        if (typeof Worker === \"function\") {\r\n            return new Promise((resolve, reject) => {\r\n                const workerContent = `${validateAsync}(${workerFunc})()`;\r\n                const workerBlobUrl = URL.createObjectURL(new Blob([workerContent], { type: \"application/javascript\" }));\r\n                const worker = new Worker(workerBlobUrl);\r\n\r\n                const onError = (error: ErrorEvent) => {\r\n                    worker.removeEventListener(\"error\", onError);\r\n                    worker.removeEventListener(\"message\", onMessage);\r\n                    reject(error);\r\n                };\r\n\r\n                const onMessage = (message: MessageEvent) => {\r\n                    const data = message.data;\r\n                    switch (data.id) {\r\n                        case \"getExternalResource\": {\r\n                            getExternalResource(data.uri).then(\r\n                                (value) => {\r\n                                    worker.postMessage({ id: \"getExternalResource.resolve\", index: data.index, value: value }, [value]);\r\n                                },\r\n                                (reason) => {\r\n                                    worker.postMessage({ id: \"getExternalResource.reject\", index: data.index, reason: reason });\r\n                                }\r\n                            );\r\n                            break;\r\n                        }\r\n                        case \"validate.resolve\": {\r\n                            worker.removeEventListener(\"error\", onError);\r\n                            worker.removeEventListener(\"message\", onMessage);\r\n                            resolve(data.value);\r\n                            worker.terminate();\r\n                            break;\r\n                        }\r\n                        case \"validate.reject\": {\r\n                            worker.removeEventListener(\"error\", onError);\r\n                            worker.removeEventListener(\"message\", onMessage);\r\n                            reject(data.reason);\r\n                            worker.terminate();\r\n                        }\r\n                    }\r\n                };\r\n\r\n                worker.addEventListener(\"error\", onError);\r\n                worker.addEventListener(\"message\", onMessage);\r\n\r\n                worker.postMessage({ id: \"init\", url: this.Configuration.url });\r\n                worker.postMessage({ id: \"validate\", data: data, rootUrl: rootUrl, fileName: fileName });\r\n            });\r\n        } else {\r\n            if (!this._LoadScriptPromise) {\r\n                this._LoadScriptPromise = Tools.LoadScriptAsync(this.Configuration.url);\r\n            }\r\n\r\n            return this._LoadScriptPromise.then(() => {\r\n                return validateAsync(data, rootUrl, fileName, getExternalResource);\r\n            });\r\n        }\r\n    }\r\n}\r\n"]}