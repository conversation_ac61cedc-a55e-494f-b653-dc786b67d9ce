{"version": 3, "file": "glTFLoader.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/1.0/glTFLoader.ts"], "names": [], "mappings": "AA8BA,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAG1H,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,6CAA+B;AACrE,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,KAAK,EAAE,sCAAwB;AACxC,OAAO,EAAE,MAAM,EAAE,0CAA4B;AAC7C,OAAO,EAAE,UAAU,EAAE,8CAAgC;AACrD,OAAO,EAAE,SAAS,EAAE,gDAAkC;AACtD,OAAO,EAAE,IAAI,EAAE,sCAAwB;AACvC,OAAO,EAAE,QAAQ,EAAE,0CAA4B;AAC/C,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,QAAQ,EAAE,8CAAgC;AACnD,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AACnE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAE1D,OAAO,EAAE,UAAU,EAAE,kDAAoC;AACzD,OAAO,EAAE,YAAY,EAAE,0CAA4B;AACnD,OAAO,EAAE,QAAQ,EAAE,2CAA6B;AAChD,OAAO,EAAE,OAAO,EAAE,0CAA4B;AAC9C,OAAO,EAAE,YAAY,EAAE,+CAAiC;AACxD,OAAO,EAAE,IAAI,EAAE,uCAAyB;AACxC,OAAO,EAAE,gBAAgB,EAAE,mDAAqC;AAChE,OAAO,EAAE,gBAAgB,EAAE,mDAAqC;AAChE,OAAO,EAAE,UAAU,EAAE,6CAA+B;AACpD,OAAO,EAAE,SAAS,EAAE,4CAA8B;AAIlD,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAE9C,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,6CAA+B;AAGnD;;;GAGG;AACH,IAAK,UAKJ;AALD,WAAK,UAAU;IACX,uDAAc,CAAA;IAEd,iDAAW,CAAA;IACX,2DAAgB,CAAA;AACpB,CAAC,EALI,UAAU,KAAV,UAAU,QAKd;AAED,MAAM,SAAS;IAUX,YAAY,OAAe;QARnB,SAAI,GAAW,CAAC,CAAC;QAGlB,iBAAY,GAAe,UAAU,CAAC,OAAO,CAAC;QAC9C,sBAAiB,GAAW,EAAE,CAAC;QAC/B,kBAAa,GAAW,EAAE,CAAC;QAC3B,2BAAsB,GAAW,gBAAgB,CAAC;QAGrD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAClC,CAAC;IAEM,YAAY;QACf,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;YACd,OAAO,UAAU,CAAC,YAAY,CAAC;SAClC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC;QAEvC,IAAI,IAAI,CAAC,aAAa,KAAK,GAAG,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACpF,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC;YAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC;YAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,aAAa,KAAK,GAAG,CAAC,EAAE;gBAC1H,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,aAAa,CAAC;gBAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;SACJ;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEM,IAAI;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAEM,IAAI;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACtC,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;IAEM,KAAK;QACR,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC;IACrC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAC;AAC1G,MAAM,iBAAiB,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC;AAExG,MAAM,kBAAkB,GAAG,CAAC,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAChE,MAAM,qBAAqB,GAAG,CAAC,UAAU,EAAE,oBAAoB,EAAE,SAAS,CAAC,CAAC;AAE5E;;;;GAIG;AACH,MAAM,YAAY,GAAG,CAAC,aAAkB,EAAE,WAAyB,EAAE,EAAE;IACnE,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;QAC7B,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;QACxC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QACxC,WAAW,CAAC,YAAY,EAAE,CAAC;KAC9B;AACL,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,aAAkB,EAAE,WAAyB,EAAE,EAAE;IACnE,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;QAC7B,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;QACxC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QACxC,WAAW,CAAC,YAAY,EAAE,CAAC;KAC9B;AACL,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,aAAkB,EAAE,eAAuB,EAAE,WAAyB,EAAE,EAAE;IAC3F,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE;QAChC,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QACrC,WAAY,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC;KAC9D;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,YAAY,GAAG,CAAC,MAAW,EAAE,EAAE;IACjC,IAAI,CAAC,MAAM,EAAE;QACT,OAAO;KACV;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KAC/C;AACL,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,kBAA2C,EAAoB,EAAE;IACnF,IAAI,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,EAAE;QAC1C,OAAO,QAAQ,CAAC;KACnB;SAAM,IAAI,kBAAkB,CAAC,QAAQ,KAAK,UAAU,EAAE;QACnD,OAAO,UAAU,CAAC;KACrB;SAAM,IAAI,kBAAkB,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChD,OAAO,iBAAiB,CAAC;KAC5B;SAAM,IAAI,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,EAAE;QACjD,OAAO,iBAAiB,CAAC;KAC5B;SAAM,IAAI,kBAAkB,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChD,OAAO,OAAO,CAAC;KAClB;SAAM,IAAI,kBAAkB,CAAC,QAAQ,IAAI,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAC/F,MAAM,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,OAAO,IAAI,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;KACpD;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,cAAc,GAAG,CAAC,WAAyB,EAAE,EAAE;IACjD,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,UAAU,EAAE;QACvC,MAAM,SAAS,GAAmB,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAE/D,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC5C,SAAS;SACZ;QAED,IAAI,aAAa,GAAwB,IAAI,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,kCAAkC;YAClC,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,OAAO,GAA0B,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE3E,IAAI,CAAC,OAAO,EAAE;gBACV,SAAS;aACZ;YAED,IAAI,SAAS,GAAqB,IAAI,CAAC;YACvC,IAAI,UAAU,GAAqB,IAAI,CAAC;YAExC,IAAI,SAAS,CAAC,UAAU,EAAE;gBACtB,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAChD,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACrD;iBAAM;gBACH,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC1B,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;aAC/B;YAED,MAAM,WAAW,GAAG,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;YACnG,MAAM,YAAY,GAAG,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;YAErG,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,IAAI,UAAU,GAAQ,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE9D,IAAI,UAAU,KAAK,IAAI,EAAE;gBACrB,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;aAC1D;YAED,IAAI,UAAU,KAAK,IAAI,EAAE;gBACrB,KAAK,CAAC,IAAI,CAAC,2BAA2B,GAAG,IAAI,GAAG,+BAA+B,GAAG,QAAQ,GAAG,eAAe,CAAC,CAAC;gBAC9G,SAAS;aACZ;YAED,MAAM,MAAM,GAAG,UAAU,YAAY,IAAI,CAAC;YAE1C,kDAAkD;YAClD,IAAI,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;YACrC,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE/D,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE;gBACxB,UAAU,GAAG,qBAAqB,CAAC,eAAe,CAAC,CAAC;aACvD;YAED,2BAA2B;YAC3B,IAAI,aAAa,GAAG,SAAS,CAAC,oBAAoB,CAAC;YAEnD,IAAI,CAAC,MAAM,EAAE;gBACT,IAAI,UAAU,KAAK,oBAAoB,EAAE;oBACrC,aAAa,GAAG,SAAS,CAAC,wBAAwB,CAAC;oBACnD,UAAU,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;iBACpD;qBAAM;oBACH,aAAa,GAAG,SAAS,CAAC,qBAAqB,CAAC;iBACnD;aACJ;YAED,kCAAkC;YAClC,IAAI,gBAAgB,GAAwB,IAAI,CAAC;YACjD,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,IAAI,SAAS,GAAG,KAAK,CAAC;YAEtB,IAAI,MAAM,IAAI,aAAa,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE;gBAClF,gBAAgB,GAAG,aAAa,CAAC;gBACjC,SAAS,GAAG,IAAI,CAAC;aACpB;YAED,IAAI,CAAC,SAAS,EAAE;gBACZ,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;gBACxE,gBAAgB,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,EAAE,aAAa,EAAE,SAAS,CAAC,uBAAuB,CAAC,CAAC;gBAC7H,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;aACpD;YAED,iBAAiB;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,KAAK,GAAQ,IAAI,CAAC;gBAEtB,IAAI,UAAU,KAAK,oBAAoB,EAAE;oBACrC,OAAO;oBACP,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvJ,WAAW,IAAI,CAAC,CAAC;iBACpB;qBAAM;oBACH,gCAAgC;oBAChC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrH,WAAW,IAAI,CAAC,CAAC;iBACpB;gBAED,IAAI,MAAM,EAAE;oBACR,MAAM,IAAI,GAAS,UAAU,CAAC;oBAC9B,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;oBACjC,IAAI,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC1C,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;oBAE7B,uBAAuB;oBACvB,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;oBAE/B,IAAI,SAAS,IAAI,aAAa,EAAE;wBAC5B,GAAG,GAAG,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;qBAC1C;oBAED,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;oBAExD,IAAI,UAAU,KAAK,UAAU,EAAE;wBAC3B,WAAW,GAAG,KAAK,CAAC;qBACvB;yBAAM,IAAI,UAAU,KAAK,oBAAoB,EAAE;wBAC5C,kBAAkB,GAAG,KAAK,CAAC;qBAC9B;yBAAM;wBACH,OAAO,GAAG,KAAK,CAAC;qBACnB;oBAED,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;iBACpE;gBAED,IAAI,CAAC,SAAS,EAAE;oBACZ,IAAI,CAAC,IAAI,CAAC;wBACN,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;wBACrB,KAAK,EAAE,KAAK;qBACf,CAAC,CAAC;iBACN;qBAAM,IAAI,aAAa,EAAE;oBACtB,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;iBAC5C;aACJ;YAED,SAAS;YACT,IAAI,CAAC,SAAS,IAAI,gBAAgB,EAAE;gBAChC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC/B,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aAChD;YAED,aAAa,GAAG,gBAAgB,CAAC;YAEjC,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAC5C,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;SACnG;KACJ;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,2BAA2B,GAAG,CAAC,IAAe,EAAU,EAAE;IAC5D,IAAI,GAAG,GAAqB,IAAI,CAAC;IAEjC,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;QACjD,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAElE,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;KACnD;SAAM;QACH,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACvC;IAED,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,aAAa,GAAG,CAAC,WAAyB,EAAE,KAAiB,EAAE,SAAiB,EAAE,WAAqB,EAAkB,EAAE;IAC7H,cAAc;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/C,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE;YACzC,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC/B;KACJ;IAED,kCAAkC;IAClC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAChC,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;QACrB,MAAM,IAAI,GAAc,KAAK,CAAC,GAAG,CAAC,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,SAAS;SACZ;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,KAAK,GAAc,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBAClB,SAAS;aACZ;YAED,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE;gBAC/B,MAAM,GAAG,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;gBACzH,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBACd,OAAO,IAAI,CAAC;aACf;SACJ;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,aAAa,GAAG,CAAC,WAA0B,EAAE,EAAU,EAAkB,EAAE;IAC7E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtD,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,KAAK,KAAK,EAAE,EAAE;gBACd,OAAO,UAAU,CAAC,IAAI,CAAC;aAC1B;SACJ;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,YAAY,GAAG,CAAC,WAAyB,EAAE,SAAiB,EAAwB,EAAE;IACxF,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAChC,IAAI,IAAI,GAAc,KAAK,CAAC,SAAS,CAAC,CAAC;IACvC,IAAI,IAAI,EAAE;QACN,OAAO;YACH,IAAI,EAAE,IAAI;YACV,EAAE,EAAE,SAAS;SAChB,CAAC;KACL;IAED,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;QACrB,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAClB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;YAC9B,OAAO;gBACH,IAAI,EAAE,IAAI;gBACV,EAAE,EAAE,GAAG;aACV,CAAC;SACL;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,cAAc,GAAG,CAAC,KAAiB,EAAE,EAAU,EAAW,EAAE;IAC9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5B,OAAO,IAAI,CAAC;SACf;KACJ;IAED,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,cAAc,GAAG,CAAC,WAAyB,EAAE,WAAqB,EAAE,KAAiB,EAAE,WAA0B,EAAE,EAAE;IACvH,yBAAyB;IACzB,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;QACjC,MAAM,IAAI,GAAc,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,EAAE,GAAG,GAAG,CAAC;QAEf,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;YAC1D,SAAS;SACZ;QAED,2BAA2B;QAC3B,MAAM,GAAG,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;KACxD;IAED,YAAY;IACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzC,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAA0B,IAAI,CAAC;YAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE;oBACnC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBACvB,MAAM;iBACT;aACJ;YAED,IAAI,KAAK,EAAE;gBACD,KAAK,CAAC,IAAK,CAAC,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC;gBAC5C,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC7C;SACJ;KACJ;AACL,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,cAAc,GAAG,CAAC,WAAyB,EAAE,KAAiB,EAAE,IAAU,EAAE,WAAiC,EAAY,EAAE;IAC7H,IAAI,CAAC,WAAW,EAAE;QACd,WAAW,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;KACvE;IAED,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;QACxB,OAAO,WAAW,CAAC;KACtB;IAED,sBAAsB;IACtB,MAAM,WAAW,GAAkB,EAAE,CAAC;IACtC,MAAM,gBAAgB,GAAW,EAAE,CAAC;IAEpC,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IAC7D,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;IAEvB,SAAS;IACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS;SACZ;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAE5B,IAAI,CAAC,IAAI,EAAE;YACP,KAAK,CAAC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;YACrE,SAAS;SACZ;QAED,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;QAExB,0CAA0C;QAC1C,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QACvD,IAAI,YAAY,EAAE;YACd,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrC,SAAS;SACZ;QAED,yBAAyB;QACzB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,UAAU,GAAmB,IAAI,CAAC;QAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAEjE,IAAI,CAAC,SAAS,EAAE;gBACZ,SAAS;aACZ;YAED,MAAM,KAAK,GAAc,SAAS,CAAC,IAAI,CAAC;YAExC,IAAI,CAAC,KAAK,EAAE;gBACR,KAAK,CAAC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,yCAAyC,CAAC,CAAC;gBAC7F,SAAS;aACZ;YAED,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;YAChC,IAAI,CAAC,QAAQ,EAAE;gBACX,SAAS;aACZ;YACD,SAAS,GAAG,KAAK,CAAC;YAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;oBACpB,UAAU,GAAG,aAAa,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;oBACjF,SAAS,GAAG,IAAI,CAAC;oBACjB,MAAM;iBACT;aACJ;YAED,IAAI,SAAS,EAAE;gBACX,MAAM;aACT;SACJ;QAED,cAAc;QACd,MAAM,GAAG,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,UAAU,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YACvC,UAAU,GAAG,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YAE5C,IAAI,UAAU,EAAE;gBACZ,IAAI,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC7C,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACrC;aACJ;SACJ;QAED,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QAC1E,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;KAChB;IAED,SAAS;IACT,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAChC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,MAAM,SAAS,GAAG,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS;SACZ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE;gBAC9B,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM;aACT;SACJ;KACJ;IAED,WAAW,CAAC,OAAO,EAAE,CAAC;IAEtB,SAAS;IACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC9C,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;IAED,OAAO,WAAW,CAAC;AACvB,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,UAAU,GAAG,CAAC,WAAyB,EAAE,IAAe,EAAE,MAAgB,EAAE,EAAU,EAAE,OAAa,EAAQ,EAAE;IACjH,IAAI,CAAC,OAAO,EAAE;QACV,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;QACxE,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;QACtD,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACjD,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;KACnB;IAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QACnB,OAAO,OAAO,CAAC;KAClB;IAED,MAAM,YAAY,GAAe,EAAE,CAAC;IAEpC,IAAI,UAAU,GAAyB,IAAI,CAAC;IAC5C,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;IAC3C,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;IAC3C,MAAM,WAAW,GAAG,IAAI,KAAK,EAAU,CAAC;IACxC,MAAM,WAAW,GAAG,IAAI,KAAK,EAAU,CAAC;IAExC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QAC5D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QACjC,MAAM,IAAI,GAAc,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAI,EAAE;YACP,SAAS;SACZ;QAED,6BAA6B;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,wBAAwB;YACxB,MAAM,cAAc,GAAG,IAAI,UAAU,EAAE,CAAC;YAExC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;gBACtB,YAAY;aACf;YAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;YACxC,IAAI,QAAQ,GAA4B,IAAI,CAAC;YAC7C,IAAI,MAAM,GAAQ,IAAI,CAAC;YAEvB,gCAAgC;YAChC,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;gBAC/B,gCAAgC;gBAChC,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACvD,MAAM,GAAG,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAEhE,IAAI,QAAQ,KAAK,QAAQ,EAAE;oBACvB,cAAc,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC1C,cAAc,CAAC,OAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBACtD;qBAAM,IAAI,QAAQ,KAAK,UAAU,EAAE;oBAChC,IAAI,cAAc,CAAC,sBAAsB,EAAE;wBACvC,cAAc,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;wBAE/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;4BACvC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;4BACxC,cAAc,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;4BAChD,cAAc,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;yBACnD;qBACJ;yBAAM;wBACH,cAAc,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;wBAC5C,cAAc,CAAC,SAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;qBACxD;oBAED,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACxD;qBAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/C,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;oBACxE,MAAM,GAAG,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC7B,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAChC,YAAY,CAAC,GAAG,CAAC,CAAC;oBAClB,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBACnC;qBAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;oBAC7B,cAAc,CAAC,eAAe,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAClD,cAAc,CAAC,eAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC9D;qBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;oBAC9B,cAAc,CAAC,eAAe,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAClD,cAAc,CAAC,eAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBAC9D;qBAAM,IAAI,QAAQ,KAAK,OAAO,EAAE;oBAC7B,cAAc,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACzC,cAAc,CAAC,MAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;iBACrD;aACJ;YAED,UAAU;YACV,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACpD,IAAI,QAAQ,EAAE;gBACV,MAAM,GAAG,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAEhE,cAAc,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACvD,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACnC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACnD;iBAAM;gBACH,yBAAyB;gBACzB,MAAM,OAAO,GAAa,EAAE,CAAC;gBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAgB,cAAc,CAAC,SAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;oBACxE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;iBACnB;gBAED,cAAc,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;gBACjD,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aACnD;YAED,IAAI,CAAC,UAAU,EAAE;gBACb,UAAU,GAAG,cAAc,CAAC;aAC/B;iBAAM;gBACH,UAAU,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;aACpC;YAED,eAAe;YACf,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAEvE,YAAY,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;YAElG,wCAAwC;YACxC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7I,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;SAC9H;KACJ;IACD,IAAI,QAA0C,CAAC;IAC/C,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;IACxE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;QACzB,QAAQ,GAAG,IAAI,aAAa,CAAC,UAAU,GAAG,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QAChE,QAA0B,CAAC,YAAY,GAAG,YAAY,CAAC;KAC3D;SAAM;QACH,QAAQ,GAAG,IAAI,gBAAgB,CAAC,UAAU,GAAG,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;KACvE;IAED,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAqB,CAAC;KAClD;IAED,QAAQ,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;IAEvD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QACnB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC/B;IAED,iBAAiB;IACjB,IAAI,QAAQ,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,EAAE,UAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACjE,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAEjC,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;IAEjD,kBAAkB;IAClB,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;IACvB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;QAC5D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QACjC,MAAM,IAAI,GAAc,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAI,EAAE;YACP,SAAS;SACZ;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC/B,WAAW;aACd;YAED,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACvI,KAAK,EAAE,CAAC;SACX;KACJ;IAED,SAAS;IACT,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,aAAa,GAAG,CAAC,OAAY,EAAE,QAAiB,EAAE,QAAoB,EAAE,OAAgB,EAAE,EAAE;IAC9F,IAAI,OAAO,CAAC,QAAQ,EAAE;QAClB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;KAC/B;IAED,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,QAAQ,EAAE;QAChD,OAAO,CAAC,kBAAkB,GAAG,QAAQ,CAAC;KACzC;IAED,IAAI,OAAO,CAAC,OAAO,EAAE;QACjB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;KAC7B;AACL,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,uBAAuB,GAAG,CAAC,OAAa,EAAE,IAAe,EAAE,EAAE;IAC/D,IAAI,IAAI,CAAC,MAAM,EAAE;QACb,MAAM,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAI,UAAU,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE3C,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;KACvD;SAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;QACxD,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACnI;IAED,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,UAAU,GAAG,CAAC,WAAyB,EAAE,IAAe,EAAE,EAAU,EAAkB,EAAE;IAC1F,IAAI,QAAQ,GAAmB,IAAI,CAAC;IAEpC,IAAI,WAAW,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE;QAC5D,IAAI,WAAW,CAAC,iBAAiB,IAAI,WAAW,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;YAC5I,OAAO,IAAI,CAAC;SACf;KACJ;IAED,SAAS;IACT,IAAI,IAAI,CAAC,IAAI,EAAE;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,IAAI,GAAe,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtD,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,EAAQ,IAAI,CAAC,WAAW,CAAC,CAAC;YACvF,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEpE,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE;gBAC3B,OAAO,CAAC,QAAQ,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAEpF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACvB,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC;iBAC3C;aACJ;YAED,QAAQ,GAAG,OAAO,CAAC;SACtB;KACJ;SAAM,IAAI,IAAI,CAAC,MAAM,EAAE;QACpB;;WAEG;QACH,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,EAAQ,IAAI,CAAC,WAAW,CAAC,CAAC;QACjH,QAAQ,GAAG,OAAO,CAAC;KACtB;IACD,SAAS;SACJ,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;QACvE,MAAM,KAAK,GAAe,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzD,IAAI,KAAK,EAAE;YACP,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;gBAC1B,MAAM,WAAW,GAA2B,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC/D,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;gBACtF,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAEjC,IAAI,WAAW,CAAC,KAAK,EAAE;oBACnB,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;iBAC3D;gBAED,QAAQ,GAAG,SAAS,CAAC;aACxB;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;gBACrC,MAAM,gBAAgB,GAAgC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzE,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;gBACrF,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAEhC,IAAI,gBAAgB,CAAC,KAAK,EAAE;oBACxB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;iBAC/D;gBAED,QAAQ,GAAG,QAAQ,CAAC;aACvB;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBAC/B,MAAM,UAAU,GAA0B,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC9E,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAE/B,IAAI,UAAU,CAAC,KAAK,EAAE;oBAClB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACxD;gBAED,QAAQ,GAAG,OAAO,CAAC;aACtB;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;gBAC9B,MAAM,SAAS,GAAyB,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC3D,MAAM,OAAO,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;gBACnG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAE/B,IAAI,SAAS,CAAC,KAAK,EAAE;oBACjB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;iBACvD;gBAED,IAAI,SAAS,CAAC,WAAW,EAAE;oBACvB,OAAO,CAAC,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC;iBACzC;gBAED,IAAI,SAAS,CAAC,eAAe,EAAE;oBAC3B,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAC,eAAe,CAAC;iBAChD;gBAED,QAAQ,GAAG,OAAO,CAAC;aACtB;SACJ;KACJ;IACD,UAAU;SACL,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;QACxE,MAAM,MAAM,GAAgB,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7D,IAAI,MAAM,EAAE;YACR,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;YACxE,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE;gBAChC,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAE1F,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBACnC,WAAW,CAAC,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,WAAW,CAAC,aAAa,EAAE,CAAC;gBAE5B,QAAQ,GAAG,WAAW,CAAC;gBAEvB,WAAW,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;aAC7D;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE;gBACtC,MAAM,iBAAiB,GAAiC,MAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC7E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAEzF,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAClC,UAAU,CAAC,aAAa,EAAE,CAAC;gBAE3B,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;oBAChC,iBAAiB,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,cAAc,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;iBACpI;gBAED,IAAI,iBAAiB,CAAC,KAAK,IAAI,iBAAiB,CAAC,IAAI,EAAE;oBACnD,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,CAAC;oBACzC,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAC,KAAK,CAAC;iBAC7C;gBAED,QAAQ,GAAG,UAAU,CAAC;gBACtB,UAAU,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;aAC5D;YAED,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;SACpD;KACJ;IAED,aAAa;IACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,OAAO,IAAI,CAAC,WAAW,CAAC;SAC3B;aAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;YAC1B,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;YACxE,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC3D,KAAK,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;YACpD,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACjD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,QAAQ,GAAG,KAAK,CAAC;SACpB;KACJ;IAED,IAAI,QAAQ,KAAK,IAAI,EAAE;QACnB,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,YAAY,IAAI,EAAE;YACzC,uBAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC3C;aAAM;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;SACrH;QAED,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;KAC/B;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,aAAa,GAAG,CAAC,WAAyB,EAAE,EAAU,EAAE,MAAsB,EAAE,eAAwB,KAAK,EAAE,EAAE;IACnH,MAAM,IAAI,GAAc,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAI,OAAO,GAAmB,IAAI,CAAC;IAEnC,IAAI,WAAW,CAAC,gBAAgB,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC,iBAAiB,EAAE;QAChF,IAAI,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7G,YAAY,GAAG,IAAI,CAAC;SACvB;aAAM;YACH,YAAY,GAAG,KAAK,CAAC;SACxB;KACJ;SAAM;QACH,YAAY,GAAG,IAAI,CAAC;KACvB;IAED,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,YAAY,EAAE;QACjC,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAE5C,IAAI,OAAO,KAAK,IAAI,EAAE;YAClB,OAAO,CAAC,EAAE,GAAG,EAAE,CAAC;YAChB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;SAC3B;KACJ;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SACvE;KACJ;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,QAAQ,GAAG,CAAC,WAAyB,EAAE,EAAE;IAC3C,QAAQ;IACR,IAAI,YAAY,GAA2B,WAAW,CAAC,YAAY,CAAC;IAEpE,IAAI,YAAY,EAAE;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC3D;KACJ;SAAM;QACH,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE;YACpC,YAAY,GAAe,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC3D;SACJ;KACJ;IAED,iBAAiB;IACjB,cAAc,CAAC,WAAW,CAAC,CAAC;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACzD,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAChD,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;KAC9E;AACL,CAAC,CAAC;AAEF;;;;;;;;;GASG;AACH,MAAM,oBAAoB,GAAG,CACzB,IAAkB,EAClB,WAAyB,EACzB,iBAA6D,EAC7D,cAA8B,EAC9B,SAAyB,EACzB,QAAuB,EACvB,SAAmD,EACrD,EAAE;IACA,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC;IAE/D,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE;QAClC,MAAM,OAAO,GAA4B,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACjE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAE1B,IAAI,IAAI,KAAK,cAAc,CAAC,UAAU,IAAI,IAAI,KAAK,cAAc,CAAC,UAAU,IAAI,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;YAChH,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;gBACtD,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAU,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;aACnG;iBAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC7D,IAAI,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;gBACnF,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;iBAChF;gBACD,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,SAAS;iBACZ;gBAED,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAU,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;aACrG;SACJ;aAAM;YACH,MAAM,KAAK,GAAS,cAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,KAAK,EAAE;gBACR,SAAS;aACZ;YAED,IAAI,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;gBACpC,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC;gBAE7F,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;oBAC3C,SAAS;iBACZ;gBAEQ,cAAc,CAAC,SAAS,EAAG,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;aAClE;iBAAM;gBACH,SAAS,CAAC,UAAU,CAAS,cAAc,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC/E;SACJ;KACJ;IAED,SAAS,CAAC,cAAc,CAAC,CAAC;AAC9B,CAAC,CAAC;AAEF;;;;;;;GAOG;AACH,MAAM,6BAA6B,GAAG,CAClC,WAAyB,EACzB,cAA8B,EAC9B,SAAyB,EACzB,QAAuB,EACvB,iBAA6D,EAC/D,EAAE;IACA,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC;IAC/D,MAAM,iBAAiB,GAAG,SAAS,CAAC,QAAQ,CAAC;IAE7C;;OAEG;IACH,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE;QAClC,MAAM,OAAO,GAA4B,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACjE,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,KAAK,GAAS,cAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QAE3D,IAAI,KAAK,KAAK,SAAS,EAAE;YACrB,kDAAkD;YAClD,KAAK,GAAQ,OAAO,CAAC,KAAK,CAAC;SAC9B;QAED,IAAI,CAAC,KAAK,EAAE;YACR,SAAS;SACZ;QAED,MAAM,aAAa,GAAG,CAAC,WAA6B,EAAE,EAAE;YACpD,OAAO,CAAC,OAAgB,EAAE,EAAE;gBACxB,IAAI,OAAO,CAAC,KAAK,IAAI,WAAW,EAAE;oBAC9B,iBAAiB;oBACjB,cAAc,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;oBAChD,OAAO,iBAAiB,CAAC,WAAW,CAAC,CAAC;iBACzC;YACL,CAAC,CAAC;QACN,CAAC,CAAC;QAEF,sBAAsB;QACtB,IAAI,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;YACpC,mBAAmB,CAAC,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;SAC9I;QACD,SAAS;aACJ;YACD,IAAI,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBAC5G,iBAAiB;gBACjB,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAClC;SACJ;KACJ;AACL,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,oBAAoB,GAAG,CAAC,OAAqB,EAAE,cAA8B,EAAE,OAAkC,EAAE,EAAE;IACvH,OAAO,CAAC,MAAc,EAAE,KAAa,EAAE,EAAE;QACrC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC7B,OAAO,CAAC,+BAA+B,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,GAAG,KAAK,GAAG,oCAAoC,CAAC,CAAC;IACzH,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,sBAAsB,GAAG,CAC3B,WAAyB,EACzB,cAA8B,EAC9B,SAAyB,EACzB,QAAuB,EACvB,iBAA6D,EAC7D,SAAmD,EACrD,EAAE;IACA,OAAO,CAAC,CAAS,EAAE,EAAE;QACjB,6BAA6B,CAAC,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QAEnG,cAAc,CAAC,MAAM,GAAG,CAAC,IAAkB,EAAE,EAAE;YAC3C,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC/G,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,mBAAmB,GAAG,CAAC,SAAoB,EAAE,SAAyB,EAAE,iBAA6D,EAAU,EAAE;IACnJ,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,QAAQ,EAAE;QACnC,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzC,MAAM,gBAAgB,GAA4B,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEhF,IAAI,SAAS,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACtC,IAAI,gBAAgB,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;gBACjF,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAEzE,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;oBACvB,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAC/B,OAAO,iBAAiB,CAAC,cAAc,CAAC,CAAC;iBAC5C;aACJ;SACJ;KACJ;IAED,OAAO,SAAS,CAAC,iBAAiB,CAAC;AACvC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,eAAe,GAAG,CAAC,WAAyB,EAAE,EAAE;IAClD,mBAAmB;IACnB,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,SAAS,EAAE;QACrC,mBAAmB,CAAC,iBAAiB,CACjC,WAAW,EACX,GAAG,EACH,GAAG,EAAE,GAAE,CAAC,EACR,GAAG,EAAE,GAAE,CAAC,CACX,CAAC;KACL;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,cAAc;IAChB,MAAM,CAAC,aAAa,CAAC,UAAe,EAAE,KAAY,EAAE,OAAe;QACtE,MAAM,WAAW,GAAiB;YAC9B,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,EAAE;YACX,WAAW,EAAE,EAAE;YACf,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,EAAE;YACV,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAE;YACd,KAAK,EAAE,EAAE;YACT,cAAc,EAAE,EAAE;YAElB,MAAM,EAAE,EAAE;YAEV,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YAEf,KAAK,EAAE,KAAK;YACZ,OAAO,EAAE,OAAO;YAEhB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,EAAE;YAErB,iBAAiB,EAAE,CAAC;YAEpB,gBAAgB,EAAE,KAAK;YAEvB,UAAU,EAAE,EAAE;YAEd,cAAc,EAAE,IAAI;SACvB,CAAC;QAEF,QAAQ;QACR,IAAI,UAAU,CAAC,UAAU,EAAE;YACvB,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;SACjE;QAED,IAAI,UAAU,CAAC,cAAc,EAAE;YAC3B,WAAW,CAAC,UAAU,CAAC,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;SACzE;QAED,IAAI,UAAU,CAAC,OAAO,EAAE;YACpB,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;SACjD;QAED,IAAI,UAAU,CAAC,WAAW,EAAE;YACxB,WAAW,CAAC,UAAU,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;SACnE;QAED,IAAI,UAAU,CAAC,SAAS,EAAE;YACtB,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;SAC/D;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YACnB,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;SACzD;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YACnB,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;SACzD;QAED,IAAI,UAAU,CAAC,OAAO,EAAE;YACpB,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;SAC3D;QAED,IAAI,UAAU,CAAC,KAAK,EAAE;YAClB,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SACvD;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YACnB,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;SACzD;QAED,IAAI,UAAU,CAAC,QAAQ,EAAE;YACrB,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;SAC7D;QAED,IAAI,UAAU,CAAC,OAAO,EAAE;YACpB,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;SACjD;QAED,IAAI,UAAU,CAAC,QAAQ,EAAE;YACrB,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;SAC7D;QAED,IAAI,UAAU,CAAC,QAAQ,EAAE;YACrB,WAAW,CAAC,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;SAC7D;QAED,IAAI,UAAU,CAAC,UAAU,EAAE;YACvB,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;SACjE;QAED,IAAI,UAAU,CAAC,SAAS,EAAE;YACtB,WAAW,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;SAC/D;QAED,IAAI,UAAU,CAAC,UAAU,EAAE;YACvB,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;SACjE;QAED,IAAI,UAAU,CAAC,KAAK,EAAE;YAClB,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;SACvD;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YACnB,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;SAC1C;QAED,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;YACvC,WAAW,CAAC,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SAClE;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEM,MAAM,CAAC,eAAe,CACzB,WAAyB,EACzB,EAAU,EACV,SAA4C,EAC5C,OAAkC,EAClC,UAAuB;QAEvB,MAAM,MAAM,GAAgB,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEpD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC5B,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/E;aAAM;YACH,KAAK,CAAC,QAAQ,CACV,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,EAChC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,EACxD,UAAU,EACV,SAAS,EACT,IAAI,EACJ,CAAC,OAAO,EAAE,EAAE;gBACR,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;iBACtD;YACL,CAAC,CACJ,CAAC;SACL;IACL,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAsD,EAAE,OAAkC;QAClK,MAAM,OAAO,GAAiB,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,EAAE,CAAC,CAAC;YACZ,OAAO;SACV;QAED,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,SAAS,CAAC,IAAI,CAAC,CAAC;YAChB,OAAO;SACV;QAED,MAAM,MAAM,GAAe,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAE9D,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC5B,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/E;aAAM;YACH,KAAK,CAAC,QAAQ,CACV,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,EAChC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,EACxD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,CAAC,OAAO,EAAE,EAAE;gBACR,IAAI,OAAO,EAAE;oBACT,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;iBACtD;YACL,CAAC,CACJ,CAAC;SACL;IACL,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,WAAyB,EAAE,EAAU,EAAE,MAAiC,EAAE,SAAqC;QAC5I,MAAM,OAAO,GAAiB,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAEvD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAClC,OAAO;SACV;QAED,MAAM,OAAO,GAAiB,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpE,MAAM,aAAa,GACf,OAAO,CAAC,SAAS,KAAK,kBAAkB,CAAC,sBAAsB;YAC/D,OAAO,CAAC,SAAS,KAAK,kBAAkB,CAAC,qBAAqB;YAC9D,OAAO,CAAC,SAAS,KAAK,kBAAkB,CAAC,qBAAqB;YAC9D,OAAO,CAAC,SAAS,KAAK,kBAAkB,CAAC,oBAAoB,CAAC;QAElE,MAAM,YAAY,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAEnD,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,aAAa,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC7H,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC7B,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC3D;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC7B,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC3D;QACD,UAAU,CAAC,IAAI,GAAG,EAAE,CAAC;QAErB,OAAO,CAAC,cAAc,GAAG,UAAU,CAAC;QACpC,SAAS,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,qBAAqB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAuD,EAAE,OAAmC;QACnK,MAAM,MAAM,GAAgB,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEpD,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,IAAI,SAAS,EAAE;gBACX,SAAS,CAAC,YAAY,CAAC,CAAC;aAC3B;SACJ;aAAM;YACH,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,GAAG,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,OAAO,EAAE,EAAE;gBACjG,IAAI,OAAO,IAAI,OAAO,EAAE;oBACpB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;iBACtD;YACL,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAuC,EAAE,OAAkC;QAC9I,MAAM,QAAQ,GAAkB,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YACrB,IAAI,OAAO,EAAE;gBACT,OAAO,CAAC,qBAAqB,CAAC,CAAC;aAClC;YACD,OAAO;SACV;QAED,MAAM,SAAS,GAAmB,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7E,IAAI,CAAC,SAAS,EAAE;YACZ,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC;YACxE,MAAM,eAAe,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YACpE,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,cAAc,CAAC;YAC9D,WAAW,CAAC,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACjD,eAAe,CAAC,YAAY,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YACzD,eAAe,CAAC,eAAe,GAAG,QAAQ,CAAC,+BAA+B,CAAC;YAC3E,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3B,OAAO;SACV;QAED,MAAM,OAAO,GAAiB,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACtE,MAAM,MAAM,GAAyB,SAAS,CAAC,MAAM,CAAC;QAEtD,MAAM,YAAY,GAAW,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,CAAC;QACxF,MAAM,WAAW,GAAW,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC,CAAC;QACxF,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,MAAM,eAAe,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;QACpD,MAAM,cAAc,GAAG,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC;QAElD,MAAM,iBAAiB,GAA+C,EAAE,CAAC;QACzE,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,yCAAyC;QACzC,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,QAAQ,EAAE;YACnC,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,gBAAgB,GAA4B,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAEhF,iBAAiB,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;YAE3C,IAAI,gBAAgB,CAAC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;gBACjF,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACzE,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE;oBACvB,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC;oBACjD,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;iBAClC;qBAAM;oBACH,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACvB;aACJ;iBAAM,IAAI,gBAAgB,CAAC,IAAI,KAAK,cAAc,CAAC,UAAU,EAAE;gBAC5D,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACvB;iBAAM;gBACH,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACvB;SACJ;QAED,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,UAAU,EAAE;YACrC,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,kBAAkB,GAA4B,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAEpF,IAAI,kBAAkB,CAAC,QAAQ,EAAE;gBAC7B,MAAM,IAAI,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC;gBAC9C,IAAI,IAAI,EAAE;oBACN,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACzB;aACJ;SACJ;QAED,0BAA0B;QAC1B,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,eAAe,CAAC,YAAY,EAAE,EAAE;YAC/D,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC;YAE/C,IAAI,SAAS,KAAK,UAAU,CAAC,UAAU,EAAE;gBACrC,eAAe,IAAI,eAAe,CAAC,aAAa,CAAC;gBACjD,SAAS;aACZ;YAED,IAAI,cAAc,GAAG,KAAK,CAAC;YAE3B,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,UAAU,EAAE;gBACrC,MAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,kBAAkB,GAA4B,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAEpF,IAAI,eAAe,CAAC,iBAAiB,KAAK,IAAI,IAAI,kBAAkB,CAAC,QAAQ,EAAE;oBAC3E,eAAe,IAAI,YAAY,CAAC,kBAAkB,CAAC,CAAC;oBACpD,cAAc,GAAG,IAAI,CAAC;oBACtB,MAAM;iBACT;aACJ;YAED,IAAI,cAAc,EAAE;gBAChB,SAAS;aACZ;YAED,eAAe,IAAI,mBAAmB,CAAC,eAAe,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;SACzF;QAED,yBAAyB;QACzB,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,cAAc,CAAC,YAAY,EAAE,EAAE;YAC7D,MAAM,SAAS,GAAG,cAAc,CAAC,YAAY,CAAC;YAE9C,IAAI,SAAS,KAAK,UAAU,CAAC,UAAU,EAAE;gBACrC,cAAc,IAAI,cAAc,CAAC,aAAa,CAAC;gBAC/C,SAAS;aACZ;YAED,cAAc,IAAI,mBAAmB,CAAC,cAAc,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;SACvF;QAED,yBAAyB;QACzB,MAAM,UAAU,GAAG;YACf,MAAM,EAAE,OAAO,CAAC,YAAY,GAAG,EAAE;YACjC,QAAQ,EAAE,OAAO,CAAC,cAAc,GAAG,EAAE;SACxC,CAAC;QAEF,MAAM,OAAO,GAAG;YACZ,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,QAAQ;YAClB,iBAAiB,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnF,CAAC;QAEF,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,GAAG,EAAE,GAAG,cAAc,CAAC,GAAG,eAAe,CAAC;QAClF,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,GAAG,aAAa,CAAC,GAAG,cAAc,CAAC;QAElF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QACtF,cAAc,CAAC,OAAO,GAAG,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAChF,cAAc,CAAC,UAAU,GAAG,sBAAsB,CAAC,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,CAAC,CAAC;QACnI,cAAc,CAAC,eAAe,GAAG,QAAQ,CAAC,+BAA+B,CAAC;QAE1E,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE;YAC5B,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;YACnC,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,EAAE;gBACnE,cAAc,CAAC,eAAe,GAAG,KAAK,CAAC;aAC1C;YAED,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC;YAC9C,IAAI,SAAS,EAAE;gBACX,IACI,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,SAAS;oBAC5C,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,mBAAmB;oBACtD,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC;iBACtD;qBAAM,IACH,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,IAAI;oBACvC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC;iBACrD;qBAAM,IACH,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,SAAS;oBAC5C,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,IAAI;oBACvC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;iBAClD;qBAAM,IACH,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,IAAI;oBACvC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,mBAAmB;oBACtD,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,cAAc,CAAC;iBACvD;qBAAM,IACH,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,SAAS;oBAC5C,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,IAAI;oBACvC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,cAAc,CAAC;iBACvD;qBAAM,IACH,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,SAAS;oBAC5C,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,mBAAmB;oBACtD,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG;oBACtC,SAAS,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,GAAG,EACxC;oBACE,cAAc,CAAC,SAAS,GAAG,SAAS,CAAC,eAAe,CAAC;iBACxD;aACJ;SACJ;IACL,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,UAAU;IAGZ,MAAM,CAAC,iBAAiB,CAAC,SAA8B;QAC1D,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACvC,KAAK,CAAC,KAAK,CAAC,2BAA2B,GAAG,SAAS,CAAC,IAAI,GAAG,kBAAkB,CAAC,CAAC;YAC/E,OAAO;SACV;QAED,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;IACtD,CAAC;IAEM,OAAO;QACV,aAAa;IACjB,CAAC;IAEO,gBAAgB,CACpB,WAAgB,EAChB,KAAY,EACZ,IAAqB,EACrB,OAAe,EACf,cAAwC,EACxC,SAAkE,EAClE,UAAuD,EACvD,OAAmC;QAEnC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAElC,mBAAmB,CAAC,gBAAgB,CAChC,KAAK,EACL,IAAI,EACJ,OAAO,EACP,CAAC,WAAW,EAAE,EAAE;YACZ,WAAW,CAAC,cAAc,GAAG,cAAc,CAAC;YAC5C,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAEpC,IAAI,WAAW,KAAK,EAAE,EAAE;gBACpB,WAAW,CAAC,iBAAiB,GAAG,EAAE,CAAC;aACtC;iBAAM,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;gBACxC,WAAW,CAAC,iBAAiB,GAAG,CAAC,WAAW,CAAC,CAAC;aACjD;iBAAM,IAAI,WAAW,IAAI,CAAC,CAAC,WAAW,YAAY,KAAK,CAAC,EAAE;gBACvD,WAAW,CAAC,iBAAiB,GAAG,CAAC,WAAW,CAAC,CAAC;aACjD;iBAAM;gBACH,WAAW,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBACnC,KAAK,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;aACzE;YAED,eAAe;YACf,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAE/B,MAAM,MAAM,GAAG,IAAI,KAAK,EAAgB,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAY,CAAC;YAExC,sCAAsC;YACtC,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;gBACjC,MAAM,IAAI,GAAc,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAE/C,IAAI,IAAI,CAAC,WAAW,YAAY,YAAY,EAAE;oBAC1C,MAAM,CAAC,IAAI,CAAe,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC/C;aACJ;YAED,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;gBACjC,MAAM,IAAI,GAAe,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAEhD,IAAI,IAAI,CAAC,eAAe,YAAY,QAAQ,EAAE;oBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;iBACxC;aACJ;YAED,yCAAyC;YACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,GAAG,EAAE;gBACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,GAAG,EAAE;oBACrC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAC7B,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAEtB,IAAI,CAAC,cAAc,CAAC,kBAAkB,IAAI,SAAS,EAAE;wBACjD,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;qBAChC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YAEH,IAAI,cAAc,CAAC,kBAAkB,IAAI,SAAS,EAAE;gBAChD,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;aAChC;QACL,CAAC,EACD,OAAO,CACV,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,eAAe,CAClB,WAAgB,EAChB,KAAY,EACZ,cAAwC,EACxC,IAAqB,EACrB,OAAe,EACf,UAAuD;QAEvD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,gBAAgB,CACjB,WAAW,EACX,KAAK,EACL,IAAI,EACJ,OAAO,EACP,cAAc,EACd,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;gBAClB,OAAO,CAAC;oBACJ,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,EAAE;oBACnB,SAAS,EAAE,SAAS;oBACpB,eAAe,EAAE,EAAE;oBACnB,MAAM,EAAE,EAAE;oBACV,cAAc,EAAE,EAAE;oBAClB,UAAU,EAAE,EAAE;iBACjB,CAAC,CAAC;YACP,CAAC,EACD,UAAU,EACV,CAAC,OAAO,EAAE,EAAE;gBACR,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/B,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,UAAU,CACd,KAAY,EACZ,IAAqB,EACrB,OAAe,EACf,SAAqB,EACrB,UAAuD,EACvD,OAAmC;QAEnC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAElC,mBAAmB,CAAC,gBAAgB,CAChC,KAAK,EACL,IAAI,EACJ,OAAO,EACP,CAAC,WAAW,EAAE,EAAE;YACZ,yBAAyB;YACzB,mBAAmB,CAAC,0BAA0B,CAC1C,WAAW,EACX,GAAG,EAAE;gBACD,eAAe;gBACf,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAE/B,yCAAyC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,GAAG,EAAE;oBACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,GAAG,EAAE;wBACrC,eAAe,CAAC,WAAW,CAAC,CAAC;wBAC7B,QAAQ,CAAC,WAAW,CAAC,CAAC;wBAEtB,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE;4BACpC,SAAS,EAAE,CAAC;yBACf;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;gBAEH,IAAI,cAAc,CAAC,kBAAkB,EAAE;oBACnC,SAAS,EAAE,CAAC;iBACf;YACL,CAAC,EACD,OAAO,CACV,CAAC;QACN,CAAC,EACD,OAAO,CACV,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,KAAY,EAAE,IAAqB,EAAE,OAAe,EAAE,UAAuD;QAC1H,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,UAAU,CACX,KAAK,EACL,IAAI,EACJ,OAAO,EACP,GAAG,EAAE;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,EACD,UAAU,EACV,CAAC,OAAO,EAAE,EAAE;gBACR,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC/B,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,iBAAiB,CAAC,WAAyB,EAAE,MAAkB;QACnE,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,MAAmB,EAAE,EAAE;YACvD,mBAAmB,CAAC,qBAAqB,CACrC,WAAW,EACX,GAAG,EACH,CAAC,YAAY,EAAE,EAAE;gBACb,IAAI,YAAY,YAAY,WAAW,EAAE;oBACrC,OAAO;iBACV;gBAED,WAAW,CAAC,iBAAiB,EAAE,CAAC;gBAEhC,IAAI,YAAY,EAAE;oBACd,MAAM,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,GAAG,YAAY,CAAC;iBACnH;gBAED,IAAI,WAAW,CAAC,iBAAiB,KAAK,WAAW,CAAC,YAAY,EAAE;oBAC5D,MAAM,EAAE,CAAC;iBACZ;YACL,CAAC,EACD,GAAG,EAAE;gBACD,KAAK,CAAC,KAAK,CAAC,0CAA0C,GAAG,GAAG,GAAG,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAChG,CAAC,CACJ,CAAC;QACN,CAAC,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE;YACnC,UAAU,GAAG,IAAI,CAAC;YAElB,MAAM,MAAM,GAAgB,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,MAAM,EAAE;gBACR,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;aAC3C;iBAAM;gBACH,KAAK,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,CAAC;aAC1C;SACJ;QAED,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,EAAE,CAAC;SACZ;IACL,CAAC;IAEO,iBAAiB,CAAC,WAAyB,EAAE,MAAkB;QACnE,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,MAAmB,EAAE,EAAE;YACvD,mBAAmB,CAAC,eAAe,CAC/B,WAAW,EACX,GAAG,EACH,CAAC,UAAU,EAAE,EAAE;gBACX,WAAW,CAAC,iBAAiB,EAAE,CAAC;gBAEhC,IAAI,UAAU,EAAE;oBACZ,IAAI,UAAU,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE;wBAC9D,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,GAAG,aAAa,GAAG,UAAU,CAAC,UAAU,GAAG,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,wBAAwB;qBAC5I;oBAED,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;iBACnD;gBAED,IAAI,WAAW,CAAC,iBAAiB,KAAK,WAAW,CAAC,YAAY,EAAE;oBAC5D,MAAM,EAAE,CAAC;iBACZ;YACL,CAAC,EACD,GAAG,EAAE;gBACD,KAAK,CAAC,KAAK,CAAC,kCAAkC,GAAG,GAAG,GAAG,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YACxF,CAAC,CACJ,CAAC;QACN,CAAC,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE;YACnC,UAAU,GAAG,IAAI,CAAC;YAElB,MAAM,MAAM,GAAgB,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,MAAM,EAAE;gBACR,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;aAC3C;iBAAM;gBACH,KAAK,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,CAAC;aAC1C;SACJ;QAED,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,EAAE,CAAC;SACZ;IACL,CAAC;IAEO,YAAY,CAAC,WAAyB;QAC1C,IAAI,YAAY,GAAe,WAAW,CAAC,YAAY,CAAC;QAExD,IAAI,YAAY,EAAE;YACd,qDAAqD;YACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC3D;SACJ;aAAM;YACH,kBAAkB;YAClB,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE;gBACpC,YAAY,GAAe,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAErD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAChD,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBAC3D;aACJ;SACJ;IACL,CAAC;;AAvTa,qBAAU,GAA4C,EAAE,CAAC;AA0T3E,gBAAgB;AAChB,MAAM,OAAgB,mBAAmB;IAGrC,YAAmB,IAAY;QAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;;;;;;OAQG;IACI,gBAAgB,CAAC,KAAY,EAAE,IAAqB,EAAE,OAAe,EAAE,SAA+C,EAAE,OAAmC;QAC9J,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,0BAA0B,CAAC,WAAyB,EAAE,SAAqB,EAAE,OAAmC;QACnH,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACI,eAAe,CAClB,WAAyB,EACzB,EAAU,EACV,SAA4C,EAC5C,OAAkC,EAClC,UAAuB;QAEvB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,sBAAsB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAA4C,EAAE,OAAkC;QACjJ,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACI,kBAAkB,CAAC,WAAyB,EAAE,EAAU,EAAE,MAAuB,EAAE,SAAqC,EAAE,OAAkC;QAC/J,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,qBAAqB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAyC,EAAE,OAAkC;QAC7I,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,iBAAiB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAuC,EAAE,OAAkC;QACvI,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,YAAY;IACZ,YAAY;IACZ,YAAY;IAEL,MAAM,CAAC,gBAAgB,CAC1B,KAAY,EACZ,IAAqB,EACrB,OAAe,EACf,SAA+C,EAC/C,OAAmC;QAEnC,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACtF,CAAC,EACD,GAAG,EAAE;YACD,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,SAAS,EAAE;oBACZ,OAAO;iBACV;gBACD,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACP,CAAC,CACJ,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,0BAA0B,CAAC,WAAyB,EAAE,SAAqB,EAAE,OAAmC;QAC1H,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,0BAA0B,CAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACvF,CAAC,EACD,GAAG,EAAE;YACD,UAAU,CAAC,GAAG,EAAE;gBACZ,SAAS,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACP,CAAC,CACJ,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,eAAe,CACzB,WAAyB,EACzB,EAAU,EACV,SAAgD,EAChD,OAAkC,EAClC,UAAuB;QAEvB,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC5F,CAAC,EACD,GAAG,EAAE;YACD,cAAc,CAAC,eAAe,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QACpF,CAAC,CACJ,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAqC,EAAE,OAAkC;QAC3I,mBAAmB,CAAC,uBAAuB,CACvC,WAAW,EACX,EAAE,EACF,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,MAAM,EAAE;gBACR,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;aACxF;QACL,CAAC,EACD,OAAO,CACV,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,qBAAqB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAqD,EAAE,OAAkC;QAChK,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACtF,CAAC,EACD,GAAG,EAAE;YACD,cAAc,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC9E,CAAC,CACJ,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAuC,EAAE,OAAkC;QAC9I,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAClF,CAAC,EACD,GAAG,EAAE;YACD,cAAc,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC1E,CAAC,CACJ,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAClC,WAAyB,EACzB,EAAU,EACV,SAAsD,EACtD,OAAkC;QAElC,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACvF,CAAC,EACD,GAAG,EAAE;YACD,cAAc,CAAC,sBAAsB,CAAC,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/E,CAAC,CACJ,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAC9B,WAAyB,EACzB,EAAU,EACV,MAAuB,EACvB,SAAqC,EACrC,OAAkC;QAElC,mBAAmB,CAAC,gBAAgB,CAChC,CAAC,eAAe,EAAE,EAAE;YAChB,OAAO,eAAe,CAAC,kBAAkB,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC3F,CAAC,EACD,GAAG,EAAE;YACD,cAAc,CAAC,kBAAkB,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAC1E,CAAC,CACJ,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAuD,EAAE,WAAuB;QAC5G,KAAK,MAAM,aAAa,IAAI,UAAU,CAAC,UAAU,EAAE;YAC/C,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YAC7D,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE;gBACvB,OAAO;aACV;SACJ;QAED,WAAW,EAAE,CAAC;IAClB,CAAC;CACJ;AAED,cAAc,CAAC,kBAAkB,GAAG,GAAG,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type {\r\n    IGLTFRuntime,\r\n    IGLTFTechniqueParameter,\r\n    IGLTFAnimation,\r\n    IGLTFAnimationSampler,\r\n    IGLTFNode,\r\n    IGLTFSkins,\r\n    INodeToRoot,\r\n    IJointNode,\r\n    IGLTFMesh,\r\n    IGLTFAccessor,\r\n    IGLTFLight,\r\n    IGLTFAmbienLight,\r\n    IGLTFDirectionalLight,\r\n    IGLTFPointLight,\r\n    IGLTFSpotLight,\r\n    IGLTFCamera,\r\n    IGLTFCameraPerspective,\r\n    IGLTFScene,\r\n    IGLTFTechnique,\r\n    IGLTFMaterial,\r\n    IGLTFProgram,\r\n    IGLTFBuffer,\r\n    IGLTFTexture,\r\n    IGLTFImage,\r\n    IGLTFSampler,\r\n    IGLTFShader,\r\n    IGLTFTechniqueStates,\r\n} from \"./glTFLoaderInterfaces\";\r\nimport { EParameterType, ETextureFilterType, ECullingType, EBlendingFunction, EShaderType } from \"./glTFLoaderInterfaces\";\r\n\r\nimport type { FloatArray, Nullable } from \"core/types\";\r\nimport { Quaternion, Vector3, Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport { Camera } from \"core/Cameras/camera\";\r\nimport { FreeCamera } from \"core/Cameras/freeCamera\";\r\nimport { Animation } from \"core/Animations/animation\";\r\nimport { Bone } from \"core/Bones/bone\";\r\nimport { Skeleton } from \"core/Bones/skeleton\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport { Material } from \"core/Materials/material\";\r\nimport { MultiMaterial } from \"core/Materials/multiMaterial\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport { ShaderMaterial } from \"core/Materials/shaderMaterial\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { Node } from \"core/node\";\r\nimport { VertexData } from \"core/Meshes/mesh.vertexData\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Geometry } from \"core/Meshes/geometry\";\r\nimport { SubMesh } from \"core/Meshes/subMesh\";\r\nimport { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { Mesh } from \"core/Meshes/mesh\";\r\nimport { HemisphericLight } from \"core/Lights/hemisphericLight\";\r\nimport { DirectionalLight } from \"core/Lights/directionalLight\";\r\nimport { PointLight } from \"core/Lights/pointLight\";\r\nimport { SpotLight } from \"core/Lights/spotLight\";\r\nimport type { ISceneLoaderAsyncResult, ISceneLoaderProgressEvent } from \"core/Loading/sceneLoader\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\nimport { GLTFUtils } from \"./glTFLoaderUtils\";\r\nimport type { IGLTFLoader, IGLTFLoaderData } from \"../glTFFileLoader\";\r\nimport { GLTFFileLoader } from \"../glTFFileLoader\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { AssetContainer } from \"core/assetContainer\";\r\n\r\n/**\r\n * Tokenizer. Used for shaders compatibility\r\n * Automatically map world, view, projection, worldViewProjection, attributes and so on\r\n */\r\nenum ETokenType {\r\n    IDENTIFIER = 1,\r\n\r\n    UNKNOWN = 2,\r\n    END_OF_INPUT = 3,\r\n}\r\n\r\nclass Tokenizer {\r\n    private _toParse: string;\r\n    private _pos: number = 0;\r\n    private _maxPos: number;\r\n\r\n    public currentToken: ETokenType = ETokenType.UNKNOWN;\r\n    public currentIdentifier: string = \"\";\r\n    public currentString: string = \"\";\r\n    public isLetterOrDigitPattern: RegExp = /^[a-zA-Z0-9]+$/;\r\n\r\n    constructor(toParse: string) {\r\n        this._toParse = toParse;\r\n        this._maxPos = toParse.length;\r\n    }\r\n\r\n    public getNextToken(): ETokenType {\r\n        if (this.isEnd()) {\r\n            return ETokenType.END_OF_INPUT;\r\n        }\r\n\r\n        this.currentString = this.read();\r\n        this.currentToken = ETokenType.UNKNOWN;\r\n\r\n        if (this.currentString === \"_\" || this.isLetterOrDigitPattern.test(this.currentString)) {\r\n            this.currentToken = ETokenType.IDENTIFIER;\r\n            this.currentIdentifier = this.currentString;\r\n            while (!this.isEnd() && (this.isLetterOrDigitPattern.test((this.currentString = this.peek())) || this.currentString === \"_\")) {\r\n                this.currentIdentifier += this.currentString;\r\n                this.forward();\r\n            }\r\n        }\r\n\r\n        return this.currentToken;\r\n    }\r\n\r\n    public peek(): string {\r\n        return this._toParse[this._pos];\r\n    }\r\n\r\n    public read(): string {\r\n        return this._toParse[this._pos++];\r\n    }\r\n\r\n    public forward(): void {\r\n        this._pos++;\r\n    }\r\n\r\n    public isEnd(): boolean {\r\n        return this._pos >= this._maxPos;\r\n    }\r\n}\r\n\r\n/**\r\n * Values\r\n */\r\nconst glTFTransforms = [\"MODEL\", \"VIEW\", \"PROJECTION\", \"MODELVIEW\", \"MODELVIEWPROJECTION\", \"JOINTMATRIX\"];\r\nconst babylonTransforms = [\"world\", \"view\", \"projection\", \"worldView\", \"worldViewProjection\", \"mBones\"];\r\n\r\nconst glTFAnimationPaths = [\"translation\", \"rotation\", \"scale\"];\r\nconst babylonAnimationPaths = [\"position\", \"rotationQuaternion\", \"scaling\"];\r\n\r\n/**\r\n * Parse\r\n * @param parsedBuffers\r\n * @param gltfRuntime\r\n */\r\nconst parseBuffers = (parsedBuffers: any, gltfRuntime: IGLTFRuntime) => {\r\n    for (const buf in parsedBuffers) {\r\n        const parsedBuffer = parsedBuffers[buf];\r\n        gltfRuntime.buffers[buf] = parsedBuffer;\r\n        gltfRuntime.buffersCount++;\r\n    }\r\n};\r\n\r\nconst parseShaders = (parsedShaders: any, gltfRuntime: IGLTFRuntime) => {\r\n    for (const sha in parsedShaders) {\r\n        const parsedShader = parsedShaders[sha];\r\n        gltfRuntime.shaders[sha] = parsedShader;\r\n        gltfRuntime.shaderscount++;\r\n    }\r\n};\r\n\r\nconst parseObject = (parsedObjects: any, runtimeProperty: string, gltfRuntime: IGLTFRuntime) => {\r\n    for (const object in parsedObjects) {\r\n        const parsedObject = parsedObjects[object];\r\n        (<any>gltfRuntime)[runtimeProperty][object] = parsedObject;\r\n    }\r\n};\r\n\r\n/**\r\n * Utils\r\n * @param buffer\r\n */\r\nconst normalizeUVs = (buffer: any) => {\r\n    if (!buffer) {\r\n        return;\r\n    }\r\n\r\n    for (let i = 0; i < buffer.length / 2; i++) {\r\n        buffer[i * 2 + 1] = 1.0 - buffer[i * 2 + 1];\r\n    }\r\n};\r\n\r\nconst getAttribute = (attributeParameter: IGLTFTechniqueParameter): Nullable<string> => {\r\n    if (attributeParameter.semantic === \"NORMAL\") {\r\n        return \"normal\";\r\n    } else if (attributeParameter.semantic === \"POSITION\") {\r\n        return \"position\";\r\n    } else if (attributeParameter.semantic === \"JOINT\") {\r\n        return \"matricesIndices\";\r\n    } else if (attributeParameter.semantic === \"WEIGHT\") {\r\n        return \"matricesWeights\";\r\n    } else if (attributeParameter.semantic === \"COLOR\") {\r\n        return \"color\";\r\n    } else if (attributeParameter.semantic && attributeParameter.semantic.indexOf(\"TEXCOORD_\") !== -1) {\r\n        const channel = Number(attributeParameter.semantic.split(\"_\")[1]);\r\n        return \"uv\" + (channel === 0 ? \"\" : channel + 1);\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n/**\r\n * Loads and creates animations\r\n * @param gltfRuntime\r\n */\r\nconst loadAnimations = (gltfRuntime: IGLTFRuntime) => {\r\n    for (const anim in gltfRuntime.animations) {\r\n        const animation: IGLTFAnimation = gltfRuntime.animations[anim];\r\n\r\n        if (!animation.channels || !animation.samplers) {\r\n            continue;\r\n        }\r\n\r\n        let lastAnimation: Nullable<Animation> = null;\r\n\r\n        for (let i = 0; i < animation.channels.length; i++) {\r\n            // Get parameters and load buffers\r\n            const channel = animation.channels[i];\r\n            const sampler: IGLTFAnimationSampler = animation.samplers[channel.sampler];\r\n\r\n            if (!sampler) {\r\n                continue;\r\n            }\r\n\r\n            let inputData: Nullable<string> = null;\r\n            let outputData: Nullable<string> = null;\r\n\r\n            if (animation.parameters) {\r\n                inputData = animation.parameters[sampler.input];\r\n                outputData = animation.parameters[sampler.output];\r\n            } else {\r\n                inputData = sampler.input;\r\n                outputData = sampler.output;\r\n            }\r\n\r\n            const bufferInput = GLTFUtils.GetBufferFromAccessor(gltfRuntime, gltfRuntime.accessors[inputData]);\r\n            const bufferOutput = GLTFUtils.GetBufferFromAccessor(gltfRuntime, gltfRuntime.accessors[outputData]);\r\n\r\n            const targetId = channel.target.id;\r\n            let targetNode: any = gltfRuntime.scene.getNodeById(targetId);\r\n\r\n            if (targetNode === null) {\r\n                targetNode = gltfRuntime.scene.getNodeByName(targetId);\r\n            }\r\n\r\n            if (targetNode === null) {\r\n                Tools.Warn(\"Creating animation named \" + anim + \". But cannot find node named \" + targetId + \" to attach to\");\r\n                continue;\r\n            }\r\n\r\n            const isBone = targetNode instanceof Bone;\r\n\r\n            // Get target path (position, rotation or scaling)\r\n            let targetPath = channel.target.path;\r\n            const targetPathIndex = glTFAnimationPaths.indexOf(targetPath);\r\n\r\n            if (targetPathIndex !== -1) {\r\n                targetPath = babylonAnimationPaths[targetPathIndex];\r\n            }\r\n\r\n            // Determine animation type\r\n            let animationType = Animation.ANIMATIONTYPE_MATRIX;\r\n\r\n            if (!isBone) {\r\n                if (targetPath === \"rotationQuaternion\") {\r\n                    animationType = Animation.ANIMATIONTYPE_QUATERNION;\r\n                    targetNode.rotationQuaternion = new Quaternion();\r\n                } else {\r\n                    animationType = Animation.ANIMATIONTYPE_VECTOR3;\r\n                }\r\n            }\r\n\r\n            // Create animation and key frames\r\n            let babylonAnimation: Nullable<Animation> = null;\r\n            const keys = [];\r\n            let arrayOffset = 0;\r\n            let modifyKey = false;\r\n\r\n            if (isBone && lastAnimation && lastAnimation.getKeys().length === bufferInput.length) {\r\n                babylonAnimation = lastAnimation;\r\n                modifyKey = true;\r\n            }\r\n\r\n            if (!modifyKey) {\r\n                gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n                babylonAnimation = new Animation(anim, isBone ? \"_matrix\" : targetPath, 1, animationType, Animation.ANIMATIONLOOPMODE_CYCLE);\r\n                gltfRuntime.scene._blockEntityCollection = false;\r\n            }\r\n\r\n            // For each frame\r\n            for (let j = 0; j < bufferInput.length; j++) {\r\n                let value: any = null;\r\n\r\n                if (targetPath === \"rotationQuaternion\") {\r\n                    // VEC4\r\n                    value = Quaternion.FromArray([bufferOutput[arrayOffset], bufferOutput[arrayOffset + 1], bufferOutput[arrayOffset + 2], bufferOutput[arrayOffset + 3]]);\r\n                    arrayOffset += 4;\r\n                } else {\r\n                    // Position and scaling are VEC3\r\n                    value = Vector3.FromArray([bufferOutput[arrayOffset], bufferOutput[arrayOffset + 1], bufferOutput[arrayOffset + 2]]);\r\n                    arrayOffset += 3;\r\n                }\r\n\r\n                if (isBone) {\r\n                    const bone = <Bone>targetNode;\r\n                    let translation = Vector3.Zero();\r\n                    let rotationQuaternion = new Quaternion();\r\n                    let scaling = Vector3.Zero();\r\n\r\n                    // Warning on decompose\r\n                    let mat = bone.getBaseMatrix();\r\n\r\n                    if (modifyKey && lastAnimation) {\r\n                        mat = lastAnimation.getKeys()[j].value;\r\n                    }\r\n\r\n                    mat.decompose(scaling, rotationQuaternion, translation);\r\n\r\n                    if (targetPath === \"position\") {\r\n                        translation = value;\r\n                    } else if (targetPath === \"rotationQuaternion\") {\r\n                        rotationQuaternion = value;\r\n                    } else {\r\n                        scaling = value;\r\n                    }\r\n\r\n                    value = Matrix.Compose(scaling, rotationQuaternion, translation);\r\n                }\r\n\r\n                if (!modifyKey) {\r\n                    keys.push({\r\n                        frame: bufferInput[j],\r\n                        value: value,\r\n                    });\r\n                } else if (lastAnimation) {\r\n                    lastAnimation.getKeys()[j].value = value;\r\n                }\r\n            }\r\n\r\n            // Finish\r\n            if (!modifyKey && babylonAnimation) {\r\n                babylonAnimation.setKeys(keys);\r\n                targetNode.animations.push(babylonAnimation);\r\n            }\r\n\r\n            lastAnimation = babylonAnimation;\r\n\r\n            gltfRuntime.scene.stopAnimation(targetNode);\r\n            gltfRuntime.scene.beginAnimation(targetNode, 0, bufferInput[bufferInput.length - 1], true, 1.0);\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * Returns the bones transformation matrix\r\n * @param node\r\n */\r\nconst configureBoneTransformation = (node: IGLTFNode): Matrix => {\r\n    let mat: Nullable<Matrix> = null;\r\n\r\n    if (node.translation || node.rotation || node.scale) {\r\n        const scale = Vector3.FromArray(node.scale || [1, 1, 1]);\r\n        const rotation = Quaternion.FromArray(node.rotation || [0, 0, 0, 1]);\r\n        const position = Vector3.FromArray(node.translation || [0, 0, 0]);\r\n\r\n        mat = Matrix.Compose(scale, rotation, position);\r\n    } else {\r\n        mat = Matrix.FromArray(node.matrix);\r\n    }\r\n\r\n    return mat;\r\n};\r\n\r\n/**\r\n * Returns the parent bone\r\n * @param gltfRuntime\r\n * @param skins\r\n * @param jointName\r\n * @param newSkeleton\r\n */\r\nconst getParentBone = (gltfRuntime: IGLTFRuntime, skins: IGLTFSkins, jointName: string, newSkeleton: Skeleton): Nullable<Bone> => {\r\n    // Try to find\r\n    for (let i = 0; i < newSkeleton.bones.length; i++) {\r\n        if (newSkeleton.bones[i].name === jointName) {\r\n            return newSkeleton.bones[i];\r\n        }\r\n    }\r\n\r\n    // Not found, search in gltf nodes\r\n    const nodes = gltfRuntime.nodes;\r\n    for (const nde in nodes) {\r\n        const node: IGLTFNode = nodes[nde];\r\n\r\n        if (!node.jointName) {\r\n            continue;\r\n        }\r\n\r\n        const children = node.children;\r\n        for (let i = 0; i < children.length; i++) {\r\n            const child: IGLTFNode = gltfRuntime.nodes[children[i]];\r\n            if (!child.jointName) {\r\n                continue;\r\n            }\r\n\r\n            if (child.jointName === jointName) {\r\n                const mat = configureBoneTransformation(node);\r\n                const bone = new Bone(node.name || \"\", newSkeleton, getParentBone(gltfRuntime, skins, node.jointName, newSkeleton), mat);\r\n                bone.id = nde;\r\n                return bone;\r\n            }\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n/**\r\n * Returns the appropriate root node\r\n * @param nodesToRoot\r\n * @param id\r\n */\r\nconst getNodeToRoot = (nodesToRoot: INodeToRoot[], id: string): Nullable<Bone> => {\r\n    for (let i = 0; i < nodesToRoot.length; i++) {\r\n        const nodeToRoot = nodesToRoot[i];\r\n\r\n        for (let j = 0; j < nodeToRoot.node.children.length; j++) {\r\n            const child = nodeToRoot.node.children[j];\r\n            if (child === id) {\r\n                return nodeToRoot.bone;\r\n            }\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n/**\r\n * Returns the node with the joint name\r\n * @param gltfRuntime\r\n * @param jointName\r\n */\r\nconst getJointNode = (gltfRuntime: IGLTFRuntime, jointName: string): Nullable<IJointNode> => {\r\n    const nodes = gltfRuntime.nodes;\r\n    let node: IGLTFNode = nodes[jointName];\r\n    if (node) {\r\n        return {\r\n            node: node,\r\n            id: jointName,\r\n        };\r\n    }\r\n\r\n    for (const nde in nodes) {\r\n        node = nodes[nde];\r\n        if (node.jointName === jointName) {\r\n            return {\r\n                node: node,\r\n                id: nde,\r\n            };\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n/**\r\n * Checks if a nodes is in joints\r\n * @param skins\r\n * @param id\r\n */\r\nconst nodeIsInJoints = (skins: IGLTFSkins, id: string): boolean => {\r\n    for (let i = 0; i < skins.jointNames.length; i++) {\r\n        if (skins.jointNames[i] === id) {\r\n            return true;\r\n        }\r\n    }\r\n\r\n    return false;\r\n};\r\n\r\n/**\r\n * Fills the nodes to root for bones and builds hierarchy\r\n * @param gltfRuntime\r\n * @param newSkeleton\r\n * @param skins\r\n * @param nodesToRoot\r\n */\r\nconst getNodesToRoot = (gltfRuntime: IGLTFRuntime, newSkeleton: Skeleton, skins: IGLTFSkins, nodesToRoot: INodeToRoot[]) => {\r\n    // Creates nodes for root\r\n    for (const nde in gltfRuntime.nodes) {\r\n        const node: IGLTFNode = gltfRuntime.nodes[nde];\r\n        const id = nde;\r\n\r\n        if (!node.jointName || nodeIsInJoints(skins, node.jointName)) {\r\n            continue;\r\n        }\r\n\r\n        // Create node to root bone\r\n        const mat = configureBoneTransformation(node);\r\n        const bone = new Bone(node.name || \"\", newSkeleton, null, mat);\r\n        bone.id = id;\r\n        nodesToRoot.push({ bone: bone, node: node, id: id });\r\n    }\r\n\r\n    // Parenting\r\n    for (let i = 0; i < nodesToRoot.length; i++) {\r\n        const nodeToRoot = nodesToRoot[i];\r\n        const children = nodeToRoot.node.children;\r\n\r\n        for (let j = 0; j < children.length; j++) {\r\n            let child: Nullable<INodeToRoot> = null;\r\n\r\n            for (let k = 0; k < nodesToRoot.length; k++) {\r\n                if (nodesToRoot[k].id === children[j]) {\r\n                    child = nodesToRoot[k];\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (child) {\r\n                (<any>child.bone)._parent = nodeToRoot.bone;\r\n                nodeToRoot.bone.children.push(child.bone);\r\n            }\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * Imports a skeleton\r\n * @param gltfRuntime\r\n * @param skins\r\n * @param mesh\r\n * @param newSkeleton\r\n */\r\nconst importSkeleton = (gltfRuntime: IGLTFRuntime, skins: IGLTFSkins, mesh: Mesh, newSkeleton: Skeleton | undefined): Skeleton => {\r\n    if (!newSkeleton) {\r\n        newSkeleton = new Skeleton(skins.name || \"\", \"\", gltfRuntime.scene);\r\n    }\r\n\r\n    if (!skins.babylonSkeleton) {\r\n        return newSkeleton;\r\n    }\r\n\r\n    // Find the root bones\r\n    const nodesToRoot: INodeToRoot[] = [];\r\n    const nodesToRootToAdd: Bone[] = [];\r\n\r\n    getNodesToRoot(gltfRuntime, newSkeleton, skins, nodesToRoot);\r\n    newSkeleton.bones = [];\r\n\r\n    // Joints\r\n    for (let i = 0; i < skins.jointNames.length; i++) {\r\n        const jointNode = getJointNode(gltfRuntime, skins.jointNames[i]);\r\n\r\n        if (!jointNode) {\r\n            continue;\r\n        }\r\n\r\n        const node = jointNode.node;\r\n\r\n        if (!node) {\r\n            Tools.Warn(\"Joint named \" + skins.jointNames[i] + \" does not exist\");\r\n            continue;\r\n        }\r\n\r\n        const id = jointNode.id;\r\n\r\n        // Optimize, if the bone already exists...\r\n        const existingBone = gltfRuntime.scene.getBoneById(id);\r\n        if (existingBone) {\r\n            newSkeleton.bones.push(existingBone);\r\n            continue;\r\n        }\r\n\r\n        // Search for parent bone\r\n        let foundBone = false;\r\n        let parentBone: Nullable<Bone> = null;\r\n\r\n        for (let j = 0; j < i; j++) {\r\n            const jointNode = getJointNode(gltfRuntime, skins.jointNames[j]);\r\n\r\n            if (!jointNode) {\r\n                continue;\r\n            }\r\n\r\n            const joint: IGLTFNode = jointNode.node;\r\n\r\n            if (!joint) {\r\n                Tools.Warn(\"Joint named \" + skins.jointNames[j] + \" does not exist when looking for parent\");\r\n                continue;\r\n            }\r\n\r\n            const children = joint.children;\r\n            if (!children) {\r\n                continue;\r\n            }\r\n            foundBone = false;\r\n\r\n            for (let k = 0; k < children.length; k++) {\r\n                if (children[k] === id) {\r\n                    parentBone = getParentBone(gltfRuntime, skins, skins.jointNames[j], newSkeleton);\r\n                    foundBone = true;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (foundBone) {\r\n                break;\r\n            }\r\n        }\r\n\r\n        // Create bone\r\n        const mat = configureBoneTransformation(node);\r\n\r\n        if (!parentBone && nodesToRoot.length > 0) {\r\n            parentBone = getNodeToRoot(nodesToRoot, id);\r\n\r\n            if (parentBone) {\r\n                if (nodesToRootToAdd.indexOf(parentBone) === -1) {\r\n                    nodesToRootToAdd.push(parentBone);\r\n                }\r\n            }\r\n        }\r\n\r\n        const bone = new Bone(node.jointName || \"\", newSkeleton, parentBone, mat);\r\n        bone.id = id;\r\n    }\r\n\r\n    // Polish\r\n    const bones = newSkeleton.bones;\r\n    newSkeleton.bones = [];\r\n\r\n    for (let i = 0; i < skins.jointNames.length; i++) {\r\n        const jointNode = getJointNode(gltfRuntime, skins.jointNames[i]);\r\n\r\n        if (!jointNode) {\r\n            continue;\r\n        }\r\n\r\n        for (let j = 0; j < bones.length; j++) {\r\n            if (bones[j].id === jointNode.id) {\r\n                newSkeleton.bones.push(bones[j]);\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    newSkeleton.prepare();\r\n\r\n    // Finish\r\n    for (let i = 0; i < nodesToRootToAdd.length; i++) {\r\n        newSkeleton.bones.push(nodesToRootToAdd[i]);\r\n    }\r\n\r\n    return newSkeleton;\r\n};\r\n\r\n/**\r\n * Imports a mesh and its geometries\r\n * @param gltfRuntime\r\n * @param node\r\n * @param meshes\r\n * @param id\r\n * @param newMesh\r\n */\r\nconst importMesh = (gltfRuntime: IGLTFRuntime, node: IGLTFNode, meshes: string[], id: string, newMesh: Mesh): Mesh => {\r\n    if (!newMesh) {\r\n        gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n        newMesh = new Mesh(node.name || \"\", gltfRuntime.scene);\r\n        newMesh._parentContainer = gltfRuntime.assetContainer;\r\n        gltfRuntime.scene._blockEntityCollection = false;\r\n        newMesh.id = id;\r\n    }\r\n\r\n    if (!node.babylonNode) {\r\n        return newMesh;\r\n    }\r\n\r\n    const subMaterials: Material[] = [];\r\n\r\n    let vertexData: Nullable<VertexData> = null;\r\n    const verticesStarts = new Array<number>();\r\n    const verticesCounts = new Array<number>();\r\n    const indexStarts = new Array<number>();\r\n    const indexCounts = new Array<number>();\r\n\r\n    for (let meshIndex = 0; meshIndex < meshes.length; meshIndex++) {\r\n        const meshId = meshes[meshIndex];\r\n        const mesh: IGLTFMesh = gltfRuntime.meshes[meshId];\r\n\r\n        if (!mesh) {\r\n            continue;\r\n        }\r\n\r\n        // Positions, normals and UVs\r\n        for (let i = 0; i < mesh.primitives.length; i++) {\r\n            // Temporary vertex data\r\n            const tempVertexData = new VertexData();\r\n\r\n            const primitive = mesh.primitives[i];\r\n            if (primitive.mode !== 4) {\r\n                // continue;\r\n            }\r\n\r\n            const attributes = primitive.attributes;\r\n            let accessor: Nullable<IGLTFAccessor> = null;\r\n            let buffer: any = null;\r\n\r\n            // Set positions, normal and uvs\r\n            for (const semantic in attributes) {\r\n                // Link accessor and buffer view\r\n                accessor = gltfRuntime.accessors[attributes[semantic]];\r\n                buffer = GLTFUtils.GetBufferFromAccessor(gltfRuntime, accessor);\r\n\r\n                if (semantic === \"NORMAL\") {\r\n                    tempVertexData.normals = new Float32Array(buffer.length);\r\n                    (<Float32Array>tempVertexData.normals).set(buffer);\r\n                } else if (semantic === \"POSITION\") {\r\n                    if (GLTFFileLoader.HomogeneousCoordinates) {\r\n                        tempVertexData.positions = new Float32Array(buffer.length - buffer.length / 4);\r\n\r\n                        for (let j = 0; j < buffer.length; j += 4) {\r\n                            tempVertexData.positions[j] = buffer[j];\r\n                            tempVertexData.positions[j + 1] = buffer[j + 1];\r\n                            tempVertexData.positions[j + 2] = buffer[j + 2];\r\n                        }\r\n                    } else {\r\n                        tempVertexData.positions = new Float32Array(buffer.length);\r\n                        (<Float32Array>tempVertexData.positions).set(buffer);\r\n                    }\r\n\r\n                    verticesCounts.push(tempVertexData.positions.length);\r\n                } else if (semantic.indexOf(\"TEXCOORD_\") !== -1) {\r\n                    const channel = Number(semantic.split(\"_\")[1]);\r\n                    const uvKind = VertexBuffer.UVKind + (channel === 0 ? \"\" : channel + 1);\r\n                    const uvs = new Float32Array(buffer.length);\r\n                    (<Float32Array>uvs).set(buffer);\r\n                    normalizeUVs(uvs);\r\n                    tempVertexData.set(uvs, uvKind);\r\n                } else if (semantic === \"JOINT\") {\r\n                    tempVertexData.matricesIndices = new Float32Array(buffer.length);\r\n                    (<Float32Array>tempVertexData.matricesIndices).set(buffer);\r\n                } else if (semantic === \"WEIGHT\") {\r\n                    tempVertexData.matricesWeights = new Float32Array(buffer.length);\r\n                    (<Float32Array>tempVertexData.matricesWeights).set(buffer);\r\n                } else if (semantic === \"COLOR\") {\r\n                    tempVertexData.colors = new Float32Array(buffer.length);\r\n                    (<Float32Array>tempVertexData.colors).set(buffer);\r\n                }\r\n            }\r\n\r\n            // Indices\r\n            accessor = gltfRuntime.accessors[primitive.indices];\r\n            if (accessor) {\r\n                buffer = GLTFUtils.GetBufferFromAccessor(gltfRuntime, accessor);\r\n\r\n                tempVertexData.indices = new Int32Array(buffer.length);\r\n                tempVertexData.indices.set(buffer);\r\n                indexCounts.push(tempVertexData.indices.length);\r\n            } else {\r\n                // Set indices on the fly\r\n                const indices: number[] = [];\r\n                for (let j = 0; j < (<FloatArray>tempVertexData.positions).length / 3; j++) {\r\n                    indices.push(j);\r\n                }\r\n\r\n                tempVertexData.indices = new Int32Array(indices);\r\n                indexCounts.push(tempVertexData.indices.length);\r\n            }\r\n\r\n            if (!vertexData) {\r\n                vertexData = tempVertexData;\r\n            } else {\r\n                vertexData.merge(tempVertexData);\r\n            }\r\n\r\n            // Sub material\r\n            const material = gltfRuntime.scene.getMaterialById(primitive.material);\r\n\r\n            subMaterials.push(material === null ? GLTFUtils.GetDefaultMaterial(gltfRuntime.scene) : material);\r\n\r\n            // Update vertices start and index start\r\n            verticesStarts.push(verticesStarts.length === 0 ? 0 : verticesStarts[verticesStarts.length - 1] + verticesCounts[verticesCounts.length - 2]);\r\n            indexStarts.push(indexStarts.length === 0 ? 0 : indexStarts[indexStarts.length - 1] + indexCounts[indexCounts.length - 2]);\r\n        }\r\n    }\r\n    let material: StandardMaterial | MultiMaterial;\r\n    gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n    if (subMaterials.length > 1) {\r\n        material = new MultiMaterial(\"multimat\" + id, gltfRuntime.scene);\r\n        (material as MultiMaterial).subMaterials = subMaterials;\r\n    } else {\r\n        material = new StandardMaterial(\"multimat\" + id, gltfRuntime.scene);\r\n    }\r\n\r\n    if (subMaterials.length === 1) {\r\n        material = subMaterials[0] as StandardMaterial;\r\n    }\r\n\r\n    material._parentContainer = gltfRuntime.assetContainer;\r\n\r\n    if (!newMesh.material) {\r\n        newMesh.material = material;\r\n    }\r\n\r\n    // Apply geometry\r\n    new Geometry(id, gltfRuntime.scene, vertexData!, false, newMesh);\r\n    newMesh.computeWorldMatrix(true);\r\n\r\n    gltfRuntime.scene._blockEntityCollection = false;\r\n\r\n    // Apply submeshes\r\n    newMesh.subMeshes = [];\r\n    let index = 0;\r\n    for (let meshIndex = 0; meshIndex < meshes.length; meshIndex++) {\r\n        const meshId = meshes[meshIndex];\r\n        const mesh: IGLTFMesh = gltfRuntime.meshes[meshId];\r\n\r\n        if (!mesh) {\r\n            continue;\r\n        }\r\n\r\n        for (let i = 0; i < mesh.primitives.length; i++) {\r\n            if (mesh.primitives[i].mode !== 4) {\r\n                //continue;\r\n            }\r\n\r\n            SubMesh.AddToMesh(index, verticesStarts[index], verticesCounts[index], indexStarts[index], indexCounts[index], newMesh, newMesh, true);\r\n            index++;\r\n        }\r\n    }\r\n\r\n    // Finish\r\n    return newMesh;\r\n};\r\n\r\n/**\r\n * Configure node transformation from position, rotation and scaling\r\n * @param newNode\r\n * @param position\r\n * @param rotation\r\n * @param scaling\r\n */\r\nconst configureNode = (newNode: any, position: Vector3, rotation: Quaternion, scaling: Vector3) => {\r\n    if (newNode.position) {\r\n        newNode.position = position;\r\n    }\r\n\r\n    if (newNode.rotationQuaternion || newNode.rotation) {\r\n        newNode.rotationQuaternion = rotation;\r\n    }\r\n\r\n    if (newNode.scaling) {\r\n        newNode.scaling = scaling;\r\n    }\r\n};\r\n\r\n/**\r\n * Configures node from transformation matrix\r\n * @param newNode\r\n * @param node\r\n */\r\nconst configureNodeFromMatrix = (newNode: Mesh, node: IGLTFNode) => {\r\n    if (node.matrix) {\r\n        const position = new Vector3(0, 0, 0);\r\n        const rotation = new Quaternion();\r\n        const scaling = new Vector3(0, 0, 0);\r\n        const mat = Matrix.FromArray(node.matrix);\r\n        mat.decompose(scaling, rotation, position);\r\n\r\n        configureNode(newNode, position, rotation, scaling);\r\n    } else if (node.translation && node.rotation && node.scale) {\r\n        configureNode(newNode, Vector3.FromArray(node.translation), Quaternion.FromArray(node.rotation), Vector3.FromArray(node.scale));\r\n    }\r\n\r\n    newNode.computeWorldMatrix(true);\r\n};\r\n\r\n/**\r\n * Imports a node\r\n * @param gltfRuntime\r\n * @param node\r\n * @param id\r\n */\r\nconst importNode = (gltfRuntime: IGLTFRuntime, node: IGLTFNode, id: string): Nullable<Node> => {\r\n    let lastNode: Nullable<Node> = null;\r\n\r\n    if (gltfRuntime.importOnlyMeshes && (node.skin || node.meshes)) {\r\n        if (gltfRuntime.importMeshesNames && gltfRuntime.importMeshesNames.length > 0 && gltfRuntime.importMeshesNames.indexOf(node.name || \"\") === -1) {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    // Meshes\r\n    if (node.skin) {\r\n        if (node.meshes) {\r\n            const skin: IGLTFSkins = gltfRuntime.skins[node.skin];\r\n\r\n            const newMesh = importMesh(gltfRuntime, node, node.meshes, id, <Mesh>node.babylonNode);\r\n            newMesh.skeleton = gltfRuntime.scene.getLastSkeletonById(node.skin);\r\n\r\n            if (newMesh.skeleton === null) {\r\n                newMesh.skeleton = importSkeleton(gltfRuntime, skin, newMesh, skin.babylonSkeleton);\r\n\r\n                if (!skin.babylonSkeleton) {\r\n                    skin.babylonSkeleton = newMesh.skeleton;\r\n                }\r\n            }\r\n\r\n            lastNode = newMesh;\r\n        }\r\n    } else if (node.meshes) {\r\n        /**\r\n         * Improve meshes property\r\n         */\r\n        const newMesh = importMesh(gltfRuntime, node, node.mesh ? [node.mesh] : node.meshes, id, <Mesh>node.babylonNode);\r\n        lastNode = newMesh;\r\n    }\r\n    // Lights\r\n    else if (node.light && !node.babylonNode && !gltfRuntime.importOnlyMeshes) {\r\n        const light: IGLTFLight = gltfRuntime.lights[node.light];\r\n\r\n        if (light) {\r\n            if (light.type === \"ambient\") {\r\n                const ambienLight: IGLTFAmbienLight = (<any>light)[light.type];\r\n                const hemiLight = new HemisphericLight(node.light, Vector3.Zero(), gltfRuntime.scene);\r\n                hemiLight.name = node.name || \"\";\r\n\r\n                if (ambienLight.color) {\r\n                    hemiLight.diffuse = Color3.FromArray(ambienLight.color);\r\n                }\r\n\r\n                lastNode = hemiLight;\r\n            } else if (light.type === \"directional\") {\r\n                const directionalLight: IGLTFDirectionalLight = (<any>light)[light.type];\r\n                const dirLight = new DirectionalLight(node.light, Vector3.Zero(), gltfRuntime.scene);\r\n                dirLight.name = node.name || \"\";\r\n\r\n                if (directionalLight.color) {\r\n                    dirLight.diffuse = Color3.FromArray(directionalLight.color);\r\n                }\r\n\r\n                lastNode = dirLight;\r\n            } else if (light.type === \"point\") {\r\n                const pointLight: IGLTFPointLight = (<any>light)[light.type];\r\n                const ptLight = new PointLight(node.light, Vector3.Zero(), gltfRuntime.scene);\r\n                ptLight.name = node.name || \"\";\r\n\r\n                if (pointLight.color) {\r\n                    ptLight.diffuse = Color3.FromArray(pointLight.color);\r\n                }\r\n\r\n                lastNode = ptLight;\r\n            } else if (light.type === \"spot\") {\r\n                const spotLight: IGLTFSpotLight = (<any>light)[light.type];\r\n                const spLight = new SpotLight(node.light, Vector3.Zero(), Vector3.Zero(), 0, 0, gltfRuntime.scene);\r\n                spLight.name = node.name || \"\";\r\n\r\n                if (spotLight.color) {\r\n                    spLight.diffuse = Color3.FromArray(spotLight.color);\r\n                }\r\n\r\n                if (spotLight.fallOfAngle) {\r\n                    spLight.angle = spotLight.fallOfAngle;\r\n                }\r\n\r\n                if (spotLight.fallOffExponent) {\r\n                    spLight.exponent = spotLight.fallOffExponent;\r\n                }\r\n\r\n                lastNode = spLight;\r\n            }\r\n        }\r\n    }\r\n    // Cameras\r\n    else if (node.camera && !node.babylonNode && !gltfRuntime.importOnlyMeshes) {\r\n        const camera: IGLTFCamera = gltfRuntime.cameras[node.camera];\r\n\r\n        if (camera) {\r\n            gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n            if (camera.type === \"orthographic\") {\r\n                const orthoCamera = new FreeCamera(node.camera, Vector3.Zero(), gltfRuntime.scene, false);\r\n\r\n                orthoCamera.name = node.name || \"\";\r\n                orthoCamera.mode = Camera.ORTHOGRAPHIC_CAMERA;\r\n                orthoCamera.attachControl();\r\n\r\n                lastNode = orthoCamera;\r\n\r\n                orthoCamera._parentContainer = gltfRuntime.assetContainer;\r\n            } else if (camera.type === \"perspective\") {\r\n                const perspectiveCamera: IGLTFCameraPerspective = (<any>camera)[camera.type];\r\n                const persCamera = new FreeCamera(node.camera, Vector3.Zero(), gltfRuntime.scene, false);\r\n\r\n                persCamera.name = node.name || \"\";\r\n                persCamera.attachControl();\r\n\r\n                if (!perspectiveCamera.aspectRatio) {\r\n                    perspectiveCamera.aspectRatio = gltfRuntime.scene.getEngine().getRenderWidth() / gltfRuntime.scene.getEngine().getRenderHeight();\r\n                }\r\n\r\n                if (perspectiveCamera.znear && perspectiveCamera.zfar) {\r\n                    persCamera.maxZ = perspectiveCamera.zfar;\r\n                    persCamera.minZ = perspectiveCamera.znear;\r\n                }\r\n\r\n                lastNode = persCamera;\r\n                persCamera._parentContainer = gltfRuntime.assetContainer;\r\n            }\r\n\r\n            gltfRuntime.scene._blockEntityCollection = false;\r\n        }\r\n    }\r\n\r\n    // Empty node\r\n    if (!node.jointName) {\r\n        if (node.babylonNode) {\r\n            return node.babylonNode;\r\n        } else if (lastNode === null) {\r\n            gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n            const dummy = new Mesh(node.name || \"\", gltfRuntime.scene);\r\n            dummy._parentContainer = gltfRuntime.assetContainer;\r\n            gltfRuntime.scene._blockEntityCollection = false;\r\n            node.babylonNode = dummy;\r\n            lastNode = dummy;\r\n        }\r\n    }\r\n\r\n    if (lastNode !== null) {\r\n        if (node.matrix && lastNode instanceof Mesh) {\r\n            configureNodeFromMatrix(lastNode, node);\r\n        } else {\r\n            const translation = node.translation || [0, 0, 0];\r\n            const rotation = node.rotation || [0, 0, 0, 1];\r\n            const scale = node.scale || [1, 1, 1];\r\n            configureNode(lastNode, Vector3.FromArray(translation), Quaternion.FromArray(rotation), Vector3.FromArray(scale));\r\n        }\r\n\r\n        lastNode.updateCache(true);\r\n        node.babylonNode = lastNode;\r\n    }\r\n\r\n    return lastNode;\r\n};\r\n\r\n/**\r\n * Traverses nodes and creates them\r\n * @param gltfRuntime\r\n * @param id\r\n * @param parent\r\n * @param meshIncluded\r\n */\r\nconst traverseNodes = (gltfRuntime: IGLTFRuntime, id: string, parent: Nullable<Node>, meshIncluded: boolean = false) => {\r\n    const node: IGLTFNode = gltfRuntime.nodes[id];\r\n    let newNode: Nullable<Node> = null;\r\n\r\n    if (gltfRuntime.importOnlyMeshes && !meshIncluded && gltfRuntime.importMeshesNames) {\r\n        if (gltfRuntime.importMeshesNames.indexOf(node.name || \"\") !== -1 || gltfRuntime.importMeshesNames.length === 0) {\r\n            meshIncluded = true;\r\n        } else {\r\n            meshIncluded = false;\r\n        }\r\n    } else {\r\n        meshIncluded = true;\r\n    }\r\n\r\n    if (!node.jointName && meshIncluded) {\r\n        newNode = importNode(gltfRuntime, node, id);\r\n\r\n        if (newNode !== null) {\r\n            newNode.id = id;\r\n            newNode.parent = parent;\r\n        }\r\n    }\r\n\r\n    if (node.children) {\r\n        for (let i = 0; i < node.children.length; i++) {\r\n            traverseNodes(gltfRuntime, node.children[i], newNode, meshIncluded);\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * do stuff after buffers, shaders are loaded (e.g. hook up materials, load animations, etc.)\r\n * @param gltfRuntime\r\n */\r\nconst postLoad = (gltfRuntime: IGLTFRuntime) => {\r\n    // Nodes\r\n    let currentScene: IGLTFScene = <IGLTFScene>gltfRuntime.currentScene;\r\n\r\n    if (currentScene) {\r\n        for (let i = 0; i < currentScene.nodes.length; i++) {\r\n            traverseNodes(gltfRuntime, currentScene.nodes[i], null);\r\n        }\r\n    } else {\r\n        for (const thing in gltfRuntime.scenes) {\r\n            currentScene = <IGLTFScene>gltfRuntime.scenes[thing];\r\n\r\n            for (let i = 0; i < currentScene.nodes.length; i++) {\r\n                traverseNodes(gltfRuntime, currentScene.nodes[i], null);\r\n            }\r\n        }\r\n    }\r\n\r\n    // Set animations\r\n    loadAnimations(gltfRuntime);\r\n\r\n    for (let i = 0; i < gltfRuntime.scene.skeletons.length; i++) {\r\n        const skeleton = gltfRuntime.scene.skeletons[i];\r\n        gltfRuntime.scene.beginAnimation(skeleton, 0, Number.MAX_VALUE, true, 1.0);\r\n    }\r\n};\r\n\r\n/**\r\n * onBind shaderrs callback to set uniforms and matrices\r\n * @param mesh\r\n * @param gltfRuntime\r\n * @param unTreatedUniforms\r\n * @param shaderMaterial\r\n * @param technique\r\n * @param material\r\n * @param onSuccess\r\n */\r\nconst onBindShaderMaterial = (\r\n    mesh: AbstractMesh,\r\n    gltfRuntime: IGLTFRuntime,\r\n    unTreatedUniforms: { [key: string]: IGLTFTechniqueParameter },\r\n    shaderMaterial: ShaderMaterial,\r\n    technique: IGLTFTechnique,\r\n    material: IGLTFMaterial,\r\n    onSuccess: (shaderMaterial: ShaderMaterial) => void\r\n) => {\r\n    const materialValues = material.values || technique.parameters;\r\n\r\n    for (const unif in unTreatedUniforms) {\r\n        const uniform: IGLTFTechniqueParameter = unTreatedUniforms[unif];\r\n        const type = uniform.type;\r\n\r\n        if (type === EParameterType.FLOAT_MAT2 || type === EParameterType.FLOAT_MAT3 || type === EParameterType.FLOAT_MAT4) {\r\n            if (uniform.semantic && !uniform.source && !uniform.node) {\r\n                GLTFUtils.SetMatrix(gltfRuntime.scene, mesh, uniform, unif, <Effect>shaderMaterial.getEffect());\r\n            } else if (uniform.semantic && (uniform.source || uniform.node)) {\r\n                let source = gltfRuntime.scene.getNodeByName(uniform.source || uniform.node || \"\");\r\n                if (source === null) {\r\n                    source = gltfRuntime.scene.getNodeById(uniform.source || uniform.node || \"\");\r\n                }\r\n                if (source === null) {\r\n                    continue;\r\n                }\r\n\r\n                GLTFUtils.SetMatrix(gltfRuntime.scene, source, uniform, unif, <Effect>shaderMaterial.getEffect());\r\n            }\r\n        } else {\r\n            const value = (<any>materialValues)[technique.uniforms[unif]];\r\n            if (!value) {\r\n                continue;\r\n            }\r\n\r\n            if (type === EParameterType.SAMPLER_2D) {\r\n                const texture = gltfRuntime.textures[material.values ? value : uniform.value].babylonTexture;\r\n\r\n                if (texture === null || texture === undefined) {\r\n                    continue;\r\n                }\r\n\r\n                (<Effect>shaderMaterial.getEffect()).setTexture(unif, texture);\r\n            } else {\r\n                GLTFUtils.SetUniform(<Effect>shaderMaterial.getEffect(), unif, value, type);\r\n            }\r\n        }\r\n    }\r\n\r\n    onSuccess(shaderMaterial);\r\n};\r\n\r\n/**\r\n * Prepare uniforms to send the only one time\r\n * Loads the appropriate textures\r\n * @param gltfRuntime\r\n * @param shaderMaterial\r\n * @param technique\r\n * @param material\r\n */\r\nconst prepareShaderMaterialUniforms = (\r\n    gltfRuntime: IGLTFRuntime,\r\n    shaderMaterial: ShaderMaterial,\r\n    technique: IGLTFTechnique,\r\n    material: IGLTFMaterial,\r\n    unTreatedUniforms: { [key: string]: IGLTFTechniqueParameter }\r\n) => {\r\n    const materialValues = material.values || technique.parameters;\r\n    const techniqueUniforms = technique.uniforms;\r\n\r\n    /**\r\n     * Prepare values here (not matrices)\r\n     */\r\n    for (const unif in unTreatedUniforms) {\r\n        const uniform: IGLTFTechniqueParameter = unTreatedUniforms[unif];\r\n        const type = uniform.type;\r\n        let value = (<any>materialValues)[techniqueUniforms[unif]];\r\n\r\n        if (value === undefined) {\r\n            // In case the value is the same for all materials\r\n            value = <any>uniform.value;\r\n        }\r\n\r\n        if (!value) {\r\n            continue;\r\n        }\r\n\r\n        const onLoadTexture = (uniformName: Nullable<string>) => {\r\n            return (texture: Texture) => {\r\n                if (uniform.value && uniformName) {\r\n                    // Static uniform\r\n                    shaderMaterial.setTexture(uniformName, texture);\r\n                    delete unTreatedUniforms[uniformName];\r\n                }\r\n            };\r\n        };\r\n\r\n        // Texture (sampler2D)\r\n        if (type === EParameterType.SAMPLER_2D) {\r\n            GLTFLoaderExtension.LoadTextureAsync(gltfRuntime, material.values ? value : uniform.value, onLoadTexture(unif), () => onLoadTexture(null));\r\n        }\r\n        // Others\r\n        else {\r\n            if (uniform.value && GLTFUtils.SetUniform(shaderMaterial, unif, material.values ? value : uniform.value, type)) {\r\n                // Static uniform\r\n                delete unTreatedUniforms[unif];\r\n            }\r\n        }\r\n    }\r\n};\r\n\r\n/**\r\n * Shader compilation failed\r\n * @param program\r\n * @param shaderMaterial\r\n * @param onError\r\n */\r\nconst onShaderCompileError = (program: IGLTFProgram, shaderMaterial: ShaderMaterial, onError: (message: string) => void) => {\r\n    return (effect: Effect, error: string) => {\r\n        shaderMaterial.dispose(true);\r\n        onError(\"Cannot compile program named \" + program.name + \". Error: \" + error + \". Default material will be applied\");\r\n    };\r\n};\r\n\r\n/**\r\n * Shader compilation success\r\n * @param gltfRuntime\r\n * @param shaderMaterial\r\n * @param technique\r\n * @param material\r\n * @param unTreatedUniforms\r\n * @param onSuccess\r\n */\r\nconst onShaderCompileSuccess = (\r\n    gltfRuntime: IGLTFRuntime,\r\n    shaderMaterial: ShaderMaterial,\r\n    technique: IGLTFTechnique,\r\n    material: IGLTFMaterial,\r\n    unTreatedUniforms: { [key: string]: IGLTFTechniqueParameter },\r\n    onSuccess: (shaderMaterial: ShaderMaterial) => void\r\n) => {\r\n    return (_: Effect) => {\r\n        prepareShaderMaterialUniforms(gltfRuntime, shaderMaterial, technique, material, unTreatedUniforms);\r\n\r\n        shaderMaterial.onBind = (mesh: AbstractMesh) => {\r\n            onBindShaderMaterial(mesh, gltfRuntime, unTreatedUniforms, shaderMaterial, technique, material, onSuccess);\r\n        };\r\n    };\r\n};\r\n\r\n/**\r\n * Returns the appropriate uniform if already handled by babylon\r\n * @param tokenizer\r\n * @param technique\r\n */\r\nconst parseShaderUniforms = (tokenizer: Tokenizer, technique: IGLTFTechnique, unTreatedUniforms: { [key: string]: IGLTFTechniqueParameter }): string => {\r\n    for (const unif in technique.uniforms) {\r\n        const uniform = technique.uniforms[unif];\r\n        const uniformParameter: IGLTFTechniqueParameter = technique.parameters[uniform];\r\n\r\n        if (tokenizer.currentIdentifier === unif) {\r\n            if (uniformParameter.semantic && !uniformParameter.source && !uniformParameter.node) {\r\n                const transformIndex = glTFTransforms.indexOf(uniformParameter.semantic);\r\n\r\n                if (transformIndex !== -1) {\r\n                    delete unTreatedUniforms[unif];\r\n                    return babylonTransforms[transformIndex];\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return tokenizer.currentIdentifier;\r\n};\r\n\r\n/**\r\n * All shaders loaded. Create materials one by one\r\n * @param gltfRuntime\r\n */\r\nconst importMaterials = (gltfRuntime: IGLTFRuntime) => {\r\n    // Create materials\r\n    for (const mat in gltfRuntime.materials) {\r\n        GLTFLoaderExtension.LoadMaterialAsync(\r\n            gltfRuntime,\r\n            mat,\r\n            () => {},\r\n            () => {}\r\n        );\r\n    }\r\n};\r\n\r\n/**\r\n * Implementation of the base glTF spec\r\n * @internal\r\n */\r\nexport class GLTFLoaderBase {\r\n    public static CreateRuntime(parsedData: any, scene: Scene, rootUrl: string): IGLTFRuntime {\r\n        const gltfRuntime: IGLTFRuntime = {\r\n            extensions: {},\r\n            accessors: {},\r\n            buffers: {},\r\n            bufferViews: {},\r\n            meshes: {},\r\n            lights: {},\r\n            cameras: {},\r\n            nodes: {},\r\n            images: {},\r\n            textures: {},\r\n            shaders: {},\r\n            programs: {},\r\n            samplers: {},\r\n            techniques: {},\r\n            materials: {},\r\n            animations: {},\r\n            skins: {},\r\n            extensionsUsed: [],\r\n\r\n            scenes: {},\r\n\r\n            buffersCount: 0,\r\n            shaderscount: 0,\r\n\r\n            scene: scene,\r\n            rootUrl: rootUrl,\r\n\r\n            loadedBufferCount: 0,\r\n            loadedBufferViews: {},\r\n\r\n            loadedShaderCount: 0,\r\n\r\n            importOnlyMeshes: false,\r\n\r\n            dummyNodes: [],\r\n\r\n            assetContainer: null,\r\n        };\r\n\r\n        // Parse\r\n        if (parsedData.extensions) {\r\n            parseObject(parsedData.extensions, \"extensions\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.extensionsUsed) {\r\n            parseObject(parsedData.extensionsUsed, \"extensionsUsed\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.buffers) {\r\n            parseBuffers(parsedData.buffers, gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.bufferViews) {\r\n            parseObject(parsedData.bufferViews, \"bufferViews\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.accessors) {\r\n            parseObject(parsedData.accessors, \"accessors\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.meshes) {\r\n            parseObject(parsedData.meshes, \"meshes\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.lights) {\r\n            parseObject(parsedData.lights, \"lights\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.cameras) {\r\n            parseObject(parsedData.cameras, \"cameras\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.nodes) {\r\n            parseObject(parsedData.nodes, \"nodes\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.images) {\r\n            parseObject(parsedData.images, \"images\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.textures) {\r\n            parseObject(parsedData.textures, \"textures\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.shaders) {\r\n            parseShaders(parsedData.shaders, gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.programs) {\r\n            parseObject(parsedData.programs, \"programs\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.samplers) {\r\n            parseObject(parsedData.samplers, \"samplers\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.techniques) {\r\n            parseObject(parsedData.techniques, \"techniques\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.materials) {\r\n            parseObject(parsedData.materials, \"materials\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.animations) {\r\n            parseObject(parsedData.animations, \"animations\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.skins) {\r\n            parseObject(parsedData.skins, \"skins\", gltfRuntime);\r\n        }\r\n\r\n        if (parsedData.scenes) {\r\n            gltfRuntime.scenes = parsedData.scenes;\r\n        }\r\n\r\n        if (parsedData.scene && parsedData.scenes) {\r\n            gltfRuntime.currentScene = parsedData.scenes[parsedData.scene];\r\n        }\r\n\r\n        return gltfRuntime;\r\n    }\r\n\r\n    public static LoadBufferAsync(\r\n        gltfRuntime: IGLTFRuntime,\r\n        id: string,\r\n        onSuccess: (buffer: ArrayBufferView) => void,\r\n        onError: (message: string) => void,\r\n        onProgress?: () => void\r\n    ): void {\r\n        const buffer: IGLTFBuffer = gltfRuntime.buffers[id];\r\n\r\n        if (Tools.IsBase64(buffer.uri)) {\r\n            setTimeout(() => onSuccess(new Uint8Array(Tools.DecodeBase64(buffer.uri))));\r\n        } else {\r\n            Tools.LoadFile(\r\n                gltfRuntime.rootUrl + buffer.uri,\r\n                (data) => onSuccess(new Uint8Array(data as ArrayBuffer)),\r\n                onProgress,\r\n                undefined,\r\n                true,\r\n                (request) => {\r\n                    if (request) {\r\n                        onError(request.status + \" \" + request.statusText);\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    }\r\n\r\n    public static LoadTextureBufferAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (buffer: Nullable<ArrayBufferView>) => void, onError: (message: string) => void): void {\r\n        const texture: IGLTFTexture = gltfRuntime.textures[id];\r\n\r\n        if (!texture || !texture.source) {\r\n            onError(\"\");\r\n            return;\r\n        }\r\n\r\n        if (texture.babylonTexture) {\r\n            onSuccess(null);\r\n            return;\r\n        }\r\n\r\n        const source: IGLTFImage = gltfRuntime.images[texture.source];\r\n\r\n        if (Tools.IsBase64(source.uri)) {\r\n            setTimeout(() => onSuccess(new Uint8Array(Tools.DecodeBase64(source.uri))));\r\n        } else {\r\n            Tools.LoadFile(\r\n                gltfRuntime.rootUrl + source.uri,\r\n                (data) => onSuccess(new Uint8Array(data as ArrayBuffer)),\r\n                undefined,\r\n                undefined,\r\n                true,\r\n                (request) => {\r\n                    if (request) {\r\n                        onError(request.status + \" \" + request.statusText);\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    }\r\n\r\n    public static CreateTextureAsync(gltfRuntime: IGLTFRuntime, id: string, buffer: Nullable<ArrayBufferView>, onSuccess: (texture: Texture) => void): void {\r\n        const texture: IGLTFTexture = gltfRuntime.textures[id];\r\n\r\n        if (texture.babylonTexture) {\r\n            onSuccess(texture.babylonTexture);\r\n            return;\r\n        }\r\n\r\n        const sampler: IGLTFSampler = gltfRuntime.samplers[texture.sampler];\r\n\r\n        const createMipMaps =\r\n            sampler.minFilter === ETextureFilterType.NEAREST_MIPMAP_NEAREST ||\r\n            sampler.minFilter === ETextureFilterType.NEAREST_MIPMAP_LINEAR ||\r\n            sampler.minFilter === ETextureFilterType.LINEAR_MIPMAP_NEAREST ||\r\n            sampler.minFilter === ETextureFilterType.LINEAR_MIPMAP_LINEAR;\r\n\r\n        const samplingMode = Texture.BILINEAR_SAMPLINGMODE;\r\n\r\n        const blob = buffer == null ? new Blob() : new Blob([buffer]);\r\n        const blobURL = URL.createObjectURL(blob);\r\n        const revokeBlobURL = () => URL.revokeObjectURL(blobURL);\r\n        const newTexture = new Texture(blobURL, gltfRuntime.scene, !createMipMaps, true, samplingMode, revokeBlobURL, revokeBlobURL);\r\n        if (sampler.wrapS !== undefined) {\r\n            newTexture.wrapU = GLTFUtils.GetWrapMode(sampler.wrapS);\r\n        }\r\n        if (sampler.wrapT !== undefined) {\r\n            newTexture.wrapV = GLTFUtils.GetWrapMode(sampler.wrapT);\r\n        }\r\n        newTexture.name = id;\r\n\r\n        texture.babylonTexture = newTexture;\r\n        onSuccess(newTexture);\r\n    }\r\n\r\n    public static LoadShaderStringAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (shaderString: string | ArrayBuffer) => void, onError?: (message: string) => void): void {\r\n        const shader: IGLTFShader = gltfRuntime.shaders[id];\r\n\r\n        if (Tools.IsBase64(shader.uri)) {\r\n            const shaderString = atob(shader.uri.split(\",\")[1]);\r\n            if (onSuccess) {\r\n                onSuccess(shaderString);\r\n            }\r\n        } else {\r\n            Tools.LoadFile(gltfRuntime.rootUrl + shader.uri, onSuccess, undefined, undefined, false, (request) => {\r\n                if (request && onError) {\r\n                    onError(request.status + \" \" + request.statusText);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    public static LoadMaterialAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (material: Material) => void, onError: (message: string) => void): void {\r\n        const material: IGLTFMaterial = gltfRuntime.materials[id];\r\n        if (!material.technique) {\r\n            if (onError) {\r\n                onError(\"No technique found.\");\r\n            }\r\n            return;\r\n        }\r\n\r\n        const technique: IGLTFTechnique = gltfRuntime.techniques[material.technique];\r\n        if (!technique) {\r\n            gltfRuntime.scene._blockEntityCollection = !!gltfRuntime.assetContainer;\r\n            const defaultMaterial = new StandardMaterial(id, gltfRuntime.scene);\r\n            defaultMaterial._parentContainer = gltfRuntime.assetContainer;\r\n            gltfRuntime.scene._blockEntityCollection = false;\r\n            defaultMaterial.diffuseColor = new Color3(0.5, 0.5, 0.5);\r\n            defaultMaterial.sideOrientation = Material.CounterClockWiseSideOrientation;\r\n            onSuccess(defaultMaterial);\r\n            return;\r\n        }\r\n\r\n        const program: IGLTFProgram = gltfRuntime.programs[technique.program];\r\n        const states: IGLTFTechniqueStates = technique.states;\r\n\r\n        const vertexShader: string = Effect.ShadersStore[program.vertexShader + \"VertexShader\"];\r\n        const pixelShader: string = Effect.ShadersStore[program.fragmentShader + \"PixelShader\"];\r\n        let newVertexShader = \"\";\r\n        let newPixelShader = \"\";\r\n\r\n        const vertexTokenizer = new Tokenizer(vertexShader);\r\n        const pixelTokenizer = new Tokenizer(pixelShader);\r\n\r\n        const unTreatedUniforms: { [key: string]: IGLTFTechniqueParameter } = {};\r\n        const uniforms: string[] = [];\r\n        const attributes: string[] = [];\r\n        const samplers: string[] = [];\r\n\r\n        // Fill uniform, sampler2D and attributes\r\n        for (const unif in technique.uniforms) {\r\n            const uniform = technique.uniforms[unif];\r\n            const uniformParameter: IGLTFTechniqueParameter = technique.parameters[uniform];\r\n\r\n            unTreatedUniforms[unif] = uniformParameter;\r\n\r\n            if (uniformParameter.semantic && !uniformParameter.node && !uniformParameter.source) {\r\n                const transformIndex = glTFTransforms.indexOf(uniformParameter.semantic);\r\n                if (transformIndex !== -1) {\r\n                    uniforms.push(babylonTransforms[transformIndex]);\r\n                    delete unTreatedUniforms[unif];\r\n                } else {\r\n                    uniforms.push(unif);\r\n                }\r\n            } else if (uniformParameter.type === EParameterType.SAMPLER_2D) {\r\n                samplers.push(unif);\r\n            } else {\r\n                uniforms.push(unif);\r\n            }\r\n        }\r\n\r\n        for (const attr in technique.attributes) {\r\n            const attribute = technique.attributes[attr];\r\n            const attributeParameter: IGLTFTechniqueParameter = technique.parameters[attribute];\r\n\r\n            if (attributeParameter.semantic) {\r\n                const name = getAttribute(attributeParameter);\r\n                if (name) {\r\n                    attributes.push(name);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Configure vertex shader\r\n        while (!vertexTokenizer.isEnd() && vertexTokenizer.getNextToken()) {\r\n            const tokenType = vertexTokenizer.currentToken;\r\n\r\n            if (tokenType !== ETokenType.IDENTIFIER) {\r\n                newVertexShader += vertexTokenizer.currentString;\r\n                continue;\r\n            }\r\n\r\n            let foundAttribute = false;\r\n\r\n            for (const attr in technique.attributes) {\r\n                const attribute = technique.attributes[attr];\r\n                const attributeParameter: IGLTFTechniqueParameter = technique.parameters[attribute];\r\n\r\n                if (vertexTokenizer.currentIdentifier === attr && attributeParameter.semantic) {\r\n                    newVertexShader += getAttribute(attributeParameter);\r\n                    foundAttribute = true;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (foundAttribute) {\r\n                continue;\r\n            }\r\n\r\n            newVertexShader += parseShaderUniforms(vertexTokenizer, technique, unTreatedUniforms);\r\n        }\r\n\r\n        // Configure pixel shader\r\n        while (!pixelTokenizer.isEnd() && pixelTokenizer.getNextToken()) {\r\n            const tokenType = pixelTokenizer.currentToken;\r\n\r\n            if (tokenType !== ETokenType.IDENTIFIER) {\r\n                newPixelShader += pixelTokenizer.currentString;\r\n                continue;\r\n            }\r\n\r\n            newPixelShader += parseShaderUniforms(pixelTokenizer, technique, unTreatedUniforms);\r\n        }\r\n\r\n        // Create shader material\r\n        const shaderPath = {\r\n            vertex: program.vertexShader + id,\r\n            fragment: program.fragmentShader + id,\r\n        };\r\n\r\n        const options = {\r\n            attributes: attributes,\r\n            uniforms: uniforms,\r\n            samplers: samplers,\r\n            needAlphaBlending: states && states.enable && states.enable.indexOf(3042) !== -1,\r\n        };\r\n\r\n        Effect.ShadersStore[program.vertexShader + id + \"VertexShader\"] = newVertexShader;\r\n        Effect.ShadersStore[program.fragmentShader + id + \"PixelShader\"] = newPixelShader;\r\n\r\n        const shaderMaterial = new ShaderMaterial(id, gltfRuntime.scene, shaderPath, options);\r\n        shaderMaterial.onError = onShaderCompileError(program, shaderMaterial, onError);\r\n        shaderMaterial.onCompiled = onShaderCompileSuccess(gltfRuntime, shaderMaterial, technique, material, unTreatedUniforms, onSuccess);\r\n        shaderMaterial.sideOrientation = Material.CounterClockWiseSideOrientation;\r\n\r\n        if (states && states.functions) {\r\n            const functions = states.functions;\r\n            if (functions.cullFace && functions.cullFace[0] !== ECullingType.BACK) {\r\n                shaderMaterial.backFaceCulling = false;\r\n            }\r\n\r\n            const blendFunc = functions.blendFuncSeparate;\r\n            if (blendFunc) {\r\n                if (\r\n                    blendFunc[0] === EBlendingFunction.SRC_ALPHA &&\r\n                    blendFunc[1] === EBlendingFunction.ONE_MINUS_SRC_ALPHA &&\r\n                    blendFunc[2] === EBlendingFunction.ONE &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_COMBINE;\r\n                } else if (\r\n                    blendFunc[0] === EBlendingFunction.ONE &&\r\n                    blendFunc[1] === EBlendingFunction.ONE &&\r\n                    blendFunc[2] === EBlendingFunction.ZERO &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_ONEONE;\r\n                } else if (\r\n                    blendFunc[0] === EBlendingFunction.SRC_ALPHA &&\r\n                    blendFunc[1] === EBlendingFunction.ONE &&\r\n                    blendFunc[2] === EBlendingFunction.ZERO &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_ADD;\r\n                } else if (\r\n                    blendFunc[0] === EBlendingFunction.ZERO &&\r\n                    blendFunc[1] === EBlendingFunction.ONE_MINUS_SRC_COLOR &&\r\n                    blendFunc[2] === EBlendingFunction.ONE &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_SUBTRACT;\r\n                } else if (\r\n                    blendFunc[0] === EBlendingFunction.DST_COLOR &&\r\n                    blendFunc[1] === EBlendingFunction.ZERO &&\r\n                    blendFunc[2] === EBlendingFunction.ONE &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_MULTIPLY;\r\n                } else if (\r\n                    blendFunc[0] === EBlendingFunction.SRC_ALPHA &&\r\n                    blendFunc[1] === EBlendingFunction.ONE_MINUS_SRC_COLOR &&\r\n                    blendFunc[2] === EBlendingFunction.ONE &&\r\n                    blendFunc[3] === EBlendingFunction.ONE\r\n                ) {\r\n                    shaderMaterial.alphaMode = Constants.ALPHA_MAXIMIZED;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * glTF V1 Loader\r\n * @internal\r\n * @deprecated\r\n */\r\nexport class GLTFLoader implements IGLTFLoader {\r\n    public static Extensions: { [name: string]: GLTFLoaderExtension } = {};\r\n\r\n    public static RegisterExtension(extension: GLTFLoaderExtension): void {\r\n        if (GLTFLoader.Extensions[extension.name]) {\r\n            Tools.Error('Tool with the same name \"' + extension.name + '\" already exists');\r\n            return;\r\n        }\r\n\r\n        GLTFLoader.Extensions[extension.name] = extension;\r\n    }\r\n\r\n    public dispose(): void {\r\n        // do nothing\r\n    }\r\n\r\n    private _importMeshAsync(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        assetContainer: Nullable<AssetContainer>,\r\n        onSuccess: (meshes: AbstractMesh[], skeletons: Skeleton[]) => void,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        onError?: (message: string) => void\r\n    ): boolean {\r\n        scene.useRightHandedSystem = true;\r\n\r\n        GLTFLoaderExtension.LoadRuntimeAsync(\r\n            scene,\r\n            data,\r\n            rootUrl,\r\n            (gltfRuntime) => {\r\n                gltfRuntime.assetContainer = assetContainer;\r\n                gltfRuntime.importOnlyMeshes = true;\r\n\r\n                if (meshesNames === \"\") {\r\n                    gltfRuntime.importMeshesNames = [];\r\n                } else if (typeof meshesNames === \"string\") {\r\n                    gltfRuntime.importMeshesNames = [meshesNames];\r\n                } else if (meshesNames && !(meshesNames instanceof Array)) {\r\n                    gltfRuntime.importMeshesNames = [meshesNames];\r\n                } else {\r\n                    gltfRuntime.importMeshesNames = [];\r\n                    Tools.Warn(\"Argument meshesNames must be of type string or string[]\");\r\n                }\r\n\r\n                // Create nodes\r\n                this._createNodes(gltfRuntime);\r\n\r\n                const meshes = new Array<AbstractMesh>();\r\n                const skeletons = new Array<Skeleton>();\r\n\r\n                // Fill arrays of meshes and skeletons\r\n                for (const nde in gltfRuntime.nodes) {\r\n                    const node: IGLTFNode = gltfRuntime.nodes[nde];\r\n\r\n                    if (node.babylonNode instanceof AbstractMesh) {\r\n                        meshes.push(<AbstractMesh>node.babylonNode);\r\n                    }\r\n                }\r\n\r\n                for (const skl in gltfRuntime.skins) {\r\n                    const skin: IGLTFSkins = gltfRuntime.skins[skl];\r\n\r\n                    if (skin.babylonSkeleton instanceof Skeleton) {\r\n                        skeletons.push(skin.babylonSkeleton);\r\n                    }\r\n                }\r\n\r\n                // Load buffers, shaders, materials, etc.\r\n                this._loadBuffersAsync(gltfRuntime, () => {\r\n                    this._loadShadersAsync(gltfRuntime, () => {\r\n                        importMaterials(gltfRuntime);\r\n                        postLoad(gltfRuntime);\r\n\r\n                        if (!GLTFFileLoader.IncrementalLoading && onSuccess) {\r\n                            onSuccess(meshes, skeletons);\r\n                        }\r\n                    });\r\n                });\r\n\r\n                if (GLTFFileLoader.IncrementalLoading && onSuccess) {\r\n                    onSuccess(meshes, skeletons);\r\n                }\r\n            },\r\n            onError\r\n        );\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Imports one or more meshes from a loaded gltf file and adds them to the scene\r\n     * @param meshesNames a string or array of strings of the mesh names that should be loaded from the file\r\n     * @param scene the scene the meshes should be added to\r\n     * @param assetContainer defines the asset container to use (can be null)\r\n     * @param data gltf data containing information of the meshes in a loaded file\r\n     * @param rootUrl root url to load from\r\n     * @param onProgress event that fires when loading progress has occured\r\n     * @returns a promise containg the loaded meshes, particles, skeletons and animations\r\n     */\r\n    public importMeshAsync(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        assetContainer: Nullable<AssetContainer>,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void\r\n    ): Promise<ISceneLoaderAsyncResult> {\r\n        return new Promise((resolve, reject) => {\r\n            this._importMeshAsync(\r\n                meshesNames,\r\n                scene,\r\n                data,\r\n                rootUrl,\r\n                assetContainer,\r\n                (meshes, skeletons) => {\r\n                    resolve({\r\n                        meshes: meshes,\r\n                        particleSystems: [],\r\n                        skeletons: skeletons,\r\n                        animationGroups: [],\r\n                        lights: [],\r\n                        transformNodes: [],\r\n                        geometries: [],\r\n                    });\r\n                },\r\n                onProgress,\r\n                (message) => {\r\n                    reject(new Error(message));\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    private _loadAsync(\r\n        scene: Scene,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        onSuccess: () => void,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        onError?: (message: string) => void\r\n    ): void {\r\n        scene.useRightHandedSystem = true;\r\n\r\n        GLTFLoaderExtension.LoadRuntimeAsync(\r\n            scene,\r\n            data,\r\n            rootUrl,\r\n            (gltfRuntime) => {\r\n                // Load runtime extensios\r\n                GLTFLoaderExtension.LoadRuntimeExtensionsAsync(\r\n                    gltfRuntime,\r\n                    () => {\r\n                        // Create nodes\r\n                        this._createNodes(gltfRuntime);\r\n\r\n                        // Load buffers, shaders, materials, etc.\r\n                        this._loadBuffersAsync(gltfRuntime, () => {\r\n                            this._loadShadersAsync(gltfRuntime, () => {\r\n                                importMaterials(gltfRuntime);\r\n                                postLoad(gltfRuntime);\r\n\r\n                                if (!GLTFFileLoader.IncrementalLoading) {\r\n                                    onSuccess();\r\n                                }\r\n                            });\r\n                        });\r\n\r\n                        if (GLTFFileLoader.IncrementalLoading) {\r\n                            onSuccess();\r\n                        }\r\n                    },\r\n                    onError\r\n                );\r\n            },\r\n            onError\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Imports all objects from a loaded gltf file and adds them to the scene\r\n     * @param scene the scene the objects should be added to\r\n     * @param data gltf data containing information of the meshes in a loaded file\r\n     * @param rootUrl root url to load from\r\n     * @param onProgress event that fires when loading progress has occured\r\n     * @returns a promise which completes when objects have been loaded to the scene\r\n     */\r\n    public loadAsync(scene: Scene, data: IGLTFLoaderData, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void): Promise<void> {\r\n        return new Promise((resolve, reject) => {\r\n            this._loadAsync(\r\n                scene,\r\n                data,\r\n                rootUrl,\r\n                () => {\r\n                    resolve();\r\n                },\r\n                onProgress,\r\n                (message) => {\r\n                    reject(new Error(message));\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    private _loadShadersAsync(gltfRuntime: IGLTFRuntime, onload: () => void): void {\r\n        let hasShaders = false;\r\n\r\n        const processShader = (sha: string, shader: IGLTFShader) => {\r\n            GLTFLoaderExtension.LoadShaderStringAsync(\r\n                gltfRuntime,\r\n                sha,\r\n                (shaderString) => {\r\n                    if (shaderString instanceof ArrayBuffer) {\r\n                        return;\r\n                    }\r\n\r\n                    gltfRuntime.loadedShaderCount++;\r\n\r\n                    if (shaderString) {\r\n                        Effect.ShadersStore[sha + (shader.type === EShaderType.VERTEX ? \"VertexShader\" : \"PixelShader\")] = shaderString;\r\n                    }\r\n\r\n                    if (gltfRuntime.loadedShaderCount === gltfRuntime.shaderscount) {\r\n                        onload();\r\n                    }\r\n                },\r\n                () => {\r\n                    Tools.Error(\"Error when loading shader program named \" + sha + \" located at \" + shader.uri);\r\n                }\r\n            );\r\n        };\r\n\r\n        for (const sha in gltfRuntime.shaders) {\r\n            hasShaders = true;\r\n\r\n            const shader: IGLTFShader = gltfRuntime.shaders[sha];\r\n            if (shader) {\r\n                processShader.bind(this, sha, shader)();\r\n            } else {\r\n                Tools.Error(\"No shader named: \" + sha);\r\n            }\r\n        }\r\n\r\n        if (!hasShaders) {\r\n            onload();\r\n        }\r\n    }\r\n\r\n    private _loadBuffersAsync(gltfRuntime: IGLTFRuntime, onLoad: () => void): void {\r\n        let hasBuffers = false;\r\n\r\n        const processBuffer = (buf: string, buffer: IGLTFBuffer) => {\r\n            GLTFLoaderExtension.LoadBufferAsync(\r\n                gltfRuntime,\r\n                buf,\r\n                (bufferView) => {\r\n                    gltfRuntime.loadedBufferCount++;\r\n\r\n                    if (bufferView) {\r\n                        if (bufferView.byteLength != gltfRuntime.buffers[buf].byteLength) {\r\n                            Tools.Error(\"Buffer named \" + buf + \" is length \" + bufferView.byteLength + \". Expected: \" + buffer.byteLength); // Improve error message\r\n                        }\r\n\r\n                        gltfRuntime.loadedBufferViews[buf] = bufferView;\r\n                    }\r\n\r\n                    if (gltfRuntime.loadedBufferCount === gltfRuntime.buffersCount) {\r\n                        onLoad();\r\n                    }\r\n                },\r\n                () => {\r\n                    Tools.Error(\"Error when loading buffer named \" + buf + \" located at \" + buffer.uri);\r\n                }\r\n            );\r\n        };\r\n\r\n        for (const buf in gltfRuntime.buffers) {\r\n            hasBuffers = true;\r\n\r\n            const buffer: IGLTFBuffer = gltfRuntime.buffers[buf];\r\n            if (buffer) {\r\n                processBuffer.bind(this, buf, buffer)();\r\n            } else {\r\n                Tools.Error(\"No buffer named: \" + buf);\r\n            }\r\n        }\r\n\r\n        if (!hasBuffers) {\r\n            onLoad();\r\n        }\r\n    }\r\n\r\n    private _createNodes(gltfRuntime: IGLTFRuntime): void {\r\n        let currentScene = <IGLTFScene>gltfRuntime.currentScene;\r\n\r\n        if (currentScene) {\r\n            // Only one scene even if multiple scenes are defined\r\n            for (let i = 0; i < currentScene.nodes.length; i++) {\r\n                traverseNodes(gltfRuntime, currentScene.nodes[i], null);\r\n            }\r\n        } else {\r\n            // Load all scenes\r\n            for (const thing in gltfRuntime.scenes) {\r\n                currentScene = <IGLTFScene>gltfRuntime.scenes[thing];\r\n\r\n                for (let i = 0; i < currentScene.nodes.length; i++) {\r\n                    traverseNodes(gltfRuntime, currentScene.nodes[i], null);\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport abstract class GLTFLoaderExtension {\r\n    private _name: string;\r\n\r\n    public constructor(name: string) {\r\n        this._name = name;\r\n    }\r\n\r\n    public get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for loading the runtime\r\n     * Return true to stop further extensions from loading the runtime\r\n     * @param scene\r\n     * @param data\r\n     * @param rootUrl\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public loadRuntimeAsync(scene: Scene, data: IGLTFLoaderData, rootUrl: string, onSuccess?: (gltfRuntime: IGLTFRuntime) => void, onError?: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an onverride for creating gltf runtime\r\n     * Return true to stop further extensions from creating the runtime\r\n     * @param gltfRuntime\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public loadRuntimeExtensionsAsync(gltfRuntime: IGLTFRuntime, onSuccess: () => void, onError?: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for loading buffers\r\n     * Return true to stop further extensions from loading this buffer\r\n     * @param gltfRuntime\r\n     * @param id\r\n     * @param onSuccess\r\n     * @param onError\r\n     * @param onProgress\r\n     */\r\n    public loadBufferAsync(\r\n        gltfRuntime: IGLTFRuntime,\r\n        id: string,\r\n        onSuccess: (buffer: ArrayBufferView) => void,\r\n        onError: (message: string) => void,\r\n        onProgress?: () => void\r\n    ): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for loading texture buffers\r\n     * Return true to stop further extensions from loading this texture data\r\n     * @param gltfRuntime\r\n     * @param id\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public loadTextureBufferAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (buffer: ArrayBufferView) => void, onError: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for creating textures\r\n     * Return true to stop further extensions from loading this texture\r\n     * @param gltfRuntime\r\n     * @param id\r\n     * @param buffer\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public createTextureAsync(gltfRuntime: IGLTFRuntime, id: string, buffer: ArrayBufferView, onSuccess: (texture: Texture) => void, onError: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for loading shader strings\r\n     * Return true to stop further extensions from loading this shader data\r\n     * @param gltfRuntime\r\n     * @param id\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public loadShaderStringAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (shaderString: string) => void, onError: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Defines an override for loading materials\r\n     * Return true to stop further extensions from loading this material\r\n     * @param gltfRuntime\r\n     * @param id\r\n     * @param onSuccess\r\n     * @param onError\r\n     */\r\n    public loadMaterialAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (material: Material) => void, onError: (message: string) => void): boolean {\r\n        return false;\r\n    }\r\n\r\n    // ---------\r\n    // Utilities\r\n    // ---------\r\n\r\n    public static LoadRuntimeAsync(\r\n        scene: Scene,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        onSuccess?: (gltfRuntime: IGLTFRuntime) => void,\r\n        onError?: (message: string) => void\r\n    ): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadRuntimeAsync(scene, data, rootUrl, onSuccess, onError);\r\n            },\r\n            () => {\r\n                setTimeout(() => {\r\n                    if (!onSuccess) {\r\n                        return;\r\n                    }\r\n                    onSuccess(GLTFLoaderBase.CreateRuntime(data.json, scene, rootUrl));\r\n                });\r\n            }\r\n        );\r\n    }\r\n\r\n    public static LoadRuntimeExtensionsAsync(gltfRuntime: IGLTFRuntime, onSuccess: () => void, onError?: (message: string) => void): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadRuntimeExtensionsAsync(gltfRuntime, onSuccess, onError);\r\n            },\r\n            () => {\r\n                setTimeout(() => {\r\n                    onSuccess();\r\n                });\r\n            }\r\n        );\r\n    }\r\n\r\n    public static LoadBufferAsync(\r\n        gltfRuntime: IGLTFRuntime,\r\n        id: string,\r\n        onSuccess: (bufferView: ArrayBufferView) => void,\r\n        onError: (message: string) => void,\r\n        onProgress?: () => void\r\n    ): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadBufferAsync(gltfRuntime, id, onSuccess, onError, onProgress);\r\n            },\r\n            () => {\r\n                GLTFLoaderBase.LoadBufferAsync(gltfRuntime, id, onSuccess, onError, onProgress);\r\n            }\r\n        );\r\n    }\r\n\r\n    public static LoadTextureAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (texture: Texture) => void, onError: (message: string) => void): void {\r\n        GLTFLoaderExtension._LoadTextureBufferAsync(\r\n            gltfRuntime,\r\n            id,\r\n            (buffer) => {\r\n                if (buffer) {\r\n                    GLTFLoaderExtension._CreateTextureAsync(gltfRuntime, id, buffer, onSuccess, onError);\r\n                }\r\n            },\r\n            onError\r\n        );\r\n    }\r\n\r\n    public static LoadShaderStringAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (shaderData: string | ArrayBuffer) => void, onError: (message: string) => void): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadShaderStringAsync(gltfRuntime, id, onSuccess, onError);\r\n            },\r\n            () => {\r\n                GLTFLoaderBase.LoadShaderStringAsync(gltfRuntime, id, onSuccess, onError);\r\n            }\r\n        );\r\n    }\r\n\r\n    public static LoadMaterialAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (material: Material) => void, onError: (message: string) => void): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadMaterialAsync(gltfRuntime, id, onSuccess, onError);\r\n            },\r\n            () => {\r\n                GLTFLoaderBase.LoadMaterialAsync(gltfRuntime, id, onSuccess, onError);\r\n            }\r\n        );\r\n    }\r\n\r\n    private static _LoadTextureBufferAsync(\r\n        gltfRuntime: IGLTFRuntime,\r\n        id: string,\r\n        onSuccess: (buffer: Nullable<ArrayBufferView>) => void,\r\n        onError: (message: string) => void\r\n    ): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.loadTextureBufferAsync(gltfRuntime, id, onSuccess, onError);\r\n            },\r\n            () => {\r\n                GLTFLoaderBase.LoadTextureBufferAsync(gltfRuntime, id, onSuccess, onError);\r\n            }\r\n        );\r\n    }\r\n\r\n    private static _CreateTextureAsync(\r\n        gltfRuntime: IGLTFRuntime,\r\n        id: string,\r\n        buffer: ArrayBufferView,\r\n        onSuccess: (texture: Texture) => void,\r\n        onError: (message: string) => void\r\n    ): void {\r\n        GLTFLoaderExtension._ApplyExtensions(\r\n            (loaderExtension) => {\r\n                return loaderExtension.createTextureAsync(gltfRuntime, id, buffer, onSuccess, onError);\r\n            },\r\n            () => {\r\n                GLTFLoaderBase.CreateTextureAsync(gltfRuntime, id, buffer, onSuccess);\r\n            }\r\n        );\r\n    }\r\n\r\n    private static _ApplyExtensions(func: (loaderExtension: GLTFLoaderExtension) => boolean, defaultFunc: () => void): void {\r\n        for (const extensionName in GLTFLoader.Extensions) {\r\n            const loaderExtension = GLTFLoader.Extensions[extensionName];\r\n            if (func(loaderExtension)) {\r\n                return;\r\n            }\r\n        }\r\n\r\n        defaultFunc();\r\n    }\r\n}\r\n\r\nGLTFFileLoader._CreateGLTF1Loader = () => new GLTFLoader();\r\n"]}