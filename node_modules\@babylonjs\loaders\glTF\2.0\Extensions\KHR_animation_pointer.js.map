{"version": 3, "file": "KHR_animation_pointer.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_animation_pointer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAQ3C,OAAO,EAAE,MAAM,EAAE,uCAAyB;AAC1C,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AAEpE,MAAM,IAAI,GAAG,uBAAuB,CAAC;AAErC;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,qBAAqB;IAQ9B;;OAEG;IACH,YAAY,MAAkB;QAV9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAQxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;;;;;;OAQG;IACI,0BAA0B,CAC7B,OAAe,EACf,gBAAwB,EACxB,SAAqB,EACrB,OAA0B,EAC1B,MAA6E;;QAE7E,MAAM,SAAS,GAAG,MAAA,OAAO,CAAC,MAAM,CAAC,UAAU,0CAAE,qBAA6C,CAAC;QAC3F,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,uDAAuC,EAAE;YAC5D,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,wBAAwB,OAAO,CAAC,MAAM,CAAC,IAAI,cAAc,kDAAkC,oBAAoB,IAAI,CAAC,IAAI,YAAY,CAAC,CAAC;SAC/J;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,wBAAwB,OAAO,CAAC,MAAM,CAAC,IAAI,wCAAwC,IAAI,CAAC,IAAI,YAAY,CAAC,CAAC;SACnI;QAED,MAAM,gBAAgB,GAAG,GAAG,OAAO,eAAe,IAAI,CAAC,IAAI,EAAE,CAAC;QAE9D,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,GAAG,gBAAgB,sBAAsB,CAAC,CAAC;SAC9D;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,gBAAgB,UAAU,EAAE,OAAO,CAAC,CAAC;QACvF,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,8BAA8B,OAAO,WAAW,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;SACf;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,wCAAwC,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACpI,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACK,sBAAsB,CAAC,OAAe,EAAE,OAAe;QAC3D,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAC1B,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,YAAY,OAAO,2BAA2B,CAAC,CAAC;YACtE,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjC,2FAA2F;QAC3F,KAAK,CAAC,KAAK,EAAE,CAAC;QAEd,IAAI,IAAI,GAAQ,oBAAoB,CAAC;QACrC,IAAI,eAAe,GAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7C,IAAI,cAAc,GAAQ,SAAS,CAAC;QACpC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;aACzB;iBAAM;gBACH,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClB,IAAI,CAAC,IAAI,EAAE;oBACP,OAAO,IAAI,CAAC;iBACf;aACJ;YAED,eAAe,GAAG,eAAe,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,cAAc,GAAG,eAAe,CAAC;aACpC;SACJ;QAED,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACzC,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,IAAI;SACnB,CAAC;IACN,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IAnimationTargetInfo } from \"../glTFLoader\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { Animation } from \"core/Animations/animation\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport type { IAnimation, IAnimationChannel } from \"../glTFLoaderInterfaces\";\r\nimport type { IKHRAnimationPointer } from \"babylonjs-gltf2interface\";\r\nimport { AnimationChannelTargetPath } from \"babylonjs-gltf2interface\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { animationPointerTree } from \"./KHR_animation_pointer.data\";\r\n\r\nconst NAME = \"KHR_animation_pointer\";\r\n\r\n/**\r\n * [Specification PR](https://github.com/KhronosGroup/glTF/pull/2147)\r\n * !!! Experimental Extension Subject to Changes !!!\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_animation_pointer implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n    }\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public get enabled(): boolean {\r\n        return this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF animation channel.\r\n     * @param context The context when loading the asset\r\n     * @param animationContext The context of the animation when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @param channel The glTF animation channel property\r\n     * @param onLoad Called for each animation loaded\r\n     * @returns A void promise that resolves when the load is complete or null if not handled\r\n     */\r\n    public _loadAnimationChannelAsync(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        channel: IAnimationChannel,\r\n        onLoad: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): Nullable<Promise<void>> {\r\n        const extension = channel.target.extensions?.KHR_animation_pointer as IKHRAnimationPointer;\r\n        if (!extension) {\r\n            return null;\r\n        }\r\n\r\n        if (channel.target.path !== AnimationChannelTargetPath.POINTER) {\r\n            Logger.Warn(`${context}/target/path: Value (${channel.target.path}) must be (${AnimationChannelTargetPath.POINTER}) when using the ${this.name} extension`);\r\n        }\r\n\r\n        if (channel.target.node != undefined) {\r\n            Logger.Warn(`${context}/target/node: Value (${channel.target.node}) must not be present when using the ${this.name} extension`);\r\n        }\r\n\r\n        const extensionContext = `${context}/extensions/${this.name}`;\r\n\r\n        const pointer = extension.pointer;\r\n        if (!pointer) {\r\n            throw new Error(`${extensionContext}: Pointer is missing`);\r\n        }\r\n\r\n        const targetInfo = this._parseAnimationPointer(`${extensionContext}/pointer`, pointer);\r\n        if (!targetInfo) {\r\n            Logger.Warn(`${extensionContext}/pointer: Invalid pointer (${pointer}) skipped`);\r\n            return null;\r\n        }\r\n\r\n        return this._loader._loadAnimationChannelFromTargetInfoAsync(context, animationContext, animation, channel, targetInfo, onLoad);\r\n    }\r\n\r\n    /**\r\n     * The pointer string is represented by a [JSON pointer](https://datatracker.ietf.org/doc/html/rfc6901).\r\n     * <animationPointer> := /<rootNode>/<assetIndex>/<propertyPath>\r\n     * <rootNode> := \"nodes\" | \"materials\" | \"meshes\" | \"cameras\" | \"extensions\"\r\n     * <assetIndex> := <digit> | <name>\r\n     * <propertyPath> := <extensionPath> | <standardPath>\r\n     * <extensionPath> := \"extensions\"/<name>/<standardPath>\r\n     * <standardPath> := <name> | <name>/<standardPath>\r\n     * <name> := W+\r\n     * <digit> := D+\r\n     *\r\n     * Examples:\r\n     *  - \"/nodes/0/rotation\"\r\n     *  - \"/materials/2/emissiveFactor\"\r\n     *  - \"/materials/2/pbrMetallicRoughness/baseColorFactor\"\r\n     *  - \"/materials/2/extensions/KHR_materials_emissive_strength/emissiveStrength\"\r\n     */\r\n    private _parseAnimationPointer(context: string, pointer: string): Nullable<IAnimationTargetInfo> {\r\n        if (!pointer.startsWith(\"/\")) {\r\n            Logger.Warn(`${context}: Value (${pointer}) must start with a slash`);\r\n            return null;\r\n        }\r\n\r\n        const parts = pointer.split(\"/\");\r\n\r\n        // Remove the first part since it will be empty string as pointers must start with a slash.\r\n        parts.shift();\r\n\r\n        let node: any = animationPointerTree;\r\n        let gltfCurrentNode: any = this._loader.gltf;\r\n        let gltfTargetNode: any = undefined;\r\n        for (const part of parts) {\r\n            if (node.__array__) {\r\n                node = node.__array__;\r\n            } else {\r\n                node = node[part];\r\n                if (!node) {\r\n                    return null;\r\n                }\r\n            }\r\n\r\n            gltfCurrentNode = gltfCurrentNode && gltfCurrentNode[part];\r\n\r\n            if (node.__target__) {\r\n                gltfTargetNode = gltfCurrentNode;\r\n            }\r\n        }\r\n\r\n        if (!gltfTargetNode || !Array.isArray(node)) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            target: gltfTargetNode,\r\n            properties: node,\r\n        };\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_animation_pointer(loader));\r\n"]}