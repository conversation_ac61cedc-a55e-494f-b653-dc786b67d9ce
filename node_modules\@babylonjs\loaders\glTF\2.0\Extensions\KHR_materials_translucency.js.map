{"version": 3, "file": "KHR_materials_translucency.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_translucency.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,4BAA4B,CAAC;AAE1C;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,0BAA0B;IAkBnC;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,sBAAsB,GAAG,IAAI,CAAC;SAC/C;IACL,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAA4B,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC1H,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAChG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC;YAC5G,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,+BAA+B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB,EAAE,SAAoC;QACzI,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QACD,MAAM,WAAW,GAAG,eAA8B,CAAC;QAEnD,+EAA+E;QAC/E,WAAW,CAAC,UAAU,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAEpD,+EAA+E;QAC/E,kDAAkD;QAClD,WAAW,CAAC,UAAU,CAAC,uBAAuB,GAAG,GAAG,CAAC;QACrD,WAAW,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAC9C,WAAW,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAE9C,wCAAwC;QACxC,WAAW,CAAC,UAAU,CAAC,2BAA2B,GAAG,IAAI,CAAC;QAE1D,IAAI,SAAS,CAAC,kBAAkB,KAAK,SAAS,EAAE;YAC5C,WAAW,CAAC,UAAU,CAAC,qBAAqB,GAAG,SAAS,CAAC,kBAAkB,CAAC;SAC/E;aAAM;YACH,WAAW,CAAC,UAAU,CAAC,qBAAqB,GAAG,GAAG,CAAC;YACnD,WAAW,CAAC,UAAU,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACrD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,SAAS,CAAC,mBAAmB,EAAE;YAC9B,SAAS,CAAC,mBAAoC,CAAC,YAAY,GAAG,IAAI,CAAC;YACpE,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,sBAAsB,EAAE,SAAS,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,OAAoB,EAAE,EAAE;gBACpI,WAAW,CAAC,UAAU,CAAC,4BAA4B,GAAG,OAAO,CAAC;YAClE,CAAC,CAAC,CAAC;SACN;aAAM;YACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;IACL,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsTranslucency } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_translucency\";\r\n\r\n/**\r\n * [Proposed Specification](https://github.com/KhronosGroup/glTF/pull/1825)\r\n * !!! Experimental Extension Subject to Changes !!!\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_translucency implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 174;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n        if (this.enabled) {\r\n            loader.parent.transparencyAsCoverage = true;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsTranslucency>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadTranslucentPropertiesAsync(extensionContext, material, babylonMaterial, extension));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadTranslucentPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material, extension: IKHRMaterialsTranslucency): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n        const pbrMaterial = babylonMaterial as PBRMaterial;\r\n\r\n        // Enables \"translucency\" texture which represents diffusely-transmitted light.\r\n        pbrMaterial.subSurface.isTranslucencyEnabled = true;\r\n\r\n        // Since this extension models thin-surface transmission only, we must make the\r\n        // internal IOR == 1.0 and set the thickness to 0.\r\n        pbrMaterial.subSurface.volumeIndexOfRefraction = 1.0;\r\n        pbrMaterial.subSurface.minimumThickness = 0.0;\r\n        pbrMaterial.subSurface.maximumThickness = 0.0;\r\n\r\n        // Albedo colour will tint transmission.\r\n        pbrMaterial.subSurface.useAlbedoToTintTranslucency = true;\r\n\r\n        if (extension.translucencyFactor !== undefined) {\r\n            pbrMaterial.subSurface.translucencyIntensity = extension.translucencyFactor;\r\n        } else {\r\n            pbrMaterial.subSurface.translucencyIntensity = 0.0;\r\n            pbrMaterial.subSurface.isTranslucencyEnabled = false;\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (extension.translucencyTexture) {\r\n            (extension.translucencyTexture as ITextureInfo).nonColorData = true;\r\n            return this._loader.loadTextureInfoAsync(`${context}/translucencyTexture`, extension.translucencyTexture).then((texture: BaseTexture) => {\r\n                pbrMaterial.subSurface.translucencyIntensityTexture = texture;\r\n            });\r\n        } else {\r\n            return Promise.resolve();\r\n        }\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_translucency(loader));\r\n"]}