{"version": 3, "file": "objFileLoader.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/OBJ/objFileLoader.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,6CAA+B;AACjD,OAAO,EAAE,KAAK,EAAE,sCAAwB;AAGxC,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,OAAO,EAAE,cAAc,EAAE,0CAA4B;AAGrD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAG5C;;;GAGG;AACH,MAAM,OAAO,aAAa;IAStB;;OAEG;IACI,MAAM,KAAK,gBAAgB;QAC9B,OAAO,aAAa,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IAEM,MAAM,KAAK,gBAAgB,CAAC,KAAc;QAC7C,aAAa,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAC3C,CAAC;IA2CD;;;;OAIG;IACH,YAAY,cAAkC;QAlB9C;;WAEG;QACI,SAAI,GAAG,KAAK,CAAC;QACpB;;WAEG;QACI,eAAU,GAAG,MAAM,CAAC;QAEnB,oBAAe,GAA6B,IAAI,CAAC;QAUrD,IAAI,CAAC,eAAe,GAAG,cAAc,IAAI,aAAa,CAAC,sBAAsB,CAAC;IAClF,CAAC;IAEO,MAAM,KAAK,sBAAsB;QACrC,OAAO;YACH,cAAc,EAAE,aAAa,CAAC,eAAe;YAC7C,eAAe,EAAE,aAAa,CAAC,gBAAgB;YAC/C,kBAAkB,EAAE,aAAa,CAAC,oBAAoB;YACtD,OAAO,EAAE,aAAa,CAAC,QAAQ;YAC/B,cAAc,EAAE,aAAa,CAAC,gBAAgB;YAC9C,gEAAgE;YAChE,SAAS,EAAE,aAAa,CAAC,UAAU;YACnC,4BAA4B,EAAE,aAAa,CAAC,+BAA+B;YAC3E,cAAc,EAAE,aAAa,CAAC,gBAAgB;YAC9C,aAAa,EAAE,aAAa,CAAC,cAAc;SAC9C,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG;IACK,QAAQ,CACZ,GAAW,EACX,OAAe,EACf,SAAwE,EACxE,SAAwD;QAExD,mCAAmC;QACnC,MAAM,UAAU,GAAG,OAAO,GAAG,GAAG,CAAC;QAEjC,6DAA6D;QAC7D,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,OAAgC,EAAE,SAAe,EAAE,EAAE;YACrH,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACH,YAAY;QACR,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAC,WAAgB,EAAE,KAAY,EAAE,IAAS,EAAE,OAAe;QAC7E,8BAA8B;QAC9B,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACvE,OAAO;gBACH,MAAM,EAAE,MAAM;gBACd,eAAe,EAAE,EAAE;gBACnB,SAAS,EAAE,EAAE;gBACb,eAAe,EAAE,EAAE;gBACnB,cAAc,EAAE,EAAE;gBAClB,UAAU,EAAE,EAAE;gBACd,MAAM,EAAE,EAAE;aACb,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,KAAY,EAAE,IAAY,EAAE,OAAe;QACxD,kBAAkB;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAC9D,cAAc;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,KAAY,EAAE,IAAY,EAAE,OAAe;QACtE,MAAM,SAAS,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QAEjC,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;aAClD,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACb,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,QAAQ,EAAE;oBACV,YAAY;oBACZ,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE;wBAC7C,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAEnC,WAAW;wBACX,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;wBAC9C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;4BACnB,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;gCACrC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;6BAC9B;wBACL,CAAC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,OAAO,SAAS,CAAC;QACrB,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE;YACV,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,MAAM,EAAE,CAAC;QACb,CAAC,CAAC,CAAC;IACX,CAAC;IAED;;;;;;;;;OASG;IACK,WAAW,CAAC,WAAgB,EAAE,KAAY,EAAE,IAAY,EAAE,OAAe;QAC7E,IAAI,UAAU,GAAW,EAAE,CAAC,CAAC,iCAAiC;QAC9D,MAAM,oBAAoB,GAAkB,IAAI,aAAa,EAAE,CAAC;QAChE,MAAM,aAAa,GAAG,IAAI,KAAK,EAAU,CAAC;QAC1C,MAAM,kBAAkB,GAAgB,EAAE,CAAC,CAAC,sBAAsB;QAElE,gBAAgB;QAChB,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,aAAa,EAAE,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAE7F,WAAW,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC,QAAgB,EAAE,EAAE;YACnF,UAAU,GAAG,QAAQ,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,kCAAkC;QAClC,IAAI,UAAU,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;YAC1D,6BAA6B;YAC7B,WAAW,CAAC,IAAI,CACZ,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC5B,IAAI,CAAC,QAAQ,CACT,UAAU,EACV,OAAO,EACP,CAAC,UAAU,EAAE,EAAE;oBACX,IAAI;wBACA,4CAA4C;wBAC5C,oBAAoB,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;wBAChF,8CAA8C;wBAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC5D,0DAA0D;4BAC1D,IAAI,UAAU,GAAG,CAAC,CAAC;4BACnB,MAAM,QAAQ,GAAG,EAAE,CAAC;4BACpB,IAAI,MAAM,CAAC;4BAEX,yDAAyD;4BACzD,6BAA6B;4BAC7B,oDAAoD;4BACpD,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gCAC9F,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCACtB,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;6BAC3B;4BACD,wCAAwC;4BACxC,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;gCACxC,0CAA0C;gCAC1C,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;6BAC/C;iCAAM;gCACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oCACtC,gEAAgE;oCAChE,MAAM,IAAI,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;oCAC7C,MAAM,QAAQ,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oCACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;oCAEzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE;wCACzB,6CAA6C;wCAC7C,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC;qCAC/B;iCACJ;6BACJ;yBACJ;wBACD,OAAO,EAAE,CAAC;qBACb;oBAAC,OAAO,CAAC,EAAE;wBACR,KAAK,CAAC,IAAI,CAAC,+BAA+B,UAAU,GAAG,CAAC,CAAC;wBACzD,IAAI,IAAI,CAAC,eAAe,CAAC,4BAA4B,EAAE;4BACnD,OAAO,EAAE,CAAC;yBACb;6BAAM;4BACH,MAAM,CAAC,CAAC,CAAC,CAAC;yBACb;qBACJ;gBACL,CAAC,EACD,CAAC,UAAkB,EAAE,SAAe,EAAE,EAAE;oBACpC,KAAK,CAAC,IAAI,CAAC,gCAAgC,UAAU,GAAG,CAAC,CAAC;oBAC1D,IAAI,IAAI,CAAC,eAAe,CAAC,4BAA4B,EAAE;wBACnD,OAAO,EAAE,CAAC;qBACb;yBAAM;wBACH,MAAM,CAAC,SAAS,CAAC,CAAC;qBACrB;gBACL,CAAC,CACJ,CAAC;YACN,CAAC,CAAC,CACL,CAAC;SACL;QACD,+BAA+B;QAC/B,OAAO,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtC,OAAO,kBAAkB,CAAC;QAC9B,CAAC,CAAC,CAAC;IACP,CAAC;;AA1SD;;GAEG;AACW,8BAAgB,GAAG,IAAI,CAAC;AACtC;;GAEG;AACW,sBAAQ,GAAG,KAAK,CAAC;AAY/B;;GAEG;AACW,kCAAoB,GAAG,KAAK,CAAC;AAC3C;;GAEG;AACW,6BAAe,GAAG,KAAK,CAAC;AACtC;;;GAGG;AACW,8BAAgB,GAAG,KAAK,CAAC;AACvC;;GAEG;AACW,wBAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7C;;GAEG;AACW,4BAAc,GAAG,KAAK,CAAC;AAErC;;;;GAIG;AACW,6CAA+B,GAAG,IAAI,CAAC;AA+PzD,IAAI,WAAW,EAAE;IACb,0CAA0C;IAC1C,WAAW,CAAC,cAAc,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC;CACnD", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Vector2 } from \"core/Maths/math.vector\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { ISceneLoaderPluginAsync, ISceneLoaderPluginFactory, ISceneLoaderPlugin, ISceneLoaderAsyncResult } from \"core/Loading/sceneLoader\";\r\nimport { SceneLoader } from \"core/Loading/sceneLoader\";\r\nimport { AssetContainer } from \"core/assetContainer\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { WebRequest } from \"core/Misc/webRequest\";\r\nimport { MTLFileLoader } from \"./mtlFileLoader\";\r\nimport type { OBJLoadingOptions } from \"./objLoadingOptions\";\r\nimport { SolidParser } from \"./solidParser\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\n\r\n/**\r\n * OBJ file type loader.\r\n * This is a babylon scene loader plugin.\r\n */\r\nexport class OBJFileLoader implements ISceneLoaderPluginAsync, ISceneLoaderPluginFactory {\r\n    /**\r\n     * Defines if UVs are optimized by default during load.\r\n     */\r\n    public static OPTIMIZE_WITH_UV = true;\r\n    /**\r\n     * Invert model on y-axis (does a model scaling inversion)\r\n     */\r\n    public static INVERT_Y = false;\r\n    /**\r\n     * Invert Y-Axis of referenced textures on load\r\n     */\r\n    public static get INVERT_TEXTURE_Y() {\r\n        return MTLFileLoader.INVERT_TEXTURE_Y;\r\n    }\r\n\r\n    public static set INVERT_TEXTURE_Y(value: boolean) {\r\n        MTLFileLoader.INVERT_TEXTURE_Y = value;\r\n    }\r\n\r\n    /**\r\n     * Include in meshes the vertex colors available in some OBJ files.  This is not part of OBJ standard.\r\n     */\r\n    public static IMPORT_VERTEX_COLORS = false;\r\n    /**\r\n     * Compute the normals for the model, even if normals are present in the file.\r\n     */\r\n    public static COMPUTE_NORMALS = false;\r\n    /**\r\n     * Optimize the normals for the model. Lighting can be uneven if you use OptimizeWithUV = true because new vertices can be created for the same location if they pertain to different faces.\r\n     * Using OptimizehNormals = true will help smoothing the lighting by averaging the normals of those vertices.\r\n     */\r\n    public static OPTIMIZE_NORMALS = false;\r\n    /**\r\n     * Defines custom scaling of UV coordinates of loaded meshes.\r\n     */\r\n    public static UV_SCALING = new Vector2(1, 1);\r\n    /**\r\n     * Skip loading the materials even if defined in the OBJ file (materials are ignored).\r\n     */\r\n    public static SKIP_MATERIALS = false;\r\n\r\n    /**\r\n     * When a material fails to load OBJ loader will silently fail and onSuccess() callback will be triggered.\r\n     *\r\n     * Defaults to true for backwards compatibility.\r\n     */\r\n    public static MATERIAL_LOADING_FAILS_SILENTLY = true;\r\n    /**\r\n     * Defines the name of the plugin.\r\n     */\r\n    public name = \"obj\";\r\n    /**\r\n     * Defines the extension the plugin is able to load.\r\n     */\r\n    public extensions = \".obj\";\r\n\r\n    private _assetContainer: Nullable<AssetContainer> = null;\r\n\r\n    private _loadingOptions: OBJLoadingOptions;\r\n\r\n    /**\r\n     * Creates loader for .OBJ files\r\n     *\r\n     * @param loadingOptions options for loading and parsing OBJ/MTL files.\r\n     */\r\n    constructor(loadingOptions?: OBJLoadingOptions) {\r\n        this._loadingOptions = loadingOptions || OBJFileLoader._DefaultLoadingOptions;\r\n    }\r\n\r\n    private static get _DefaultLoadingOptions(): OBJLoadingOptions {\r\n        return {\r\n            computeNormals: OBJFileLoader.COMPUTE_NORMALS,\r\n            optimizeNormals: OBJFileLoader.OPTIMIZE_NORMALS,\r\n            importVertexColors: OBJFileLoader.IMPORT_VERTEX_COLORS,\r\n            invertY: OBJFileLoader.INVERT_Y,\r\n            invertTextureY: OBJFileLoader.INVERT_TEXTURE_Y,\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            UVScaling: OBJFileLoader.UV_SCALING,\r\n            materialLoadingFailsSilently: OBJFileLoader.MATERIAL_LOADING_FAILS_SILENTLY,\r\n            optimizeWithUV: OBJFileLoader.OPTIMIZE_WITH_UV,\r\n            skipMaterials: OBJFileLoader.SKIP_MATERIALS,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Calls synchronously the MTL file attached to this obj.\r\n     * Load function or importMesh function don't enable to load 2 files in the same time asynchronously.\r\n     * Without this function materials are not displayed in the first frame (but displayed after).\r\n     * In consequence it is impossible to get material information in your HTML file\r\n     *\r\n     * @param url The URL of the MTL file\r\n     * @param rootUrl defines where to load data from\r\n     * @param onSuccess Callback function to be called when the MTL file is loaded\r\n     * @param onFailure\r\n     */\r\n    private _loadMTL(\r\n        url: string,\r\n        rootUrl: string,\r\n        onSuccess: (response: string | ArrayBuffer, responseUrl?: string) => any,\r\n        onFailure: (pathOfFile: string, exception?: any) => void\r\n    ) {\r\n        //The complete path to the mtl file\r\n        const pathOfFile = rootUrl + url;\r\n\r\n        // Loads through the babylon tools to allow fileInput search.\r\n        Tools.LoadFile(pathOfFile, onSuccess, undefined, undefined, false, (request?: WebRequest | undefined, exception?: any) => {\r\n            onFailure(pathOfFile, exception);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Instantiates a OBJ file loader plugin.\r\n     * @returns the created plugin\r\n     */\r\n    createPlugin(): ISceneLoaderPluginAsync | ISceneLoaderPlugin {\r\n        return new OBJFileLoader(OBJFileLoader._DefaultLoadingOptions);\r\n    }\r\n\r\n    /**\r\n     * If the data string can be loaded directly.\r\n     * @returns if the data can be loaded directly\r\n     */\r\n    public canDirectLoad(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Imports one or more meshes from the loaded OBJ data and adds them to the scene\r\n     * @param meshesNames a string or array of strings of the mesh names that should be loaded from the file\r\n     * @param scene the scene the meshes should be added to\r\n     * @param data the OBJ data to load\r\n     * @param rootUrl root url to load from\r\n     * @returns a promise containing the loaded meshes, particles, skeletons and animations\r\n     */\r\n    public importMeshAsync(meshesNames: any, scene: Scene, data: any, rootUrl: string): Promise<ISceneLoaderAsyncResult> {\r\n        //get the meshes from OBJ file\r\n        return this._parseSolid(meshesNames, scene, data, rootUrl).then((meshes) => {\r\n            return {\r\n                meshes: meshes,\r\n                particleSystems: [],\r\n                skeletons: [],\r\n                animationGroups: [],\r\n                transformNodes: [],\r\n                geometries: [],\r\n                lights: [],\r\n            };\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Imports all objects from the loaded OBJ data and adds them to the scene\r\n     * @param scene the scene the objects should be added to\r\n     * @param data the OBJ data to load\r\n     * @param rootUrl root url to load from\r\n     * @returns a promise which completes when objects have been loaded to the scene\r\n     */\r\n    public loadAsync(scene: Scene, data: string, rootUrl: string): Promise<void> {\r\n        //Get the 3D model\r\n        return this.importMeshAsync(null, scene, data, rootUrl).then(() => {\r\n            // return void\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @returns The loaded asset container\r\n     */\r\n    public loadAssetContainerAsync(scene: Scene, data: string, rootUrl: string): Promise<AssetContainer> {\r\n        const container = new AssetContainer(scene);\r\n        this._assetContainer = container;\r\n\r\n        return this.importMeshAsync(null, scene, data, rootUrl)\r\n            .then((result) => {\r\n                result.meshes.forEach((mesh) => container.meshes.push(mesh));\r\n                result.meshes.forEach((mesh) => {\r\n                    const material = mesh.material;\r\n                    if (material) {\r\n                        // Materials\r\n                        if (container.materials.indexOf(material) == -1) {\r\n                            container.materials.push(material);\r\n\r\n                            // Textures\r\n                            const textures = material.getActiveTextures();\r\n                            textures.forEach((t) => {\r\n                                if (container.textures.indexOf(t) == -1) {\r\n                                    container.textures.push(t);\r\n                                }\r\n                            });\r\n                        }\r\n                    }\r\n                });\r\n                this._assetContainer = null;\r\n                return container;\r\n            })\r\n            .catch((ex) => {\r\n                this._assetContainer = null;\r\n                throw ex;\r\n            });\r\n    }\r\n\r\n    /**\r\n     * Read the OBJ file and create an Array of meshes.\r\n     * Each mesh contains all information given by the OBJ and the MTL file.\r\n     * i.e. vertices positions and indices, optional normals values, optional UV values, optional material\r\n     * @param meshesNames defines a string or array of strings of the mesh names that should be loaded from the file\r\n     * @param scene defines the scene where are displayed the data\r\n     * @param data defines the content of the obj file\r\n     * @param rootUrl defines the path to the folder\r\n     * @returns the list of loaded meshes\r\n     */\r\n    private _parseSolid(meshesNames: any, scene: Scene, data: string, rootUrl: string): Promise<Array<AbstractMesh>> {\r\n        let fileToLoad: string = \"\"; //The name of the mtlFile to load\r\n        const materialsFromMTLFile: MTLFileLoader = new MTLFileLoader();\r\n        const materialToUse = new Array<string>();\r\n        const babylonMeshesArray: Array<Mesh> = []; //The mesh for babylon\r\n\r\n        // Main function\r\n        const solidParser = new SolidParser(materialToUse, babylonMeshesArray, this._loadingOptions);\r\n\r\n        solidParser.parse(meshesNames, data, scene, this._assetContainer, (fileName: string) => {\r\n            fileToLoad = fileName;\r\n        });\r\n\r\n        // load the materials\r\n        const mtlPromises: Array<Promise<void>> = [];\r\n        // Check if we have a file to load\r\n        if (fileToLoad !== \"\" && !this._loadingOptions.skipMaterials) {\r\n            //Load the file synchronously\r\n            mtlPromises.push(\r\n                new Promise((resolve, reject) => {\r\n                    this._loadMTL(\r\n                        fileToLoad,\r\n                        rootUrl,\r\n                        (dataLoaded) => {\r\n                            try {\r\n                                //Create materials thanks MTLLoader function\r\n                                materialsFromMTLFile.parseMTL(scene, dataLoaded, rootUrl, this._assetContainer);\r\n                                //Look at each material loaded in the mtl file\r\n                                for (let n = 0; n < materialsFromMTLFile.materials.length; n++) {\r\n                                    //Three variables to get all meshes with the same material\r\n                                    let startIndex = 0;\r\n                                    const _indices = [];\r\n                                    let _index;\r\n\r\n                                    //The material from MTL file is used in the meshes loaded\r\n                                    //Push the indice in an array\r\n                                    //Check if the material is not used for another mesh\r\n                                    while ((_index = materialToUse.indexOf(materialsFromMTLFile.materials[n].name, startIndex)) > -1) {\r\n                                        _indices.push(_index);\r\n                                        startIndex = _index + 1;\r\n                                    }\r\n                                    //If the material is not used dispose it\r\n                                    if (_index === -1 && _indices.length === 0) {\r\n                                        //If the material is not needed, remove it\r\n                                        materialsFromMTLFile.materials[n].dispose();\r\n                                    } else {\r\n                                        for (let o = 0; o < _indices.length; o++) {\r\n                                            //Apply the material to the Mesh for each mesh with the material\r\n                                            const mesh = babylonMeshesArray[_indices[o]];\r\n                                            const material = materialsFromMTLFile.materials[n];\r\n                                            mesh.material = material;\r\n\r\n                                            if (!mesh.getTotalIndices()) {\r\n                                                // No indices, we need to turn on point cloud\r\n                                                material.pointsCloud = true;\r\n                                            }\r\n                                        }\r\n                                    }\r\n                                }\r\n                                resolve();\r\n                            } catch (e) {\r\n                                Tools.Warn(`Error processing MTL file: '${fileToLoad}'`);\r\n                                if (this._loadingOptions.materialLoadingFailsSilently) {\r\n                                    resolve();\r\n                                } else {\r\n                                    reject(e);\r\n                                }\r\n                            }\r\n                        },\r\n                        (pathOfFile: string, exception?: any) => {\r\n                            Tools.Warn(`Error downloading MTL file: '${fileToLoad}'`);\r\n                            if (this._loadingOptions.materialLoadingFailsSilently) {\r\n                                resolve();\r\n                            } else {\r\n                                reject(exception);\r\n                            }\r\n                        }\r\n                    );\r\n                })\r\n            );\r\n        }\r\n        //Return an array with all Mesh\r\n        return Promise.all(mtlPromises).then(() => {\r\n            return babylonMeshesArray;\r\n        });\r\n    }\r\n}\r\n\r\nif (SceneLoader) {\r\n    //Add this loader into the register plugin\r\n    SceneLoader.RegisterPlugin(new OBJFileLoader());\r\n}\r\n"]}