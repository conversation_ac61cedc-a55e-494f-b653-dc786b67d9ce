{"version": 3, "file": "normal.fragment.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/normal/normal.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,mEAAqD;AACrD,4EAA8D;AAC9D,uEAAyD;AACzD,2EAA6D;AAC7D,4EAA8D;AAC9D,gFAAkE;AAClE,0EAA4D;AAC5D,qEAAuD;AACvD,gEAAkD;AAClD,iEAAmD;AACnD,+DAAiD;AACjD,gFAAkE;AAElE,MAAM,IAAI,GAAG,mBAAmB,CAAC;AACjC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkEb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,iBAAiB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/helperFunctions\";\nimport \"core/Shaders/ShadersInclude/lightFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightUboDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/shadowsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragment\";\nimport \"core/Shaders/ShadersInclude/depthPrePass\";\nimport \"core/Shaders/ShadersInclude/lightFragment\";\nimport \"core/Shaders/ShadersInclude/fogFragment\";\nimport \"core/Shaders/ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"normalPixelShader\";\nconst shader = `precision highp float;\runiform vec4 vEyePosition;\runiform vec4 vDiffuseColor;\rvarying vec3 vPositionW;\r#ifdef NORMAL\nvarying vec3 vNormalW;\r#endif\n#ifdef LIGHTING\n#include<helperFunctions>\n#include<__decl__lightFragment>[0]\n#include<__decl__lightFragment>[1]\n#include<__decl__lightFragment>[2]\n#include<__decl__lightFragment>[3]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#endif\n#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;\runiform sampler2D diffuseSampler;\runiform vec2 vDiffuseInfos;\r#endif\n#include<clipPlaneFragmentDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);\rvec4 baseColor=vec4(1.,1.,1.,1.);\rvec3 diffuseColor=vDiffuseColor.rgb;\rfloat alpha=vDiffuseColor.a;\r#ifdef DIFFUSE\nbaseColor=texture2D(diffuseSampler,vDiffuseUV);\r#ifdef ALPHATEST\nif (baseColor.a<0.4)\rdiscard;\r#endif\n#include<depthPrePass>\nbaseColor.rgb*=vDiffuseInfos.y;\r#endif\n#ifdef NORMAL\nbaseColor=mix(baseColor,vec4(vNormalW,1.0),0.5);\r#endif\n#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\r#else\nvec3 normalW=vec3(1.0,1.0,1.0);\r#endif\n#ifdef LIGHTING\nvec3 diffuseBase=vec3(0.,0.,0.);\rlightingInfo info;\rfloat shadow=1.;\rfloat glossiness=0.;\r#include<lightFragment>[0]\n#include<lightFragment>[1]\n#include<lightFragment>[2]\n#include<lightFragment>[3]\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor,0.0,1.0)*baseColor.rgb;\r#else\nvec3 finalDiffuse= baseColor.rgb;\r#endif\nvec4 color=vec4(finalDiffuse,alpha);\r#include<fogFragment>\ngl_FragColor=color;\r#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const normalPixelShader = { name, shader };\n"]}