{"version": 3, "file": "MSFT_sRGBFactors.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/MSFT_sRGBFactors.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAI7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,MAAM,IAAI,GAAG,kBAAkB,CAAC;AAEhC,gBAAgB;AAChB,gEAAgE;AAChE,MAAM,OAAO,gBAAgB;IASzB,gBAAgB;IAChB,YAAY,MAAkB;QAT9B,gBAAgB;QACA,SAAI,GAAG,IAAI,CAAC;QASxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,gBAAgB;IACT,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,cAAc,CAAU,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YAC5F,IAAI,KAAK,EAAE;gBACP,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;oBAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,YAAY,+BAA+B,CAAC,CAAC;iBACnE;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;gBAE7F,MAAM,uBAAuB,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,uBAAuB,CAAC;gBAC/F,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE;oBAChC,eAAe,CAAC,WAAW,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,EAAE,uBAAuB,CAAC,CAAC;iBACxG;gBAED,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE;oBACtC,eAAe,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;iBACpH;gBAED,OAAO,OAAO,CAAC;aAClB;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\n\r\nimport type { IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\n\r\nconst NAME = \"MSFT_sRGBFactors\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class MSFT_sRGBFactors implements IGLTFLoaderExtension {\r\n    /** @internal */\r\n    public readonly name = NAME;\r\n\r\n    /** @internal */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /** @internal */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /** @internal */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtraAsync<boolean>(context, material, this.name, (extraContext, extra) => {\r\n            if (extra) {\r\n                if (!(babylonMaterial instanceof PBRMaterial)) {\r\n                    throw new Error(`${extraContext}: Material type not supported`);\r\n                }\r\n\r\n                const promise = this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial);\r\n\r\n                const useExactSrgbConversions = babylonMaterial.getScene().getEngine().useExactSrgbConversions;\r\n                if (!babylonMaterial.albedoTexture) {\r\n                    babylonMaterial.albedoColor.toLinearSpaceToRef(babylonMaterial.albedoColor, useExactSrgbConversions);\r\n                }\r\n\r\n                if (!babylonMaterial.reflectivityTexture) {\r\n                    babylonMaterial.reflectivityColor.toLinearSpaceToRef(babylonMaterial.reflectivityColor, useExactSrgbConversions);\r\n                }\r\n\r\n                return promise;\r\n            }\r\n\r\n            return null;\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new MSFT_sRGBFactors(loader));\r\n"]}