{"version": 3, "file": "KHR_animation_pointer.data.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_animation_pointer.data.ts"], "names": [], "mappings": "AAAA,yDAAyD;AAEzD,OAAO,EAAE,SAAS,EAAE,gDAAkC;AAGtD,OAAO,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAClF,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,SAAS,SAAS,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IAChF,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,QAAQ,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IAC/E,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACtC,CAAC;AAED,SAAS,QAAQ,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IAC/E,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AAClC,CAAC;AAED,SAAS,aAAa,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IACpF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AACnC,CAAC;AAED,SAAS,YAAY,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IACnF,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AACtC,CAAC;AAED,SAAS,WAAW,CAAC,OAAY,EAAE,MAAoB,EAAE,MAAc,EAAE,KAAa;IAClF,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,uBAAuB,CAAC,WAAmB;IAChD,OAAO;QACH,KAAK,EAAE;YACH,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,GAAG,WAAW,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC5G,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,GAAG,WAAW,SAAS,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SACnH;QACD,MAAM,EAAE;YACJ,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,GAAG,WAAW,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YAC7G,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,GAAG,WAAW,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;SACpH;QACD,QAAQ,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,GAAG,WAAW,OAAO,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;KAC9H,CAAC;AACN,CAAC;AAED,MAAM,2BAA4B,SAAQ,qBAAqB;IAC3D,gBAAgB;IACT,eAAe,CAAC,MAAe,EAAE,IAAY,EAAE,GAAW,EAAE,IAAW,EAAE,QAA+E;QAC3J,QAAQ,CAAC,MAAM,CAAC,cAAe,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED,MAAM,6BAA8B,SAAQ,qBAAqB;IAC7D,gBAAgB;IACT,eAAe,CAAC,MAAiB,EAAE,IAAY,EAAE,GAAW,EAAE,IAAW,EAAE,QAA+E;QAC7J,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,KAAM,EAAE;YAClC,QAAQ,CAAC,MAAM,CAAC,KAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;SAC5F;IACL,CAAC;CACJ;AAED,MAAM,0BAA2B,SAAQ,qBAAqB;IAC1D,gBAAgB;IACT,eAAe,CAClB,MAAgC,EAChC,IAAY,EACZ,GAAW,EACX,IAAW,EACX,QAA+E;QAE/E,QAAQ,CAAC,MAAM,CAAC,aAAc,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC;CACJ;AAED,MAAM,SAAS,GAAG;IACd,SAAS,EAAE;QACP,UAAU,EAAE,IAAI;QAChB,GAAG,iBAAiB;KACvB;CACJ,CAAC;AAEF,MAAM,WAAW,GAAG;IAChB,SAAS,EAAE;QACP,UAAU,EAAE,IAAI;QAChB,YAAY,EAAE;YACV,IAAI,EAAE;gBACF,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBACnG,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aACtG;YACD,IAAI,EAAE;gBACF,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBACrG,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aACpG;YACD,IAAI,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACjG,KAAK,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACrG;QACD,WAAW,EAAE;YACT,IAAI,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAChG,IAAI,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACjG,KAAK,EAAE,CAAC,IAAI,2BAA2B,CAAC,SAAS,CAAC,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACrG;KACJ;CACJ,CAAC;AAEF,MAAM,aAAa,GAAG;IAClB,SAAS,EAAE;QACP,UAAU,EAAE,IAAI;QAChB,oBAAoB,EAAE;YAClB,eAAe,EAAE;gBACb,IAAI,6BAA6B,CAAC,SAAS,CAAC,oBAAoB,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;gBACpG,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aAC/F;YACD,cAAc,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACjH,eAAe,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACnH,gBAAgB,EAAE;gBACd,UAAU,EAAE;oBACR,qBAAqB,EAAE,uBAAuB,CAAC,eAAe,CAAC;iBAClE;aACJ;SACJ;QACD,cAAc,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,oBAAoB,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACxH,aAAa,EAAE;YACX,KAAK,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;SACpH;QACD,gBAAgB,EAAE;YACd,QAAQ,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,wBAAwB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACzH,UAAU,EAAE;gBACR,qBAAqB,EAAE,uBAAuB,CAAC,gBAAgB,CAAC;aACnE;SACJ;QACD,eAAe,EAAE;YACb,UAAU,EAAE;gBACR,qBAAqB,EAAE,uBAAuB,CAAC,iBAAiB,CAAC;aACpE;SACJ;QACD,UAAU,EAAE;YACR,iBAAiB,EAAE;gBACf,GAAG,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aAClH;YACD,uBAAuB,EAAE;gBACrB,eAAe,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC7H,wBAAwB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,qBAAqB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aACzI;YACD,mBAAmB,EAAE;gBACjB,gBAAgB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,oBAAoB,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACxH,oBAAoB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aACjI;YACD,sBAAsB,EAAE;gBACpB,cAAc,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACzH,mBAAmB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,oBAAoB,EAAE,0BAA0B,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aAC3I;YACD,+BAA+B,EAAE;gBAC7B,gBAAgB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aAC/H;YACD,0BAA0B,EAAE;gBACxB,kBAAkB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,gCAAgC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aAC9I;YACD,oBAAoB,EAAE;gBAClB,gBAAgB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjI,mBAAmB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,gCAAgC,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5I,eAAe,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,6BAA6B,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aACxI;YACD,yBAAyB,EAAE;gBACvB,iBAAiB,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,uBAAuB,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjI,cAAc,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,+BAA+B,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtI,2BAA2B,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,8BAA8B,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClJ,2BAA2B,EAAE,CAAC,IAAI,6BAA6B,CAAC,SAAS,CAAC,mBAAmB,EAAE,8BAA8B,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;aACrJ;SACJ;KACJ;CACJ,CAAC;AAEF,MAAM,cAAc,GAAG;IACnB,mBAAmB,EAAE;QACjB,MAAM,EAAE;YACJ,SAAS,EAAE;gBACP,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,oBAAoB,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtG,SAAS,EAAE,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC1G,KAAK,EAAE,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClG,IAAI,EAAE;oBACF,cAAc,EAAE,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACnH,cAAc,EAAE,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;iBACjH;aACJ;SACJ;KACJ;CACJ,CAAC;AAEF,gBAAgB;AAChB,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAChC,KAAK,EAAE,SAAS;IAChB,SAAS,EAAE,aAAa;IACxB,OAAO,EAAE,WAAW;IACpB,UAAU,EAAE,cAAc;CAC7B,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n\r\nimport { Animation } from \"core/Animations/animation\";\r\nimport type { ICamera, IKHRLightsPunctual_Light, IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport { AnimationPropertyInfo, nodeAnimationData } from \"../glTFLoaderAnimation\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\n\r\nfunction getColor3(_target: any, source: Float32Array, offset: number, scale: number): Color3 {\r\n    return Color3.FromArray(source, offset).scale(scale);\r\n}\r\n\r\nfunction getAlpha(_target: any, source: Float32Array, offset: number, scale: number): number {\r\n    return source[offset + 3] * scale;\r\n}\r\n\r\nfunction getFloat(_target: any, source: Float32Array, offset: number, scale: number): number {\r\n    return source[offset] * scale;\r\n}\r\n\r\nfunction getMinusFloat(_target: any, source: Float32Array, offset: number, scale: number): number {\r\n    return -source[offset] * scale;\r\n}\r\n\r\nfunction getNextFloat(_target: any, source: Float32Array, offset: number, scale: number): number {\r\n    return source[offset + 1] * scale;\r\n}\r\n\r\nfunction getFloatBy2(_target: any, source: Float32Array, offset: number, scale: number): number {\r\n    return source[offset] * scale * 2;\r\n}\r\n\r\nfunction getTextureTransformTree(textureName: string) {\r\n    return {\r\n        scale: [\r\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.uScale`, getFloat, () => 2),\r\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.vScale`, getNextFloat, () => 2),\r\n        ],\r\n        offset: [\r\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.uOffset`, getFloat, () => 2),\r\n            new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.vOffset`, getNextFloat, () => 2),\r\n        ],\r\n        rotation: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, `${textureName}.wAng`, getMinusFloat, () => 1)],\r\n    };\r\n}\r\n\r\nclass CameraAnimationPropertyInfo extends AnimationPropertyInfo {\r\n    /** @internal */\r\n    public buildAnimations(target: ICamera, name: string, fps: number, keys: any[], callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void): void {\r\n        callback(target._babylonCamera!, this._buildAnimation(name, fps, keys));\r\n    }\r\n}\r\n\r\nclass MaterialAnimationPropertyInfo extends AnimationPropertyInfo {\r\n    /** @internal */\r\n    public buildAnimations(target: IMaterial, name: string, fps: number, keys: any[], callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void): void {\r\n        for (const fillMode in target._data!) {\r\n            callback(target._data![fillMode].babylonMaterial, this._buildAnimation(name, fps, keys));\r\n        }\r\n    }\r\n}\r\n\r\nclass LightAnimationPropertyInfo extends AnimationPropertyInfo {\r\n    /** @internal */\r\n    public buildAnimations(\r\n        target: IKHRLightsPunctual_Light,\r\n        name: string,\r\n        fps: number,\r\n        keys: any[],\r\n        callback: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): void {\r\n        callback(target._babylonLight!, this._buildAnimation(name, fps, keys));\r\n    }\r\n}\r\n\r\nconst nodesTree = {\r\n    __array__: {\r\n        __target__: true,\r\n        ...nodeAnimationData,\r\n    },\r\n};\r\n\r\nconst camerasTree = {\r\n    __array__: {\r\n        __target__: true,\r\n        orthographic: {\r\n            xmag: [\r\n                new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoLeft\", getMinusFloat, () => 1),\r\n                new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoRight\", getNextFloat, () => 1),\r\n            ],\r\n            ymag: [\r\n                new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoBottom\", getMinusFloat, () => 1),\r\n                new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"orthoTop\", getNextFloat, () => 1),\r\n            ],\r\n            zfar: [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"maxZ\", getFloat, () => 1)],\r\n            znear: [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"minZ\", getFloat, () => 1)],\r\n        },\r\n        perspective: {\r\n            yfov: [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"fov\", getFloat, () => 1)],\r\n            zfar: [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"maxZ\", getFloat, () => 1)],\r\n            znear: [new CameraAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"minZ\", getFloat, () => 1)],\r\n        },\r\n    },\r\n};\r\n\r\nconst materialsTree = {\r\n    __array__: {\r\n        __target__: true,\r\n        pbrMetallicRoughness: {\r\n            baseColorFactor: [\r\n                new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"albedoColor\", getColor3, () => 4),\r\n                new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"alpha\", getAlpha, () => 4),\r\n            ],\r\n            metallicFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"metallic\", getFloat, () => 1)],\r\n            roughnessFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"roughness\", getFloat, () => 1)],\r\n            baseColorTexture: {\r\n                extensions: {\r\n                    KHR_texture_transform: getTextureTransformTree(\"albedoTexture\"),\r\n                },\r\n            },\r\n        },\r\n        emissiveFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"emissiveColor\", getColor3, () => 3)],\r\n        normalTexture: {\r\n            scale: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"bumpTexture.level\", getFloat, () => 1)],\r\n        },\r\n        occlusionTexture: {\r\n            strength: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"ambientTextureStrength\", getFloat, () => 1)],\r\n            extensions: {\r\n                KHR_texture_transform: getTextureTransformTree(\"ambientTexture\"),\r\n            },\r\n        },\r\n        emissiveTexture: {\r\n            extensions: {\r\n                KHR_texture_transform: getTextureTransformTree(\"emissiveTexture\"),\r\n            },\r\n        },\r\n        extensions: {\r\n            KHR_materials_ior: {\r\n                ior: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"indexOfRefraction\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_clearcoat: {\r\n                clearcoatFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"clearCoat.intensity\", getFloat, () => 1)],\r\n                clearcoatRoughnessFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"clearCoat.roughness\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_sheen: {\r\n                sheenColorFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"sheen.color\", getColor3, () => 3)],\r\n                sheenRoughnessFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"sheen.roughness\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_specular: {\r\n                specularFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"metallicF0Factor\", getFloat, () => 1)],\r\n                specularColorFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"metallicReflectanceColor\", getColor3, () => 3)],\r\n            },\r\n            KHR_materials_emissive_strength: {\r\n                emissiveStrength: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"emissiveIntensity\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_transmission: {\r\n                transmissionFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.refractionIntensity\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_volume: {\r\n                attenuationColor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"subSurface.tintColor\", getColor3, () => 3)],\r\n                attenuationDistance: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.tintColorAtDistance\", getFloat, () => 1)],\r\n                thicknessFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"subSurface.maximumThickness\", getFloat, () => 1)],\r\n            },\r\n            KHR_materials_iridescence: {\r\n                iridescenceFactor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.intensity\", getFloat, () => 1)],\r\n                iridescenceIor: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.indexOfRefraction\", getFloat, () => 1)],\r\n                iridescenceThicknessMinimum: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.minimumThickness\", getFloat, () => 1)],\r\n                iridescenceThicknessMaximum: [new MaterialAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"iridescence.maximumThickness\", getFloat, () => 1)],\r\n            },\r\n        },\r\n    },\r\n};\r\n\r\nconst extensionsTree = {\r\n    KHR_lights_punctual: {\r\n        lights: {\r\n            __array__: {\r\n                __target__: true,\r\n                color: [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_COLOR3, \"diffuse\", getColor3, () => 3)],\r\n                intensity: [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"intensity\", getFloat, () => 1)],\r\n                range: [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"range\", getFloat, () => 1)],\r\n                spot: {\r\n                    innerConeAngle: [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"innerAngle\", getFloatBy2, () => 1)],\r\n                    outerConeAngle: [new LightAnimationPropertyInfo(Animation.ANIMATIONTYPE_FLOAT, \"angle\", getFloatBy2, () => 1)],\r\n                },\r\n            },\r\n        },\r\n    },\r\n};\r\n\r\n/** @internal */\r\nexport const animationPointerTree = {\r\n    nodes: nodesTree,\r\n    materials: materialsTree,\r\n    cameras: camerasTree,\r\n    extensions: extensionsTree,\r\n};\r\n"]}