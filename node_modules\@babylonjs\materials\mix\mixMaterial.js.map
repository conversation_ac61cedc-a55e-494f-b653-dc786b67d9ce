{"version": 3, "file": "mixMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/mix/mixMaterial.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,2CAA6B;AAE/H,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAK/C,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAEpD,OAAO,gBAAgB,CAAC;AACxB,OAAO,cAAc,CAAC;AACtB,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,kBAAmB,SAAQ,eAAe;IA0B5C;QACI,KAAK,EAAE,CAAC;QA1BL,YAAO,GAAG,KAAK,CAAC;QAChB,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,iBAAY,GAAG,KAAK,CAAC;QACrB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,iBAAY,GAAG,KAAK,CAAC;QACrB,WAAM,GAAG,KAAK,CAAC;QACf,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,gBAAW,GAAG,KAAK,CAAC;QACpB,gBAAW,GAAG,KAAK,CAAC;QACpB,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,YAAO,GAAG,KAAK,CAAC;QAChB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,WAAY,SAAQ,YAAY;IAkFzC,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAxBvB;;WAEG;QAGI,iBAAY,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGnC,kBAAa,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAGpC,kBAAa,GAAG,EAAE,CAAC;QAGlB,qBAAgB,GAAG,KAAK,CAAC;QAKzB,2BAAsB,GAAG,CAAC,CAAC;IAMnC,CAAC;IAEM,iBAAiB;QACpB,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;IAC5B,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,kBAAkB,EAAE,CAAC;SACtD;QAED,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBACpD,OAAO,KAAK,CAAC;aAChB;YAED,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;YAExB,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBACrC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;oBAC5D,OAAO,KAAK,CAAC;iBAChB;gBAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBAEvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;oBAC5D,OAAO,KAAK,CAAC;iBAChB;gBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;oBAC5D,OAAO,KAAK,CAAC;iBAChB;gBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;oBAC5D,OAAO,KAAK,CAAC;iBAChB;gBAED,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;wBAC9B,OAAO,KAAK,CAAC;qBAChB;oBAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;oBAEvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;wBAC5D,OAAO,KAAK,CAAC;qBAChB;oBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;wBAC5D,OAAO,KAAK,CAAC;qBAChB;oBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;wBAC5D,OAAO,KAAK,CAAC;qBAChB;oBACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE;wBAC5D,OAAO,KAAK,CAAC;qBAChB;iBACJ;aACJ;SACJ;QAED,QAAQ;QACR,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAExI,SAAS;QACT,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/I,kDAAkD;QAClD,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEtE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAEzF,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aACtC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;aACxC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,uBAAuB;YACvB,MAAM,UAAU,GAAG,KAAK,CAAC;YACzB,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,eAAe;gBACf,QAAQ;gBACR,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,eAAe;aAClB,CAAC;YACF,MAAM,QAAQ,GAAG;gBACb,gBAAgB;gBAChB,gBAAgB;gBAChB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;gBACjB,iBAAiB;aACpB,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;YAE3C,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/B,cAAc,CAAC,8BAA8B,CAAyB;gBAClE,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,IAAI,CAAC,qBAAqB;aACpD,CAAC,CAAC;YAEH,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,EAAE;aACzE,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,WAAW;YACX,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC3G,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBAEpF,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;iBACJ;aACJ;YAED,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAEnE,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBACxE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;qBAC7G;iBACJ;aACJ;YAED,aAAa;YACb,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEnC,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QAE/F,IAAI,OAAO,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAC1F;QAED,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC9C,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACnG;QAED,OAAO;QACP,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,EAAE;YAC3E,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,cAAc;QACjB,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACnC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,iBAAiB;QACpB,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,YAAY;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC1C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC9C;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,UAAU,CAAC,OAAoB;QAClC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO,IAAI,CAAC;SACf;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE;YACnC,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,OAAO,CAAC,kBAA4B;QACvC,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC/B;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACzF,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,qBAAqB,CAAC;QACvD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,UAAU;IACH,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACxG,CAAC;CACJ;AAxhBG;IADC,kBAAkB,CAAC,aAAa,CAAC;iDACA;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;gDACrB;AAGhC;IADC,kBAAkB,CAAC,aAAa,CAAC;iDACA;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;gDACrB;AAOhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAGhC;IADC,kBAAkB,CAAC,iBAAiB,CAAC;qDACJ;AAElC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDACrB;AAOhC;IADC,iBAAiB,EAAE;iDACsB;AAG1C;IADC,iBAAiB,EAAE;kDACuB;AAG3C;IADC,SAAS,EAAE;kDACc;AAG1B;IADC,SAAS,CAAC,iBAAiB,CAAC;qDACI;AAEjC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;oDACnB;AAGhC;IADC,SAAS,CAAC,uBAAuB,CAAC;2DACA;AAEnC;IADC,gBAAgB,CAAC,gCAAgC,CAAC;0DACd;AAgdzC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { serializeAsTexture, serialize, expandToProperty, serializeAsColor3, SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { IEffectCreationOptions } from \"core/Materials/effect\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { MaterialFlags } from \"core/Materials/materialFlags\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nimport \"./mix.fragment\";\r\nimport \"./mix.vertex\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass MixMaterialDefines extends MaterialDefines {\r\n    public DIFFUSE = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public SPECULARTERM = false;\r\n    public NORMAL = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public MIXMAP2 = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class MixMaterial extends PushMaterial {\r\n    /**\r\n     * Mix textures\r\n     */\r\n\r\n    @serializeAsTexture(\"mixTexture1\")\r\n    private _mixTexture1: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public mixTexture1: BaseTexture;\r\n\r\n    @serializeAsTexture(\"mixTexture2\")\r\n    private _mixTexture2: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public mixTexture2: BaseTexture;\r\n\r\n    /**\r\n     * Diffuse textures\r\n     */\r\n\r\n    @serializeAsTexture(\"diffuseTexture1\")\r\n    private _diffuseTexture1: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture1: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture2\")\r\n    private _diffuseTexture2: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture2: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture3\")\r\n    private _diffuseTexture3: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture3: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture4\")\r\n    private _diffuseTexture4: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture4: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture1\")\r\n    private _diffuseTexture5: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture5: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture2\")\r\n    private _diffuseTexture6: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture6: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture3\")\r\n    private _diffuseTexture7: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture7: Texture;\r\n\r\n    @serializeAsTexture(\"diffuseTexture4\")\r\n    private _diffuseTexture8: Texture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public diffuseTexture8: Texture;\r\n\r\n    /**\r\n     * Uniforms\r\n     */\r\n\r\n    @serializeAsColor3()\r\n    public diffuseColor = new Color3(1, 1, 1);\r\n\r\n    @serializeAsColor3()\r\n    public specularColor = new Color3(0, 0, 0);\r\n\r\n    @serialize()\r\n    public specularPower = 64;\r\n\r\n    @serialize(\"disableLighting\")\r\n    private _disableLighting = false;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting: boolean;\r\n\r\n    @serialize(\"maxSimultaneousLights\")\r\n    private _maxSimultaneousLights = 4;\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights: number;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return this.alpha < 1.0;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new MixMaterialDefines();\r\n        }\r\n\r\n        const defines = <MixMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (!this._mixTexture1 || !this._mixTexture1.isReady()) {\r\n                return false;\r\n            }\r\n\r\n            defines._needUVs = true;\r\n\r\n            if (MaterialFlags.DiffuseTextureEnabled) {\r\n                if (!this._diffuseTexture1 || !this._diffuseTexture1.isReady()) {\r\n                    return false;\r\n                }\r\n\r\n                defines.DIFFUSE = true;\r\n\r\n                if (!this._diffuseTexture2 || !this._diffuseTexture2.isReady()) {\r\n                    return false;\r\n                }\r\n                if (!this._diffuseTexture3 || !this._diffuseTexture3.isReady()) {\r\n                    return false;\r\n                }\r\n                if (!this._diffuseTexture4 || !this._diffuseTexture4.isReady()) {\r\n                    return false;\r\n                }\r\n\r\n                if (this._mixTexture2) {\r\n                    if (!this._mixTexture2.isReady()) {\r\n                        return false;\r\n                    }\r\n\r\n                    defines.MIXMAP2 = true;\r\n\r\n                    if (!this._diffuseTexture5 || !this._diffuseTexture5.isReady()) {\r\n                        return false;\r\n                    }\r\n                    if (!this._diffuseTexture6 || !this._diffuseTexture6.isReady()) {\r\n                        return false;\r\n                    }\r\n                    if (!this._diffuseTexture7 || !this._diffuseTexture7.isReady()) {\r\n                        return false;\r\n                    }\r\n                    if (!this._diffuseTexture8 || !this._diffuseTexture8.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Misc.\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, this.pointsCloud, this.fogEnabled, this._shouldTurnAlphaTestOn(mesh), defines);\r\n\r\n        // Lights\r\n        defines._needNormals = MaterialHelper.PrepareDefinesForLights(scene, mesh, defines, false, this._maxSimultaneousLights, this._disableLighting);\r\n\r\n        // Values that need to be evaluated on every frame\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, true, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            MaterialHelper.HandleFallbacksForShadows(defines, fallbacks, this.maxSimultaneousLights);\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n\r\n            if (defines.UV2) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            // Legacy browser patch\r\n            const shaderName = \"mix\";\r\n            const join = defines.toString();\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vLightsType\",\r\n                \"vDiffuseColor\",\r\n                \"vSpecularColor\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"vTextureInfos\",\r\n                \"mBones\",\r\n                \"textureMatrix\",\r\n                \"diffuse1Infos\",\r\n                \"diffuse2Infos\",\r\n                \"diffuse3Infos\",\r\n                \"diffuse4Infos\",\r\n                \"diffuse5Infos\",\r\n                \"diffuse6Infos\",\r\n                \"diffuse7Infos\",\r\n                \"diffuse8Infos\",\r\n            ];\r\n            const samplers = [\r\n                \"mixMap1Sampler\",\r\n                \"mixMap2Sampler\",\r\n                \"diffuse1Sampler\",\r\n                \"diffuse2Sampler\",\r\n                \"diffuse3Sampler\",\r\n                \"diffuse4Sampler\",\r\n                \"diffuse5Sampler\",\r\n                \"diffuse6Sampler\",\r\n                \"diffuse7Sampler\",\r\n                \"diffuse8Sampler\",\r\n            ];\r\n\r\n            const uniformBuffers = new Array<string>();\r\n\r\n            addClipPlaneUniforms(uniforms);\r\n            MaterialHelper.PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: this.maxSimultaneousLights,\r\n            });\r\n\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: { maxSimultaneousLights: this.maxSimultaneousLights },\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <MixMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, this._activeEffect);\r\n\r\n        if (this._mustRebind(scene, effect)) {\r\n            // Textures\r\n            if (this._mixTexture1) {\r\n                this._activeEffect.setTexture(\"mixMap1Sampler\", this._mixTexture1);\r\n                this._activeEffect.setFloat2(\"vTextureInfos\", this._mixTexture1.coordinatesIndex, this._mixTexture1.level);\r\n                this._activeEffect.setMatrix(\"textureMatrix\", this._mixTexture1.getTextureMatrix());\r\n\r\n                if (MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (this._diffuseTexture1) {\r\n                        this._activeEffect.setTexture(\"diffuse1Sampler\", this._diffuseTexture1);\r\n                        this._activeEffect.setFloat2(\"diffuse1Infos\", this._diffuseTexture1.uScale, this._diffuseTexture1.vScale);\r\n                    }\r\n                    if (this._diffuseTexture2) {\r\n                        this._activeEffect.setTexture(\"diffuse2Sampler\", this._diffuseTexture2);\r\n                        this._activeEffect.setFloat2(\"diffuse2Infos\", this._diffuseTexture2.uScale, this._diffuseTexture2.vScale);\r\n                    }\r\n                    if (this._diffuseTexture3) {\r\n                        this._activeEffect.setTexture(\"diffuse3Sampler\", this._diffuseTexture3);\r\n                        this._activeEffect.setFloat2(\"diffuse3Infos\", this._diffuseTexture3.uScale, this._diffuseTexture3.vScale);\r\n                    }\r\n                    if (this._diffuseTexture4) {\r\n                        this._activeEffect.setTexture(\"diffuse4Sampler\", this._diffuseTexture4);\r\n                        this._activeEffect.setFloat2(\"diffuse4Infos\", this._diffuseTexture4.uScale, this._diffuseTexture4.vScale);\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this._mixTexture2) {\r\n                this._activeEffect.setTexture(\"mixMap2Sampler\", this._mixTexture2);\r\n\r\n                if (MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (this._diffuseTexture5) {\r\n                        this._activeEffect.setTexture(\"diffuse5Sampler\", this._diffuseTexture5);\r\n                        this._activeEffect.setFloat2(\"diffuse5Infos\", this._diffuseTexture5.uScale, this._diffuseTexture5.vScale);\r\n                    }\r\n                    if (this._diffuseTexture6) {\r\n                        this._activeEffect.setTexture(\"diffuse6Sampler\", this._diffuseTexture6);\r\n                        this._activeEffect.setFloat2(\"diffuse6Infos\", this._diffuseTexture6.uScale, this._diffuseTexture6.vScale);\r\n                    }\r\n                    if (this._diffuseTexture7) {\r\n                        this._activeEffect.setTexture(\"diffuse7Sampler\", this._diffuseTexture7);\r\n                        this._activeEffect.setFloat2(\"diffuse7Infos\", this._diffuseTexture7.uScale, this._diffuseTexture7.vScale);\r\n                    }\r\n                    if (this._diffuseTexture8) {\r\n                        this._activeEffect.setTexture(\"diffuse8Sampler\", this._diffuseTexture8);\r\n                        this._activeEffect.setFloat2(\"diffuse8Infos\", this._diffuseTexture8.uScale, this._diffuseTexture8.vScale);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Clip plane\r\n            bindClipPlane(effect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        this._activeEffect.setColor4(\"vDiffuseColor\", this.diffuseColor, this.alpha * mesh.visibility);\r\n\r\n        if (defines.SPECULARTERM) {\r\n            this._activeEffect.setColor4(\"vSpecularColor\", this.specularColor, this.specularPower);\r\n        }\r\n\r\n        if (scene.lightsEnabled && !this.disableLighting) {\r\n            MaterialHelper.BindLights(scene, mesh, this._activeEffect, defines, this.maxSimultaneousLights);\r\n        }\r\n\r\n        // View\r\n        if (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public getAnimatables(): IAnimatable[] {\r\n        const results = [];\r\n\r\n        if (this._mixTexture1 && this._mixTexture1.animations && this._mixTexture1.animations.length > 0) {\r\n            results.push(this._mixTexture1);\r\n        }\r\n\r\n        if (this._mixTexture2 && this._mixTexture2.animations && this._mixTexture2.animations.length > 0) {\r\n            results.push(this._mixTexture2);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    public getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        // Mix map 1\r\n        if (this._mixTexture1) {\r\n            activeTextures.push(this._mixTexture1);\r\n        }\r\n\r\n        if (this._diffuseTexture1) {\r\n            activeTextures.push(this._diffuseTexture1);\r\n        }\r\n\r\n        if (this._diffuseTexture2) {\r\n            activeTextures.push(this._diffuseTexture2);\r\n        }\r\n\r\n        if (this._diffuseTexture3) {\r\n            activeTextures.push(this._diffuseTexture3);\r\n        }\r\n\r\n        if (this._diffuseTexture4) {\r\n            activeTextures.push(this._diffuseTexture4);\r\n        }\r\n\r\n        // Mix map 2\r\n        if (this._mixTexture2) {\r\n            activeTextures.push(this._mixTexture2);\r\n        }\r\n\r\n        if (this._diffuseTexture5) {\r\n            activeTextures.push(this._diffuseTexture5);\r\n        }\r\n\r\n        if (this._diffuseTexture6) {\r\n            activeTextures.push(this._diffuseTexture6);\r\n        }\r\n\r\n        if (this._diffuseTexture7) {\r\n            activeTextures.push(this._diffuseTexture7);\r\n        }\r\n\r\n        if (this._diffuseTexture8) {\r\n            activeTextures.push(this._diffuseTexture8);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    public hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        // Mix map 1\r\n        if (this._mixTexture1 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture1 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture2 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture3 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture4 === texture) {\r\n            return true;\r\n        }\r\n\r\n        // Mix map 2\r\n        if (this._mixTexture2 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture5 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture6 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture7 === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture8 === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        if (this._mixTexture1) {\r\n            this._mixTexture1.dispose();\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public clone(name: string): MixMaterial {\r\n        return SerializationHelper.Clone(() => new MixMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.MixMaterial\";\r\n        return serializationObject;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"MixMaterial\";\r\n    }\r\n\r\n    // Statics\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): MixMaterial {\r\n        return SerializationHelper.Parse(() => new MixMaterial(source.name, scene), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.MixMaterial\", MixMaterial);\r\n"]}