{"version": 3, "file": "KHR_materials_specular.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_specular.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAG/C,MAAM,IAAI,GAAG,wBAAwB,CAAC;AAEtC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,sBAAsB;IAkB/B;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAwB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACtH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YAC/F,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,4BAA4B,CAAC,OAAe,EAAE,UAAiC,EAAE,eAAyB;QAC9G,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,EAAE;YACzC,eAAe,CAAC,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAAC;SAChE;QAED,IAAI,UAAU,CAAC,mBAAmB,KAAK,SAAS,EAAE;YAC9C,eAAe,CAAC,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;SAC/F;QAED,IAAI,UAAU,CAAC,eAAe,EAAE;YAC3B,UAAU,CAAC,eAAgC,CAAC,YAAY,GAAG,IAAI,CAAC;YACjE,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,kBAAkB,EAAE,UAAU,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,EAAE;gBACpG,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,yBAAyB,CAAC;gBAChE,eAAe,CAAC,0BAA0B,GAAG,OAAO,CAAC;gBACrD,eAAe,CAAC,6CAA6C,GAAG,IAAI,CAAC;YACzE,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,oBAAoB,EAAE;YACjC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,uBAAuB,EAAE,UAAU,CAAC,oBAAoB,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC9G,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,sBAAsB,CAAC;gBAC7D,eAAe,CAAC,kBAAkB,GAAG,OAAO,CAAC;YACjD,CAAC,CAAC,CACL,CAAC;SACL;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { IKHRMaterialsSpecular } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_specular\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_specular/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_specular implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 190;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsSpecular>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadSpecularPropertiesAsync(extensionContext, extension, babylonMaterial));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadSpecularPropertiesAsync(context: string, properties: IKHRMaterialsSpecular, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        if (properties.specularFactor !== undefined) {\r\n            babylonMaterial.metallicF0Factor = properties.specularFactor;\r\n        }\r\n\r\n        if (properties.specularColorFactor !== undefined) {\r\n            babylonMaterial.metallicReflectanceColor = Color3.FromArray(properties.specularColorFactor);\r\n        }\r\n\r\n        if (properties.specularTexture) {\r\n            (properties.specularTexture as ITextureInfo).nonColorData = true;\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/specularTexture`, properties.specularTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Specular F0 Strength)`;\r\n                    babylonMaterial.metallicReflectanceTexture = texture;\r\n                    babylonMaterial.useOnlyMetallicFromMetallicReflectanceTexture = true;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.specularColorTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/specularColorTexture`, properties.specularColorTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Specular F0 Color)`;\r\n                    babylonMaterial.reflectanceTexture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_specular(loader));\r\n"]}