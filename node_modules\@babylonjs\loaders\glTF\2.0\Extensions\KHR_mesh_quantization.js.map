{"version": 3, "file": "KHR_mesh_quantization.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_mesh_quantization.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,MAAM,IAAI,GAAG,uBAAuB,CAAC;AAErC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,qBAAqB;IAW9B;;OAEG;IACH,YAAY,MAAkB;QAb9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAWxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,gBAAgB;IACT,OAAO,KAAI,CAAC;CACtB;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\n\r\nconst NAME = \"KHR_mesh_quantization\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_mesh_quantization/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_mesh_quantization implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this.enabled = loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {}\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_mesh_quantization(loader));\r\n"]}