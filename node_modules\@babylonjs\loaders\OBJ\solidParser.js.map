{"version": 3, "file": "solidParser.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/OBJ/solidParser.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAEnD,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,4CAA8B;AACvD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,6CAA+B;AAE1D,OAAO,EAAE,QAAQ,EAAE,2CAA6B;AAChD,OAAO,EAAE,IAAI,EAAE,uCAAyB;AACxC,OAAO,EAAE,UAAU,EAAE,kDAAoC;AAgBzD;;GAEG;AACH,MAAM,OAAO,WAAW;IA2DpB;;;;;OAKG;IACH,YAAmB,aAAuB,EAAE,kBAA+B,EAAE,cAAiC;QAjCtG,eAAU,GAAmB,EAAE,CAAC,CAAC,sCAAsC;QACvE,aAAQ,GAAmB,EAAE,CAAC,CAAC,wBAAwB;QACvD,SAAI,GAAmB,EAAE,CAAC,CAAC,yBAAyB;QACpD,YAAO,GAAkB,EAAE,CAAC;QAC5B,mBAAc,GAAsB,EAAE,CAAC,CAAC,oCAAoC;QAE5E,uBAAkB,GAAkB,EAAE,CAAC,CAAC,oCAAoC;QAC5E,+BAA0B,GAAmB,EAAE,CAAC,CAAC,iCAAiC;QAClF,0BAAqB,GAAmB,EAAE,CAAC,CAAC,uDAAuD;QACnG,6BAAwB,GAAkB,EAAE,CAAC,CAAC,wDAAwD;QACtG,8BAAyB,GAAmB,EAAE,CAAC,CAAC,2DAA2D;QAC3G,kBAAa,GAA6E,EAAE,CAAC,CAAC,sEAAsE;QACpK,0BAAqB,GAAG,CAAC,CAAC;QAC1B,eAAU,GAAY,KAAK,CAAC,CAAC,gCAAgC;QAC7D,kCAA6B,GAAkB,EAAE,CAAC,CAAC,mDAAmD;QACtG,+BAA0B,GAAkB,EAAE,CAAC,CAAC,kDAAkD;QAClG,gCAA2B,GAAkB,EAAE,CAAC,CAAC,mDAAmD;QACpG,2BAAsB,GAAkB,EAAE,CAAC,CAAC,mDAAmD;QAC/F,eAAU,GAAkB,EAAE,CAAC,CAAC,iDAAiD;QACjF,yBAAoB,GAAW,EAAE,CAAC,CAAC,kCAAkC;QACrE,iBAAY,GAAW,EAAE,CAAC,CAAC,kCAAkC;QAC7D,eAAU,GAAW,CAAC,CAAC,CAAC,4CAA4C;QACpE,qBAAgB,GAAY,IAAI,CAAC;QACjC,eAAU,GAAG,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAW9C,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAED;;;;;;;;OAQG;IACK,UAAU,CAAC,GAA0D,EAAE,GAAkB;QAC7F,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;SAC1C;QACD,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClD,CAAC;IAEO,YAAY,CAAC,GAA6E,EAAE,GAAkB;QAClH,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;SAClD;QACD,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhD,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;YAC5C,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC/B;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,QAAQ,CACZ,qBAA6B,EAC7B,gBAAwB,EACxB,mBAA2B,EAC3B,qBAA8B,EAC9B,oBAA6B,EAC7B,oBAA6B,EAC7B,qBAA8B;QAE9B,0DAA0D;QAC1D,IAAI,MAAc,CAAC;QACnB,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;YACrC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,CAAC;SAClH;aAAM;YACH,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC,CAAC;SAC9F;QAED,kBAAkB;QAClB,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;YACf,oBAAoB;YACpB,6FAA6F;YAC7F,oCAAoC;YACpC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;YACrE,0CAA0C;YAC1C,kCAAkC;YAClC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5D,0BAA0B;YAC1B,gCAAgC;YAChC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACtD,8BAA8B;YAC9B,kCAAkC;YAClC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE1D,IAAI,qBAAqB,KAAK,SAAS,EAAE;gBACrC,6BAA6B;gBAC7B,2CAA2C;gBAC3C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;aAC7D;YAED,sCAAsC;YACtC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC5E,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACjF,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACrC,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACvE;SACJ;aAAM;YACH,0BAA0B;YAC1B,6CAA6C;YAC7C,iFAAiF;YACjF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACK,WAAW;QACf,iCAAiC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7D,gEAAgE;YAChE,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1J,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrJ,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iDAAiD;YACrJ,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;gBACzC,mEAAmE;gBACnE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAChC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,EAClC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,EAClC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,EAClC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CACrC,CAAC;aACL;SACJ;QACD,uCAAuC;QACvC,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,aAAa,CAAC,KAAoB,EAAE,CAAS;QACjD,oCAAoC;QACpC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE;YAC/D,8DAA8D;YAC9D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;SAC1E;QAED,qCAAqC;QACrC,mDAAmD;QACnD,+DAA+D;QAC/D,2EAA2E;QAC3E,qEAAqE;QACrE,6FAA6F;IACjG,CAAC;IAED;;;;;OAKG;IACK,kCAAkC,CAAC,IAAmB,EAAE,CAAS;QACrE,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5B,0CAA0C;QAC1C,uDAAuD;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,sBAAsB;YACtB,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAE/D,IAAI,CAAC,QAAQ,CACT,qBAAqB,EACrB,CAAC,EACD,CAAC,EAAE,oDAAoD;YACvD,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,uBAAuB;YAC/D,OAAO,CAAC,IAAI,EAAE,EACd,OAAO,CAAC,EAAE,EAAE,EAAE,yBAAyB;YACvC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5F,CAAC;SACL;QACD,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACK,kCAAkC,CAAC,IAAmB,EAAE,CAAS;QACrE,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,qBAAqB;YACrB,4CAA4C;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa;YAC1D,qBAAqB;YACrB,MAAM,qBAAqB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,eAAe;YACf,MAAM,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEhD,IAAI,CAAC,QAAQ,CACT,qBAAqB,EACrB,gBAAgB,EAChB,CAAC,EAAE,2BAA2B;YAC9B,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,iCAAiC;YACzE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC3B,OAAO,CAAC,EAAE,EAAE,EAAE,2BAA2B;YACzC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5F,CAAC;SACL;QAED,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACK,kCAAkC,CAAC,IAAmB,EAAE,CAAS;QACrE,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,uBAAuB;YACvB,sDAAsD;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;YAC/D,sBAAsB;YACtB,MAAM,qBAAqB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,gBAAgB;YAChB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAChD,oBAAoB;YACpB,MAAM,mBAAmB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEnD,IAAI,CAAC,QAAQ,CACT,qBAAqB,EACrB,gBAAgB,EAChB,mBAAmB,EACnB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EACtC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,mCAAmC;aACzE,CAAC;SACL;QACD,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACK,kCAAkC,CAAC,IAAmB,EAAE,CAAS;QACrE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,sBAAsB;YACtB,iDAAiD;YACjD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa;YAC3D,gCAAgC;YAChC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrD,MAAM,mBAAmB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAEnD,IAAI,CAAC,QAAQ,CACT,qBAAqB,EACrB,CAAC,EAAE,sBAAsB;YACzB,mBAAmB,EACnB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,yBAAyB;YACjE,OAAO,CAAC,IAAI,EAAE,EACd,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAClC,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5F,CAAC;SACL;QACD,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACK,kCAAkC,CAAC,IAAmB,EAAE,CAAS;QACrE,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,0BAA0B;YAC1B,sDAAsD;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,qBAAqB;YAClE,sBAAsB;YACtB,MAAM,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,gBAAgB;YAChB,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,oBAAoB;YACpB,MAAM,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtE,IAAI,CAAC,QAAQ,CACT,qBAAqB,EACrB,gBAAgB,EAChB,mBAAmB,EACnB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EACtC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC3B,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,mCAAmC;YACvE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,SAAS,CAC5F,CAAC;SACL;QACD,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEO,mBAAmB;QACvB,kEAAkE;QAClE,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,6DAA6D;YAC7D,oEAAoE;YACpE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAExE,sCAAsC;YACtC,IAAI,CAAC,WAAW,EAAE,CAAC;YAEnB,8DAA8D;YAC9D,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,kCAAkC;YAClC,+FAA+F;YAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAC5D,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;YACzE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;YACrE,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;YAE5D,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;gBACzC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;aACtE;YAED,mCAAmC;YACnC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,6BAA6B,CAAC,MAAM,GAAG,CAAC,CAAC;YAC9C,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5C,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;SAC1C;IACL,CAAC;IAEO,gBAAgB,CAAC,IAAkB;QACvC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAClE,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC9D,MAAM,WAAW,GAAgC,EAAE,CAAC;QAEpD,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;YACxB,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3C,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAElC,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,GAAG,EAAE;gBACN,GAAG,GAAG,EAAE,CAAC;gBACT,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;aAC1B;YACD,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACf;QAED,MAAM,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC7B,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;YAC3B,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;gBAChB,SAAS;aACZ;YAED,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aACnD;YAED,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9F,MAAM,CAAC,SAAS,EAAE,CAAC;YAEnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACjC,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;gBACjC,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;aACpC;SACJ;QACD,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,WAAgB,EAAE,IAAY,EAAE,KAAY,EAAE,cAAwC,EAAE,iBAA+C;;QAChJ,4BAA4B;QAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YACnD,IAAI,MAAM,CAAC;YAEX,qBAAqB;YACrB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7C,SAAS;gBAET,8DAA8D;aACjE;iBAAM,IAAI,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC7C,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAC,CAAC,0DAA0D;gBAE1F,6CAA6C;gBAC7C,6BAA6B;gBAC7B,6CAA6C;gBAC7C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEvG,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;oBACzC,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;wBACpB,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEhC,IAAI,CAAC,OAAO,CAAC,IAAI,CACb,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CACxJ,CAAC;qBACL;yBAAM;wBACH,gFAAgF;wBAChF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;qBACtC;iBACJ;aACJ;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBACjE,2CAA2C;gBAC3C,iBAAiB;gBACjB,0CAA0C;gBAC1C,uCAAuC;gBACvC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACxG;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAC7D,wCAAwC;gBACxC,iBAAiB;gBACjB,mCAAmC;gBACnC,mCAAmC;gBACnC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEhJ,4BAA4B;gBAC5B,oDAAoD;aACvD;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAChE,kBAAkB;gBAClB,iDAAiD;gBAEjD,4BAA4B;gBAC5B,IAAI,CAAC,kCAAkC,CACnC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,8BAA8B;gBAC3D,CAAC,CACJ,CAAC;aACL;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAChE,kBAAkB;gBAClB,2CAA2C;gBAE3C,4BAA4B;gBAC5B,IAAI,CAAC,kCAAkC,CACnC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,2BAA2B;gBACxD,CAAC,CACJ,CAAC;aACL;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAChE,kBAAkB;gBAClB,mEAAmE;gBAEnE,4BAA4B;gBAC5B,IAAI,CAAC,kCAAkC,CACnC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,uCAAuC;gBACpE,CAAC,CACJ,CAAC;aACL;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAChE,kBAAkB;gBAClB,qCAAqC;gBAErC,4BAA4B;gBAC5B,IAAI,CAAC,kCAAkC,CACnC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,wBAAwB;gBACrD,CAAC,CACJ,CAAC;aACL;iBAAM,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;gBAChE,iBAAiB;gBACjB,yBAAyB;gBAEzB,4BAA4B;gBAC5B,IAAI,CAAC,kCAAkC,CACnC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB;gBAC/C,CAAC,CACJ,CAAC;gBAEF,6BAA6B;gBAC7B,mGAAmG;aACtG;iBAAM,IAAI,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC1F,4DAA4D;gBAC5D,yBAAyB;gBACzB,MAAM,OAAO,GAAe;oBACxB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;oBAC9B,OAAO,EAAE,SAAS;oBAClB,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,SAAS;oBAClB,GAAG,EAAE,SAAS;oBACd,MAAM,EAAE,SAAS;oBACjB,YAAY,EAAE,IAAI,CAAC,oBAAoB;iBAC1C,CAAC;gBACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAE3B,+CAA+C;gBAC/C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAElC,iFAAiF;gBACjF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;gBACpB,iCAAiC;aACpC;iBAAM,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChD,8BAA8B;gBAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAErD,0CAA0C;gBAE1C,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAC5C,oCAAoC;oBACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,mBAAmB;oBACnB,MAAM,OAAO;oBACT,sCAAsC;oBACtC;wBACI,IAAI,EAAE,CAAC,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACxE,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE,SAAS;wBACpB,OAAO,EAAE,SAAS;wBAClB,GAAG,EAAE,SAAS;wBACd,MAAM,EAAE,SAAS;wBACjB,YAAY,EAAE,IAAI,CAAC,oBAAoB;qBAC1C,CAAC;oBACN,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,+BAA+B;oBAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;iBAC1B;gBACD,0DAA0D;gBAE1D,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBAC1C,kEAAkE;oBAClE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC;oBAC7F,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;iBACjC;gBACD,mCAAmC;aACtC;iBAAM,IAAI,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACrD,2BAA2B;gBAC3B,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBAE5C,kBAAkB;aACrB;iBAAM,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChD,oCAAoC;gBACpC,wDAAwD;gBACxD,uCAAuC;aAC1C;iBAAM;gBACH,iCAAiC;gBACjC,OAAO,CAAC,GAAG,CAAC,iCAAiC,GAAG,IAAI,CAAC,CAAC;aACzD;SACJ;QAED,yEAAyE;QACzE,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,iCAAiC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAExE,wDAAwD;YACxD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,oBAAoB;YACpB,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,WAAW;YACX,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,6BAA6B,CAAC;YACjE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC;YAC7D,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAEpD,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;gBACzC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC;aAC9D;SACJ;QAED,kEAAkE;QAClE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,WAAW,GAA+B,IAAI,CAAC;YACnD,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;gBAChC,yBAAyB;gBACzB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAClC,2BAA2B;gBAC3B,IAAI,CAAC,WAAW,EAAE,CAAC;aACtB;iBAAM;gBACH,mFAAmF;gBACnF,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC/B,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;iBAChE;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;wBAChC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;qBACvE;iBACJ;gBAED,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAClB,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;wBACxB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;qBAChD;iBACJ;gBAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;oBACrB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;wBAC9B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC5E;iBACJ;gBAED,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBAC5B,wCAAwC;oBACxC,WAAW,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;oBAE/D,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;oBAE/B,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,IAAI,CAAC;oBAE7C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;wBACvB,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;wBACnC,WAAW,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;qBAC9C;iBACJ;aACJ;YAED,uBAAuB;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;gBACrB,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBACzB,OAAO,EAAE,IAAI,CAAC,kBAAkB;gBAChC,SAAS,EAAE,IAAI,CAAC,6BAA6B;gBAC7C,MAAM,EAAE,IAAI,CAAC,0BAA0B;gBACvC,OAAO,EAAE,IAAI,CAAC,2BAA2B;gBACzC,GAAG,EAAE,IAAI,CAAC,sBAAsB;gBAChC,YAAY,EAAE,IAAI,CAAC,oBAAoB;gBACvC,cAAc,EAAE,WAAW;aAC9B,CAAC,CAAC;SACN;QAED,wBAAwB;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,mCAAmC;YACnC,IAAI,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;gBAC5C,IAAI,WAAW,YAAY,KAAK,EAAE;oBAC9B,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBACzD,SAAS;qBACZ;iBACJ;qBAAM;oBACH,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,EAAE;wBAC7C,SAAS;qBACZ;iBACJ;aACJ;YAED,sBAAsB;YACtB,8CAA8C;YAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YAC3C,6CAA6C;YAE7C,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,cAAc,CAAC;YAChD,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YACjE,WAAW,CAAC,gBAAgB,GAAG,cAAc,CAAC;YAC9C,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;YAErC,2CAA2C;YAC3C,mDAAmD;YACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YAE9D,IAAI,CAAA,MAAA,IAAI,CAAC,YAAY,CAAC,SAAS,0CAAE,MAAM,MAAK,CAAC,EAAE;gBAC3C,6BAA6B;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3C,SAAS;aACZ;YAED,MAAM,UAAU,GAAe,IAAI,UAAU,EAAE,CAAC,CAAC,8BAA8B;YAC/E,kCAAkC;YAClC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,GAAiB,CAAC;YACrD,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAuB,CAAC;YAC/D,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAuB,CAAC;YACjE,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE;gBACrC,MAAM,OAAO,GAAkB,IAAI,KAAK,EAAU,CAAC;gBACnD,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC3F,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;aAChC;iBAAM;gBACH,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAqB,CAAC;aAChE;YACD,IAAI,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE;gBACzC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,MAAoB,CAAC;aAC9D;YACD,wDAAwD;YACxD,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACpC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;gBAC9B,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC/B;YACD,IAAI,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE;gBACtC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;aACtC;YAED,6BAA6B;YAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;gBAClC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;aAC3D;SACJ;IACL,CAAC;;AAxxBD,aAAa;AACb,wBAAwB;AACV,4BAAgB,GAAG,IAAI,CAAC;AACtC,uBAAuB;AACT,2BAAe,GAAG,IAAI,CAAC;AACrC,8BAA8B;AAChB,iCAAqB,GAAG,UAAU,CAAC;AACjD,gCAAgC;AAClB,4BAAgB,GAAG,UAAU,CAAC;AAC5C,wBAAwB;AACV,4BAAgB,GAAG,KAAK,CAAC;AAEvC,WAAW;AACX,sCAAsC;AACxB,yBAAa,GAAG,6BAA6B,CAAC;AAC5D,sCAAsC;AACxB,yBAAa,GAAG,iEAAiE,CAAC;AAChG,sCAAsC;AACxB,qBAAS,GAAG,6CAA6C,CAAC;AACxE,2EAA2E;AAC7D,wBAAY,GAAG,4BAA4B,CAAC;AAC1D,wFAAwF;AAC1E,wBAAY,GAAG,wCAAwC,CAAC;AACtE,4GAA4G;AAC9F,wBAAY,GAAG,kDAAkD,CAAC;AAChF,mGAAmG;AACrF,wBAAY,GAAG,0CAA0C,CAAC;AACxE,qHAAqH;AACvG,wBAAY,GAAG,qDAAqD,CAAC", "sourcesContent": ["import type { AssetContainer } from \"core/assetContainer\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport { Color3, Color4 } from \"core/Maths/math.color\";\r\nimport { Vector2, Vector3 } from \"core/Maths/math.vector\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { Geometry } from \"core/Meshes/geometry\";\r\nimport { Mesh } from \"core/Meshes/mesh\";\r\nimport { VertexData } from \"core/Meshes/mesh.vertexData\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { FloatArray, IndicesArray, Nullable } from \"core/types\";\r\nimport type { OBJLoadingOptions } from \"./objLoadingOptions\";\r\n\r\ntype MeshObject = {\r\n    name: string;\r\n    indices?: Array<number>;\r\n    positions?: Array<number>;\r\n    normals?: Array<number>;\r\n    colors?: Array<number>;\r\n    uvs?: Array<number>;\r\n    materialName: string;\r\n    directMaterial?: Nullable<Material>;\r\n};\r\n\r\n/**\r\n * Class used to load mesh data from OBJ content\r\n */\r\nexport class SolidParser {\r\n    // Descriptor\r\n    /** Object descriptor */\r\n    public static ObjectDescriptor = /^o/;\r\n    /** Group descriptor */\r\n    public static GroupDescriptor = /^g/;\r\n    /** Material lib descriptor */\r\n    public static MtlLibGroupDescriptor = /^mtllib /;\r\n    /** Use a material descriptor */\r\n    public static UseMtlDescriptor = /^usemtl /;\r\n    /** Smooth descriptor */\r\n    public static SmoothDescriptor = /^s /;\r\n\r\n    // Patterns\r\n    /** Pattern used to detect a vertex */\r\n    public static VertexPattern = /v(\\s+[\\d|.|+|\\-|e|E]+){3,7}/;\r\n    /** Pattern used to detect a normal */\r\n    public static NormalPattern = /vn(\\s+[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)/;\r\n    /** Pattern used to detect a UV set */\r\n    public static UVPattern = /vt(\\s+[\\d|.|+|\\-|e|E]+)( +[\\d|.|+|\\-|e|E]+)/;\r\n    /** Pattern used to detect a first kind of face (f vertex vertex vertex) */\r\n    public static FacePattern1 = /f\\s+(([\\d]{1,}[\\s]?){3,})+/;\r\n    /** Pattern used to detect a second kind of face (f vertex/uvs vertex/uvs vertex/uvs) */\r\n    public static FacePattern2 = /f\\s+((([\\d]{1,}\\/[\\d]{1,}[\\s]?){3,})+)/;\r\n    /** Pattern used to detect a third kind of face (f vertex/uvs/normal vertex/uvs/normal vertex/uvs/normal) */\r\n    public static FacePattern3 = /f\\s+((([\\d]{1,}\\/[\\d]{1,}\\/[\\d]{1,}[\\s]?){3,})+)/;\r\n    /** Pattern used to detect a fourth kind of face (f vertex//normal vertex//normal vertex//normal)*/\r\n    public static FacePattern4 = /f\\s+((([\\d]{1,}\\/\\/[\\d]{1,}[\\s]?){3,})+)/;\r\n    /** Pattern used to detect a fifth kind of face (f -vertex/-uvs/-normal -vertex/-uvs/-normal -vertex/-uvs/-normal) */\r\n    public static FacePattern5 = /f\\s+(((-[\\d]{1,}\\/-[\\d]{1,}\\/-[\\d]{1,}[\\s]?){3,})+)/;\r\n\r\n    private _loadingOptions: OBJLoadingOptions;\r\n    private _positions: Array<Vector3> = []; //values for the positions of vertices\r\n    private _normals: Array<Vector3> = []; //Values for the normals\r\n    private _uvs: Array<Vector2> = []; //Values for the textures\r\n    private _colors: Array<Color4> = [];\r\n    private _meshesFromObj: Array<MeshObject> = []; //[mesh] Contains all the obj meshes\r\n    private _handledMesh: MeshObject; //The current mesh of meshes array\r\n    private _indicesForBabylon: Array<number> = []; //The list of indices for VertexData\r\n    private _wrappedPositionForBabylon: Array<Vector3> = []; //The list of position in vectors\r\n    private _wrappedUvsForBabylon: Array<Vector2> = []; //Array with all value of uvs to match with the indices\r\n    private _wrappedColorsForBabylon: Array<Color4> = []; // Array with all color values to match with the indices\r\n    private _wrappedNormalsForBabylon: Array<Vector3> = []; //Array with all value of normals to match with the indices\r\n    private _tuplePosNorm: Array<{ normals: Array<number>; idx: Array<number>; uv: Array<number> }> = []; //Create a tuple with indice of Position, Normal, UV  [pos, norm, uvs]\r\n    private _curPositionInIndices = 0;\r\n    private _hasMeshes: Boolean = false; //Meshes are defined in the file\r\n    private _unwrappedPositionsForBabylon: Array<number> = []; //Value of positionForBabylon w/o Vector3() [x,y,z]\r\n    private _unwrappedColorsForBabylon: Array<number> = []; // Value of colorForBabylon w/o Color4() [r,g,b,a]\r\n    private _unwrappedNormalsForBabylon: Array<number> = []; //Value of normalsForBabylon w/o Vector3()  [x,y,z]\r\n    private _unwrappedUVForBabylon: Array<number> = []; //Value of uvsForBabylon w/o Vector3()      [x,y,z]\r\n    private _triangles: Array<string> = []; //Indices from new triangles coming from polygons\r\n    private _materialNameFromObj: string = \"\"; //The name of the current material\r\n    private _objMeshName: string = \"\"; //The name of the current obj mesh\r\n    private _increment: number = 1; //Id for meshes created by the multimaterial\r\n    private _isFirstMaterial: boolean = true;\r\n    private _grayColor = new Color4(0.5, 0.5, 0.5, 1);\r\n    private _materialToUse: string[];\r\n    private _babylonMeshesArray: Array<Mesh>;\r\n\r\n    /**\r\n     * Creates a new SolidParser\r\n     * @param materialToUse defines the array to fill with the list of materials to use (it will be filled by the parse function)\r\n     * @param babylonMeshesArray defines the array to fill with the list of loaded meshes (it will be filled by the parse function)\r\n     * @param loadingOptions defines the loading options to use\r\n     */\r\n    public constructor(materialToUse: string[], babylonMeshesArray: Array<Mesh>, loadingOptions: OBJLoadingOptions) {\r\n        this._materialToUse = materialToUse;\r\n        this._babylonMeshesArray = babylonMeshesArray;\r\n        this._loadingOptions = loadingOptions;\r\n    }\r\n\r\n    /**\r\n     * Search for obj in the given array.\r\n     * This function is called to check if a couple of data already exists in an array.\r\n     *\r\n     * If found, returns the index of the founded tuple index. Returns -1 if not found\r\n     * @param arr Array<{ normals: Array<number>, idx: Array<number> }>\r\n     * @param obj Array<number>\r\n     * @returns {boolean}\r\n     */\r\n    private _isInArray(arr: Array<{ normals: Array<number>; idx: Array<number> }>, obj: Array<number>) {\r\n        if (!arr[obj[0]]) {\r\n            arr[obj[0]] = { normals: [], idx: [] };\r\n        }\r\n        const idx = arr[obj[0]].normals.indexOf(obj[1]);\r\n\r\n        return idx === -1 ? -1 : arr[obj[0]].idx[idx];\r\n    }\r\n\r\n    private _isInArrayUV(arr: Array<{ normals: Array<number>; idx: Array<number>; uv: Array<number> }>, obj: Array<number>) {\r\n        if (!arr[obj[0]]) {\r\n            arr[obj[0]] = { normals: [], idx: [], uv: [] };\r\n        }\r\n        const idx = arr[obj[0]].normals.indexOf(obj[1]);\r\n\r\n        if (idx != 1 && obj[2] === arr[obj[0]].uv[idx]) {\r\n            return arr[obj[0]].idx[idx];\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * This function set the data for each triangle.\r\n     * Data are position, normals and uvs\r\n     * If a tuple of (position, normal) is not set, add the data into the corresponding array\r\n     * If the tuple already exist, add only their indice\r\n     *\r\n     * @param indicePositionFromObj Integer The index in positions array\r\n     * @param indiceUvsFromObj Integer The index in uvs array\r\n     * @param indiceNormalFromObj Integer The index in normals array\r\n     * @param positionVectorFromOBJ Vector3 The value of position at index objIndice\r\n     * @param textureVectorFromOBJ Vector3 The value of uvs\r\n     * @param normalsVectorFromOBJ Vector3 The value of normals at index objNormale\r\n     * @param positionColorsFromOBJ\r\n     */\r\n    private _setData(\r\n        indicePositionFromObj: number,\r\n        indiceUvsFromObj: number,\r\n        indiceNormalFromObj: number,\r\n        positionVectorFromOBJ: Vector3,\r\n        textureVectorFromOBJ: Vector2,\r\n        normalsVectorFromOBJ: Vector3,\r\n        positionColorsFromOBJ?: Color4\r\n    ) {\r\n        //Check if this tuple already exists in the list of tuples\r\n        let _index: number;\r\n        if (this._loadingOptions.optimizeWithUV) {\r\n            _index = this._isInArrayUV(this._tuplePosNorm, [indicePositionFromObj, indiceNormalFromObj, indiceUvsFromObj]);\r\n        } else {\r\n            _index = this._isInArray(this._tuplePosNorm, [indicePositionFromObj, indiceNormalFromObj]);\r\n        }\r\n\r\n        //If it not exists\r\n        if (_index === -1) {\r\n            //Add an new indice.\r\n            //The array of indices is only an array with his length equal to the number of triangles - 1.\r\n            //We add vertices data in this order\r\n            this._indicesForBabylon.push(this._wrappedPositionForBabylon.length);\r\n            //Push the position of vertice for Babylon\r\n            //Each element is a Vector3(x,y,z)\r\n            this._wrappedPositionForBabylon.push(positionVectorFromOBJ);\r\n            //Push the uvs for Babylon\r\n            //Each element is a Vector3(u,v)\r\n            this._wrappedUvsForBabylon.push(textureVectorFromOBJ);\r\n            //Push the normals for Babylon\r\n            //Each element is a Vector3(x,y,z)\r\n            this._wrappedNormalsForBabylon.push(normalsVectorFromOBJ);\r\n\r\n            if (positionColorsFromOBJ !== undefined) {\r\n                //Push the colors for Babylon\r\n                //Each element is a BABYLON.Color4(r,g,b,a)\r\n                this._wrappedColorsForBabylon.push(positionColorsFromOBJ);\r\n            }\r\n\r\n            //Add the tuple in the comparison list\r\n            this._tuplePosNorm[indicePositionFromObj].normals.push(indiceNormalFromObj);\r\n            this._tuplePosNorm[indicePositionFromObj].idx.push(this._curPositionInIndices++);\r\n            if (this._loadingOptions.optimizeWithUV) {\r\n                this._tuplePosNorm[indicePositionFromObj].uv.push(indiceUvsFromObj);\r\n            }\r\n        } else {\r\n            //The tuple already exists\r\n            //Add the index of the already existing tuple\r\n            //At this index we can get the value of position, normal, color and uvs of vertex\r\n            this._indicesForBabylon.push(_index);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Transform Vector() and BABYLON.Color() objects into numbers in an array\r\n     */\r\n    private _unwrapData() {\r\n        //Every array has the same length\r\n        for (let l = 0; l < this._wrappedPositionForBabylon.length; l++) {\r\n            //Push the x, y, z values of each element in the unwrapped array\r\n            this._unwrappedPositionsForBabylon.push(this._wrappedPositionForBabylon[l].x, this._wrappedPositionForBabylon[l].y, this._wrappedPositionForBabylon[l].z);\r\n            this._unwrappedNormalsForBabylon.push(this._wrappedNormalsForBabylon[l].x, this._wrappedNormalsForBabylon[l].y, this._wrappedNormalsForBabylon[l].z);\r\n            this._unwrappedUVForBabylon.push(this._wrappedUvsForBabylon[l].x, this._wrappedUvsForBabylon[l].y); //z is an optional value not supported by BABYLON\r\n            if (this._loadingOptions.importVertexColors) {\r\n                //Push the r, g, b, a values of each element in the unwrapped array\r\n                this._unwrappedColorsForBabylon.push(\r\n                    this._wrappedColorsForBabylon[l].r,\r\n                    this._wrappedColorsForBabylon[l].g,\r\n                    this._wrappedColorsForBabylon[l].b,\r\n                    this._wrappedColorsForBabylon[l].a\r\n                );\r\n            }\r\n        }\r\n        // Reset arrays for the next new meshes\r\n        this._wrappedPositionForBabylon.length = 0;\r\n        this._wrappedNormalsForBabylon.length = 0;\r\n        this._wrappedUvsForBabylon.length = 0;\r\n        this._wrappedColorsForBabylon.length = 0;\r\n        this._tuplePosNorm.length = 0;\r\n        this._curPositionInIndices = 0;\r\n    }\r\n\r\n    /**\r\n     * Create triangles from polygons\r\n     * It is important to notice that a triangle is a polygon\r\n     * We get 5 patterns of face defined in OBJ File :\r\n     * facePattern1 = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\"]\r\n     * facePattern2 = [\"1/1\",\"2/2\",\"3/3\",\"4/4\",\"5/5\",\"6/6\"]\r\n     * facePattern3 = [\"1/1/1\",\"2/2/2\",\"3/3/3\",\"4/4/4\",\"5/5/5\",\"6/6/6\"]\r\n     * facePattern4 = [\"1//1\",\"2//2\",\"3//3\",\"4//4\",\"5//5\",\"6//6\"]\r\n     * facePattern5 = [\"-1/-1/-1\",\"-2/-2/-2\",\"-3/-3/-3\",\"-4/-4/-4\",\"-5/-5/-5\",\"-6/-6/-6\"]\r\n     * Each pattern is divided by the same method\r\n     * @param faces Array[String] The indices of elements\r\n     * @param v Integer The variable to increment\r\n     */\r\n    private _getTriangles(faces: Array<string>, v: number) {\r\n        //Work for each element of the array\r\n        for (let faceIndex = v; faceIndex < faces.length - 1; faceIndex++) {\r\n            //Add on the triangle variable the indexes to obtain triangles\r\n            this._triangles.push(faces[0], faces[faceIndex], faces[faceIndex + 1]);\r\n        }\r\n\r\n        //Result obtained after 2 iterations:\r\n        //Pattern1 => triangle = [\"1\",\"2\",\"3\",\"1\",\"3\",\"4\"];\r\n        //Pattern2 => triangle = [\"1/1\",\"2/2\",\"3/3\",\"1/1\",\"3/3\",\"4/4\"];\r\n        //Pattern3 => triangle = [\"1/1/1\",\"2/2/2\",\"3/3/3\",\"1/1/1\",\"3/3/3\",\"4/4/4\"];\r\n        //Pattern4 => triangle = [\"1//1\",\"2//2\",\"3//3\",\"1//1\",\"3//3\",\"4//4\"];\r\n        //Pattern5 => triangle = [\"-1/-1/-1\",\"-2/-2/-2\",\"-3/-3/-3\",\"-1/-1/-1\",\"-3/-3/-3\",\"-4/-4/-4\"];\r\n    }\r\n\r\n    /**\r\n     * Create triangles and push the data for each polygon for the pattern 1\r\n     * In this pattern we get vertice positions\r\n     * @param face\r\n     * @param v\r\n     */\r\n    private _setDataForCurrentFaceWithPattern1(face: Array<string>, v: number) {\r\n        //Get the indices of triangles for each polygon\r\n        this._getTriangles(face, v);\r\n        //For each element in the triangles array.\r\n        //This var could contains 1 to an infinity of triangles\r\n        for (let k = 0; k < this._triangles.length; k++) {\r\n            // Set position indice\r\n            const indicePositionFromObj = parseInt(this._triangles[k]) - 1;\r\n\r\n            this._setData(\r\n                indicePositionFromObj,\r\n                0,\r\n                0, // In the pattern 1, normals and uvs are not defined\r\n                this._positions[indicePositionFromObj], // Get the vectors data\r\n                Vector2.Zero(),\r\n                Vector3.Up(), // Create default vectors\r\n                this._loadingOptions.importVertexColors ? this._colors[indicePositionFromObj] : undefined\r\n            );\r\n        }\r\n        //Reset variable for the next line\r\n        this._triangles.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Create triangles and push the data for each polygon for the pattern 2\r\n     * In this pattern we get vertice positions and uvsu\r\n     * @param face\r\n     * @param v\r\n     */\r\n    private _setDataForCurrentFaceWithPattern2(face: Array<string>, v: number) {\r\n        //Get the indices of triangles for each polygon\r\n        this._getTriangles(face, v);\r\n        for (let k = 0; k < this._triangles.length; k++) {\r\n            //triangle[k] = \"1/1\"\r\n            //Split the data for getting position and uv\r\n            const point = this._triangles[k].split(\"/\"); // [\"1\", \"1\"]\r\n            //Set position indice\r\n            const indicePositionFromObj = parseInt(point[0]) - 1;\r\n            //Set uv indice\r\n            const indiceUvsFromObj = parseInt(point[1]) - 1;\r\n\r\n            this._setData(\r\n                indicePositionFromObj,\r\n                indiceUvsFromObj,\r\n                0, //Default value for normals\r\n                this._positions[indicePositionFromObj], //Get the values for each element\r\n                this._uvs[indiceUvsFromObj],\r\n                Vector3.Up(), //Default value for normals\r\n                this._loadingOptions.importVertexColors ? this._colors[indicePositionFromObj] : undefined\r\n            );\r\n        }\r\n\r\n        //Reset variable for the next line\r\n        this._triangles.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Create triangles and push the data for each polygon for the pattern 3\r\n     * In this pattern we get vertice positions, uvs and normals\r\n     * @param face\r\n     * @param v\r\n     */\r\n    private _setDataForCurrentFaceWithPattern3(face: Array<string>, v: number) {\r\n        //Get the indices of triangles for each polygon\r\n        this._getTriangles(face, v);\r\n\r\n        for (let k = 0; k < this._triangles.length; k++) {\r\n            //triangle[k] = \"1/1/1\"\r\n            //Split the data for getting position, uv, and normals\r\n            const point = this._triangles[k].split(\"/\"); // [\"1\", \"1\", \"1\"]\r\n            // Set position indice\r\n            const indicePositionFromObj = parseInt(point[0]) - 1;\r\n            // Set uv indice\r\n            const indiceUvsFromObj = parseInt(point[1]) - 1;\r\n            // Set normal indice\r\n            const indiceNormalFromObj = parseInt(point[2]) - 1;\r\n\r\n            this._setData(\r\n                indicePositionFromObj,\r\n                indiceUvsFromObj,\r\n                indiceNormalFromObj,\r\n                this._positions[indicePositionFromObj],\r\n                this._uvs[indiceUvsFromObj],\r\n                this._normals[indiceNormalFromObj] //Set the vector for each component\r\n            );\r\n        }\r\n        //Reset variable for the next line\r\n        this._triangles.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Create triangles and push the data for each polygon for the pattern 4\r\n     * In this pattern we get vertice positions and normals\r\n     * @param face\r\n     * @param v\r\n     */\r\n    private _setDataForCurrentFaceWithPattern4(face: Array<string>, v: number) {\r\n        this._getTriangles(face, v);\r\n\r\n        for (let k = 0; k < this._triangles.length; k++) {\r\n            //triangle[k] = \"1//1\"\r\n            //Split the data for getting position and normals\r\n            const point = this._triangles[k].split(\"//\"); // [\"1\", \"1\"]\r\n            // We check indices, and normals\r\n            const indicePositionFromObj = parseInt(point[0]) - 1;\r\n            const indiceNormalFromObj = parseInt(point[1]) - 1;\r\n\r\n            this._setData(\r\n                indicePositionFromObj,\r\n                1, //Default value for uv\r\n                indiceNormalFromObj,\r\n                this._positions[indicePositionFromObj], //Get each vector of data\r\n                Vector2.Zero(),\r\n                this._normals[indiceNormalFromObj],\r\n                this._loadingOptions.importVertexColors ? this._colors[indicePositionFromObj] : undefined\r\n            );\r\n        }\r\n        //Reset variable for the next line\r\n        this._triangles.length = 0;\r\n    }\r\n\r\n    /*\r\n     * Create triangles and push the data for each polygon for the pattern 3\r\n     * In this pattern we get vertice positions, uvs and normals\r\n     * @param face\r\n     * @param v\r\n     */\r\n    private _setDataForCurrentFaceWithPattern5(face: Array<string>, v: number) {\r\n        //Get the indices of triangles for each polygon\r\n        this._getTriangles(face, v);\r\n\r\n        for (let k = 0; k < this._triangles.length; k++) {\r\n            //triangle[k] = \"-1/-1/-1\"\r\n            //Split the data for getting position, uv, and normals\r\n            const point = this._triangles[k].split(\"/\"); // [\"-1\", \"-1\", \"-1\"]\r\n            // Set position indice\r\n            const indicePositionFromObj = this._positions.length + parseInt(point[0]);\r\n            // Set uv indice\r\n            const indiceUvsFromObj = this._uvs.length + parseInt(point[1]);\r\n            // Set normal indice\r\n            const indiceNormalFromObj = this._normals.length + parseInt(point[2]);\r\n\r\n            this._setData(\r\n                indicePositionFromObj,\r\n                indiceUvsFromObj,\r\n                indiceNormalFromObj,\r\n                this._positions[indicePositionFromObj],\r\n                this._uvs[indiceUvsFromObj],\r\n                this._normals[indiceNormalFromObj], //Set the vector for each component\r\n                this._loadingOptions.importVertexColors ? this._colors[indicePositionFromObj] : undefined\r\n            );\r\n        }\r\n        //Reset variable for the next line\r\n        this._triangles.length = 0;\r\n    }\r\n\r\n    private _addPreviousObjMesh() {\r\n        //Check if it is not the first mesh. Otherwise we don't have data.\r\n        if (this._meshesFromObj.length > 0) {\r\n            //Get the previous mesh for applying the data about the faces\r\n            //=> in obj file, faces definition append after the name of the mesh\r\n            this._handledMesh = this._meshesFromObj[this._meshesFromObj.length - 1];\r\n\r\n            //Set the data into Array for the mesh\r\n            this._unwrapData();\r\n\r\n            // Reverse tab. Otherwise face are displayed in the wrong sens\r\n            this._indicesForBabylon.reverse();\r\n            //Set the information for the mesh\r\n            //Slice the array to avoid rewriting because of the fact this is the same var which be rewrited\r\n            this._handledMesh.indices = this._indicesForBabylon.slice();\r\n            this._handledMesh.positions = this._unwrappedPositionsForBabylon.slice();\r\n            this._handledMesh.normals = this._unwrappedNormalsForBabylon.slice();\r\n            this._handledMesh.uvs = this._unwrappedUVForBabylon.slice();\r\n\r\n            if (this._loadingOptions.importVertexColors) {\r\n                this._handledMesh.colors = this._unwrappedColorsForBabylon.slice();\r\n            }\r\n\r\n            //Reset the array for the next mesh\r\n            this._indicesForBabylon.length = 0;\r\n            this._unwrappedPositionsForBabylon.length = 0;\r\n            this._unwrappedColorsForBabylon.length = 0;\r\n            this._unwrappedNormalsForBabylon.length = 0;\r\n            this._unwrappedUVForBabylon.length = 0;\r\n        }\r\n    }\r\n\r\n    private _optimizeNormals(mesh: AbstractMesh): void {\r\n        const positions = mesh.getVerticesData(VertexBuffer.PositionKind);\r\n        const normals = mesh.getVerticesData(VertexBuffer.NormalKind);\r\n        const mapVertices: { [key: string]: number[] } = {};\r\n\r\n        if (!positions || !normals) {\r\n            return;\r\n        }\r\n\r\n        for (let i = 0; i < positions.length / 3; i++) {\r\n            const x = positions[i * 3 + 0];\r\n            const y = positions[i * 3 + 1];\r\n            const z = positions[i * 3 + 2];\r\n            const key = x + \"_\" + y + \"_\" + z;\r\n\r\n            let lst = mapVertices[key];\r\n            if (!lst) {\r\n                lst = [];\r\n                mapVertices[key] = lst;\r\n            }\r\n            lst.push(i);\r\n        }\r\n\r\n        const normal = new Vector3();\r\n        for (const key in mapVertices) {\r\n            const lst = mapVertices[key];\r\n            if (lst.length < 2) {\r\n                continue;\r\n            }\r\n\r\n            const v0Idx = lst[0];\r\n            for (let i = 1; i < lst.length; ++i) {\r\n                const vIdx = lst[i];\r\n                normals[v0Idx * 3 + 0] += normals[vIdx * 3 + 0];\r\n                normals[v0Idx * 3 + 1] += normals[vIdx * 3 + 1];\r\n                normals[v0Idx * 3 + 2] += normals[vIdx * 3 + 2];\r\n            }\r\n\r\n            normal.copyFromFloats(normals[v0Idx * 3 + 0], normals[v0Idx * 3 + 1], normals[v0Idx * 3 + 2]);\r\n            normal.normalize();\r\n\r\n            for (let i = 0; i < lst.length; ++i) {\r\n                const vIdx = lst[i];\r\n                normals[vIdx * 3 + 0] = normal.x;\r\n                normals[vIdx * 3 + 1] = normal.y;\r\n                normals[vIdx * 3 + 2] = normal.z;\r\n            }\r\n        }\r\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\r\n    }\r\n\r\n    /**\r\n     * Function used to parse an OBJ string\r\n     * @param meshesNames defines the list of meshes to load (all if not defined)\r\n     * @param data defines the OBJ string\r\n     * @param scene defines the hosting scene\r\n     * @param assetContainer defines the asset container to load data in\r\n     * @param onFileToLoadFound defines a callback that will be called if a MTL file is found\r\n     */\r\n    public parse(meshesNames: any, data: string, scene: Scene, assetContainer: Nullable<AssetContainer>, onFileToLoadFound: (fileToLoad: string) => void): void {\r\n        // Split the file into lines\r\n        const lines = data.split(\"\\n\");\r\n        // Look at each line\r\n        for (let i = 0; i < lines.length; i++) {\r\n            const line = lines[i].trim().replace(/\\s\\s/g, \" \");\r\n            let result;\r\n\r\n            // Comment or newLine\r\n            if (line.length === 0 || line.charAt(0) === \"#\") {\r\n                continue;\r\n\r\n                //Get information about one position possible for the vertices\r\n            } else if (SolidParser.VertexPattern.test(line)) {\r\n                result = line.match(/[^ ]+/g)!; // match will return non-null due to passing regex pattern\r\n\r\n                // Value of result with line: \"v 1.0 2.0 3.0\"\r\n                // [\"v\", \"1.0\", \"2.0\", \"3.0\"]\r\n                // Create a Vector3 with the position x, y, z\r\n                this._positions.push(new Vector3(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3])));\r\n\r\n                if (this._loadingOptions.importVertexColors) {\r\n                    if (result.length >= 7) {\r\n                        const r = parseFloat(result[4]);\r\n                        const g = parseFloat(result[5]);\r\n                        const b = parseFloat(result[6]);\r\n\r\n                        this._colors.push(\r\n                            new Color4(r > 1 ? r / 255 : r, g > 1 ? g / 255 : g, b > 1 ? b / 255 : b, result.length === 7 || result[7] === undefined ? 1 : parseFloat(result[7]))\r\n                        );\r\n                    } else {\r\n                        // TODO: maybe push NULL and if all are NULL to skip (and remove grayColor var).\r\n                        this._colors.push(this._grayColor);\r\n                    }\r\n                }\r\n            } else if ((result = SolidParser.NormalPattern.exec(line)) !== null) {\r\n                //Create a Vector3 with the normals x, y, z\r\n                //Value of result\r\n                // [\"vn 1.0 2.0 3.0\", \"1.0\", \"2.0\", \"3.0\"]\r\n                //Add the Vector in the list of normals\r\n                this._normals.push(new Vector3(parseFloat(result[1]), parseFloat(result[2]), parseFloat(result[3])));\r\n            } else if ((result = SolidParser.UVPattern.exec(line)) !== null) {\r\n                //Create a Vector2 with the normals u, v\r\n                //Value of result\r\n                // [\"vt 0.1 0.2 0.3\", \"0.1\", \"0.2\"]\r\n                //Add the Vector in the list of uvs\r\n                this._uvs.push(new Vector2(parseFloat(result[1]) * this._loadingOptions.UVScaling.x, parseFloat(result[2]) * this._loadingOptions.UVScaling.y));\r\n\r\n                //Identify patterns of faces\r\n                //Face could be defined in different type of pattern\r\n            } else if ((result = SolidParser.FacePattern3.exec(line)) !== null) {\r\n                //Value of result:\r\n                //[\"f 1/1/1 2/2/2 3/3/3\", \"1/1/1 2/2/2 3/3/3\"...]\r\n\r\n                //Set the data for this face\r\n                this._setDataForCurrentFaceWithPattern3(\r\n                    result[1].trim().split(\" \"), // [\"1/1/1\", \"2/2/2\", \"3/3/3\"]\r\n                    1\r\n                );\r\n            } else if ((result = SolidParser.FacePattern4.exec(line)) !== null) {\r\n                //Value of result:\r\n                //[\"f 1//1 2//2 3//3\", \"1//1 2//2 3//3\"...]\r\n\r\n                //Set the data for this face\r\n                this._setDataForCurrentFaceWithPattern4(\r\n                    result[1].trim().split(\" \"), // [\"1//1\", \"2//2\", \"3//3\"]\r\n                    1\r\n                );\r\n            } else if ((result = SolidParser.FacePattern5.exec(line)) !== null) {\r\n                //Value of result:\r\n                //[\"f -1/-1/-1 -2/-2/-2 -3/-3/-3\", \"-1/-1/-1 -2/-2/-2 -3/-3/-3\"...]\r\n\r\n                //Set the data for this face\r\n                this._setDataForCurrentFaceWithPattern5(\r\n                    result[1].trim().split(\" \"), // [\"-1/-1/-1\", \"-2/-2/-2\", \"-3/-3/-3\"]\r\n                    1\r\n                );\r\n            } else if ((result = SolidParser.FacePattern2.exec(line)) !== null) {\r\n                //Value of result:\r\n                //[\"f 1/1 2/2 3/3\", \"1/1 2/2 3/3\"...]\r\n\r\n                //Set the data for this face\r\n                this._setDataForCurrentFaceWithPattern2(\r\n                    result[1].trim().split(\" \"), // [\"1/1\", \"2/2\", \"3/3\"]\r\n                    1\r\n                );\r\n            } else if ((result = SolidParser.FacePattern1.exec(line)) !== null) {\r\n                //Value of result\r\n                //[\"f 1 2 3\", \"1 2 3\"...]\r\n\r\n                //Set the data for this face\r\n                this._setDataForCurrentFaceWithPattern1(\r\n                    result[1].trim().split(\" \"), // [\"1\", \"2\", \"3\"]\r\n                    1\r\n                );\r\n\r\n                // Define a mesh or an object\r\n                // Each time this keyword is analysed, create a new Object with all data for creating a babylonMesh\r\n            } else if (SolidParser.GroupDescriptor.test(line) || SolidParser.ObjectDescriptor.test(line)) {\r\n                // Create a new mesh corresponding to the name of the group.\r\n                // Definition of the mesh\r\n                const objMesh: MeshObject = {\r\n                    name: line.substring(2).trim(), //Set the name of the current obj mesh\r\n                    indices: undefined,\r\n                    positions: undefined,\r\n                    normals: undefined,\r\n                    uvs: undefined,\r\n                    colors: undefined,\r\n                    materialName: this._materialNameFromObj,\r\n                };\r\n                this._addPreviousObjMesh();\r\n\r\n                //Push the last mesh created with only the name\r\n                this._meshesFromObj.push(objMesh);\r\n\r\n                //Set this variable to indicate that now meshesFromObj has objects defined inside\r\n                this._hasMeshes = true;\r\n                this._isFirstMaterial = true;\r\n                this._increment = 1;\r\n                //Keyword for applying a material\r\n            } else if (SolidParser.UseMtlDescriptor.test(line)) {\r\n                //Get the name of the material\r\n                this._materialNameFromObj = line.substring(7).trim();\r\n\r\n                //If this new material is in the same mesh\r\n\r\n                if (!this._isFirstMaterial || !this._hasMeshes) {\r\n                    //Set the data for the previous mesh\r\n                    this._addPreviousObjMesh();\r\n                    //Create a new mesh\r\n                    const objMesh: MeshObject =\r\n                        //Set the name of the current obj mesh\r\n                        {\r\n                            name: (this._objMeshName || \"mesh\") + \"_mm\" + this._increment.toString(), //Set the name of the current obj mesh\r\n                            indices: undefined,\r\n                            positions: undefined,\r\n                            normals: undefined,\r\n                            uvs: undefined,\r\n                            colors: undefined,\r\n                            materialName: this._materialNameFromObj,\r\n                        };\r\n                    this._increment++;\r\n                    //If meshes are already defined\r\n                    this._meshesFromObj.push(objMesh);\r\n                    this._hasMeshes = true;\r\n                }\r\n                //Set the material name if the previous line define a mesh\r\n\r\n                if (this._hasMeshes && this._isFirstMaterial) {\r\n                    //Set the material name to the previous mesh (1 material per mesh)\r\n                    this._meshesFromObj[this._meshesFromObj.length - 1].materialName = this._materialNameFromObj;\r\n                    this._isFirstMaterial = false;\r\n                }\r\n                // Keyword for loading the mtl file\r\n            } else if (SolidParser.MtlLibGroupDescriptor.test(line)) {\r\n                // Get the name of mtl file\r\n                onFileToLoadFound(line.substring(7).trim());\r\n\r\n                // Apply smoothing\r\n            } else if (SolidParser.SmoothDescriptor.test(line)) {\r\n                // smooth shading => apply smoothing\r\n                // Today I don't know it work with babylon and with obj.\r\n                // With the obj file  an integer is set\r\n            } else {\r\n                //If there is another possibility\r\n                console.log(\"Unhandled expression at line : \" + line);\r\n            }\r\n        }\r\n\r\n        // At the end of the file, add the last mesh into the meshesFromObj array\r\n        if (this._hasMeshes) {\r\n            // Set the data for the last mesh\r\n            this._handledMesh = this._meshesFromObj[this._meshesFromObj.length - 1];\r\n\r\n            //Reverse indices for displaying faces in the good sense\r\n            this._indicesForBabylon.reverse();\r\n            //Get the good array\r\n            this._unwrapData();\r\n            //Set array\r\n            this._handledMesh.indices = this._indicesForBabylon;\r\n            this._handledMesh.positions = this._unwrappedPositionsForBabylon;\r\n            this._handledMesh.normals = this._unwrappedNormalsForBabylon;\r\n            this._handledMesh.uvs = this._unwrappedUVForBabylon;\r\n\r\n            if (this._loadingOptions.importVertexColors) {\r\n                this._handledMesh.colors = this._unwrappedColorsForBabylon;\r\n            }\r\n        }\r\n\r\n        // If any o or g keyword not found, create a mesh with a random id\r\n        if (!this._hasMeshes) {\r\n            let newMaterial: Nullable<StandardMaterial> = null;\r\n            if (this._indicesForBabylon.length) {\r\n                // reverse tab of indices\r\n                this._indicesForBabylon.reverse();\r\n                //Get positions normals uvs\r\n                this._unwrapData();\r\n            } else {\r\n                // There is no indices in the file. We will have to switch to point cloud rendering\r\n                for (const pos of this._positions) {\r\n                    this._unwrappedPositionsForBabylon.push(pos.x, pos.y, pos.z);\r\n                }\r\n\r\n                if (this._normals.length) {\r\n                    for (const normal of this._normals) {\r\n                        this._unwrappedNormalsForBabylon.push(normal.x, normal.y, normal.z);\r\n                    }\r\n                }\r\n\r\n                if (this._uvs.length) {\r\n                    for (const uv of this._uvs) {\r\n                        this._unwrappedUVForBabylon.push(uv.x, uv.y);\r\n                    }\r\n                }\r\n\r\n                if (this._colors.length) {\r\n                    for (const color of this._colors) {\r\n                        this._unwrappedColorsForBabylon.push(color.r, color.g, color.b, color.a);\r\n                    }\r\n                }\r\n\r\n                if (!this._materialNameFromObj) {\r\n                    // Create a material with point cloud on\r\n                    newMaterial = new StandardMaterial(Geometry.RandomId(), scene);\r\n\r\n                    newMaterial.pointsCloud = true;\r\n\r\n                    this._materialNameFromObj = newMaterial.name;\r\n\r\n                    if (!this._normals.length) {\r\n                        newMaterial.disableLighting = true;\r\n                        newMaterial.emissiveColor = Color3.White();\r\n                    }\r\n                }\r\n            }\r\n\r\n            //Set data for one mesh\r\n            this._meshesFromObj.push({\r\n                name: Geometry.RandomId(),\r\n                indices: this._indicesForBabylon,\r\n                positions: this._unwrappedPositionsForBabylon,\r\n                colors: this._unwrappedColorsForBabylon,\r\n                normals: this._unwrappedNormalsForBabylon,\r\n                uvs: this._unwrappedUVForBabylon,\r\n                materialName: this._materialNameFromObj,\r\n                directMaterial: newMaterial,\r\n            });\r\n        }\r\n\r\n        //Set data for each mesh\r\n        for (let j = 0; j < this._meshesFromObj.length; j++) {\r\n            //check meshesNames (stlFileLoader)\r\n            if (meshesNames && this._meshesFromObj[j].name) {\r\n                if (meshesNames instanceof Array) {\r\n                    if (meshesNames.indexOf(this._meshesFromObj[j].name) === -1) {\r\n                        continue;\r\n                    }\r\n                } else {\r\n                    if (this._meshesFromObj[j].name !== meshesNames) {\r\n                        continue;\r\n                    }\r\n                }\r\n            }\r\n\r\n            //Get the current mesh\r\n            //Set the data with VertexBuffer for each mesh\r\n            this._handledMesh = this._meshesFromObj[j];\r\n            //Create a Mesh with the name of the obj mesh\r\n\r\n            scene._blockEntityCollection = !!assetContainer;\r\n            const babylonMesh = new Mesh(this._meshesFromObj[j].name, scene);\r\n            babylonMesh._parentContainer = assetContainer;\r\n            scene._blockEntityCollection = false;\r\n\r\n            //Push the name of the material to an array\r\n            //This is indispensable for the importMesh function\r\n            this._materialToUse.push(this._meshesFromObj[j].materialName);\r\n\r\n            if (this._handledMesh.positions?.length === 0) {\r\n                //Push the mesh into an array\r\n                this._babylonMeshesArray.push(babylonMesh);\r\n                continue;\r\n            }\r\n\r\n            const vertexData: VertexData = new VertexData(); //The container for the values\r\n            //Set the data for the babylonMesh\r\n            vertexData.uvs = this._handledMesh.uvs as FloatArray;\r\n            vertexData.indices = this._handledMesh.indices as IndicesArray;\r\n            vertexData.positions = this._handledMesh.positions as FloatArray;\r\n            if (this._loadingOptions.computeNormals) {\r\n                const normals: Array<number> = new Array<number>();\r\n                VertexData.ComputeNormals(this._handledMesh.positions, this._handledMesh.indices, normals);\r\n                vertexData.normals = normals;\r\n            } else {\r\n                vertexData.normals = this._handledMesh.normals as FloatArray;\r\n            }\r\n            if (this._loadingOptions.importVertexColors) {\r\n                vertexData.colors = this._handledMesh.colors as FloatArray;\r\n            }\r\n            //Set the data from the VertexBuffer to the current Mesh\r\n            vertexData.applyToMesh(babylonMesh);\r\n            if (this._loadingOptions.invertY) {\r\n                babylonMesh.scaling.y *= -1;\r\n            }\r\n            if (this._loadingOptions.optimizeNormals) {\r\n                this._optimizeNormals(babylonMesh);\r\n            }\r\n\r\n            //Push the mesh into an array\r\n            this._babylonMeshesArray.push(babylonMesh);\r\n\r\n            if (this._handledMesh.directMaterial) {\r\n                babylonMesh.material = this._handledMesh.directMaterial;\r\n            }\r\n        }\r\n    }\r\n}\r\n"]}