{"version": 3, "file": "KHR_materials_pbrSpecularGlossiness.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_pbrSpecularGlossiness.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,qCAAqC,CAAC;AAEnD;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,mCAAmC;IAkB5C;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAqC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACnI,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAChG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YACnH,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;YAC7E,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sCAAsC,CAAC,OAAe,EAAE,QAAmB,EAAE,UAA8C,EAAE,eAAyB;QAC1J,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC;QAChC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC;QAEjC,IAAI,UAAU,CAAC,aAAa,EAAE;YAC1B,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;YACzE,eAAe,CAAC,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;SACvD;aAAM;YACH,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;SAChD;QAED,eAAe,CAAC,iBAAiB,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAC7H,eAAe,CAAC,YAAY,GAAG,UAAU,CAAC,gBAAgB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,gBAAgB,CAAC;QAE1G,IAAI,UAAU,CAAC,cAAc,EAAE;YAC3B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,iBAAiB,EAAE,UAAU,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE;gBAClG,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,YAAY,CAAC;gBACnD,eAAe,CAAC,aAAa,GAAG,OAAO,CAAC;YAC5C,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,yBAAyB,EAAE;YACtC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,4BAA4B,EAAE,UAAU,CAAC,yBAAyB,EAAE,CAAC,OAAO,EAAE,EAAE;gBACxH,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,wBAAwB,CAAC;gBAC/D,eAAe,CAAC,mBAAmB,GAAG,OAAO,CAAC;gBAC9C,eAAe,CAAC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC;YACxD,CAAC,CAAC,CACL,CAAC;YAEF,eAAe,CAAC,uCAAuC,GAAG,IAAI,CAAC;SAClE;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,mCAAmC,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsPbrSpecularGlossiness } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_pbrSpecularGlossiness\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Archived/KHR_materials_pbrSpecularGlossiness/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_pbrSpecularGlossiness implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 200;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsPbrSpecularGlossiness>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadSpecularGlossinessPropertiesAsync(extensionContext, material, extension, babylonMaterial));\r\n            this._loader.loadMaterialAlphaProperties(context, material, babylonMaterial);\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadSpecularGlossinessPropertiesAsync(context: string, material: IMaterial, properties: IKHRMaterialsPbrSpecularGlossiness, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        babylonMaterial.metallic = null;\r\n        babylonMaterial.roughness = null;\r\n\r\n        if (properties.diffuseFactor) {\r\n            babylonMaterial.albedoColor = Color3.FromArray(properties.diffuseFactor);\r\n            babylonMaterial.alpha = properties.diffuseFactor[3];\r\n        } else {\r\n            babylonMaterial.albedoColor = Color3.White();\r\n        }\r\n\r\n        babylonMaterial.reflectivityColor = properties.specularFactor ? Color3.FromArray(properties.specularFactor) : Color3.White();\r\n        babylonMaterial.microSurface = properties.glossinessFactor == undefined ? 1 : properties.glossinessFactor;\r\n\r\n        if (properties.diffuseTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/diffuseTexture`, properties.diffuseTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Diffuse)`;\r\n                    babylonMaterial.albedoTexture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.specularGlossinessTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/specularGlossinessTexture`, properties.specularGlossinessTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Specular Glossiness)`;\r\n                    babylonMaterial.reflectivityTexture = texture;\r\n                    babylonMaterial.reflectivityTexture.hasAlpha = true;\r\n                })\r\n            );\r\n\r\n            babylonMaterial.useMicroSurfaceFromReflectivityMapAlpha = true;\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_pbrSpecularGlossiness(loader));\r\n"]}