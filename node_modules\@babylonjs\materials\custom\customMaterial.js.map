{"version": 3, "file": "customMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/custom/customMaterial.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AAGnE,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAEpD,MAAM,OAAO,qBAAqB;IAI9B,gBAAe,CAAC;CACnB;AAED,MAAM,OAAO,kBAAkB;IAC3B,gBAAe,CAAC;CAiCnB;AAED,MAAM,OAAO,cAAe,SAAQ,gBAAgB;IAczC,eAAe,CAAC,IAAsB,EAAE,MAAc;QACzD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBACxC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACjB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE;oBACxB,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC1D;qBAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE;oBACzB,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBACzD;aACJ;SACJ;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBACxC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC1G,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC3D;aACJ;SACJ;IACL,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,GAAa;QAC5C,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACnD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;iBAC5D;aACJ;SACJ;QACD,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBACrD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;oBACnD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;iBAC5D;aACJ;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAEM,OAAO,CAAC,UAAkB,EAAE,QAAkB,EAAE,cAAwB,EAAE,QAAkB,EAAE,OAAmC,EAAE,UAAqB;QAC3J,IAAI,UAAU,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAClC;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,cAAc,CAAC,aAAa,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAW,SAAS,GAAG,cAAc,CAAC,aAAa,CAAC;QAE9D,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACvB,IAAI,CAAC,CAAC,EAAE;gBACJ,OAAO;aACV;YACD,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,IAAI;gBACA,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACtB;YAAC,OAAO,CAAC,EAAE,GAAE;QAClB,CAAC,CAAC;QAEF,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;aACpK,OAAO,CACJ,mCAAmC,EACnC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CACjJ;aACA,OAAO,CAAC,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;aACvH,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAE,CAAC;aACtJ,OAAO,CAAC,qCAAqC,EAAE,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,CAAC;aAChJ,OAAO,CAAC,gCAAgC,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAEvH,IAAI,IAAI,CAAC,WAAW,CAAC,6BAA6B,EAAE;YAChD,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,CAAC,OAAO,CAC3F,uCAAuC,EACvC,IAAI,CAAC,WAAW,CAAC,6BAA6B,CACjD,CAAC;SACL;QAED,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CACnE,+BAA+B,EAC/B,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CACzE;aACI,OAAO,CAAC,oCAAoC,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;aAC7H,OAAO,CACJ,qCAAqC,EACrC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CACrJ;aACA,OAAO,CAAC,wCAAwC,EAAE,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC;aAC3I,OAAO,CAAC,sCAAsC,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;aACrI,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;aACxI,OAAO,CAAC,0CAA0C,EAAE,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,CAAC;aACjJ,OAAO,CAAC,kCAAkC,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE7H,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE;YACtC,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,OAAO,CACzF,oCAAoC,EACpC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACvC,CAAC;SACL;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;IACnE,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,IAAY,EAAE,KAAU;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;YAC/B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;SAClC;QACD,IAAI,KAAK,EAAE;YACP,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;gBACzB,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;aAC/D;iBAAM;gBACG,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;aAC/D;SACJ;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;SAC/B;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,oBAAoB,CAAC,UAAkB;QAC1C,IAAI,CAAC,WAAW,CAAC,oBAAoB,GAAG,UAAU,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,UAAkB;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,UAAkB;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,uBAAuB,CAAC,UAAkB;QAC7C,IAAI,CAAC,WAAW,CAAC,uBAAuB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,qBAAqB,CAAC,UAAkB;QAC3C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,sBAAsB,CAAC,UAAkB;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,mBAAmB,CAAC,UAAkB;QACzC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,yBAAyB,CAAC,UAAkB;QAC/C,IAAI,CAAC,WAAW,CAAC,yBAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,YAAY,CAAC,UAAkB;QAClC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,UAAU,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,kBAAkB,CAAC,UAAkB;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,gBAAgB,CAAC,UAAkB;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,6BAA6B,CAAC,UAAkB;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,2BAA2B,CAAC,UAAkB;QACjD,IAAI,CAAC,WAAW,CAAC,2BAA2B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,6BAA6B,CAAC,UAAkB;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC;QAC5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,UAAkB;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;;AAvPa,4BAAa,GAAG,CAAC,CAAC;AA0PpC,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport type { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nexport class CustomShaderStructure {\r\n    public FragmentStore: string;\r\n    public VertexStore: string;\r\n\r\n    constructor() {}\r\n}\r\n\r\nexport class ShaderSpecialParts {\r\n    constructor() {}\r\n\r\n    public Fragment_Begin: string;\r\n    public Fragment_Definitions: string;\r\n    public Fragment_MainBegin: string;\r\n    public Fragment_MainEnd: string;\r\n\r\n    // diffuseColor\r\n    public Fragment_Custom_Diffuse: string;\r\n    // lights\r\n    public Fragment_Before_Lights: string;\r\n    // fog\r\n    public Fragment_Before_Fog: string;\r\n    // alpha\r\n    public Fragment_Custom_Alpha: string;\r\n\r\n    public Fragment_Before_FragColor: string;\r\n\r\n    public Vertex_Begin: string;\r\n    public Vertex_Definitions: string;\r\n    public Vertex_MainBegin: string;\r\n\r\n    // positionUpdated\r\n    public Vertex_Before_PositionUpdated: string;\r\n\r\n    // normalUpdated\r\n    public Vertex_Before_NormalUpdated: string;\r\n\r\n    // worldPosComputed\r\n    public Vertex_After_WorldPosComputed: string;\r\n\r\n    // mainEnd\r\n    public Vertex_MainEnd: string;\r\n}\r\n\r\nexport class CustomMaterial extends StandardMaterial {\r\n    public static ShaderIndexer = 1;\r\n    public CustomParts: ShaderSpecialParts;\r\n    _isCreatedShader: boolean;\r\n    _createdShaderName: string;\r\n    _customUniform: string[];\r\n    _newUniforms: string[];\r\n    _newUniformInstances: { [name: string]: any };\r\n    _newSamplerInstances: { [name: string]: Texture };\r\n    _customAttributes: string[];\r\n\r\n    public FragmentShader: string;\r\n    public VertexShader: string;\r\n\r\n    public AttachAfterBind(mesh: Mesh | undefined, effect: Effect) {\r\n        if (this._newUniformInstances) {\r\n            for (const el in this._newUniformInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"vec2\") {\r\n                    effect.setVector2(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec3\") {\r\n                    effect.setVector3(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec4\") {\r\n                    effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"mat4\") {\r\n                    effect.setMatrix(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"float\") {\r\n                    effect.setFloat(ea[1], this._newUniformInstances[el]);\r\n                }\r\n            }\r\n        }\r\n        if (this._newSamplerInstances) {\r\n            for (const el in this._newSamplerInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"sampler2D\" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {\r\n                    effect.setTexture(ea[1], this._newSamplerInstances[el]);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    public ReviewUniform(name: string, arr: string[]): string[] {\r\n        if (name == \"uniform\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") == -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        if (name == \"sampler\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") != -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        return arr;\r\n    }\r\n\r\n    public Builder(shaderName: string, uniforms: string[], uniformBuffers: string[], samplers: string[], defines: MaterialDefines | string[], attributes?: string[]): string {\r\n        if (attributes && this._customAttributes && this._customAttributes.length > 0) {\r\n            attributes.push(...this._customAttributes);\r\n        }\r\n\r\n        this.ReviewUniform(\"uniform\", uniforms);\r\n        this.ReviewUniform(\"sampler\", samplers);\r\n\r\n        if (this._isCreatedShader) {\r\n            return this._createdShaderName;\r\n        }\r\n        this._isCreatedShader = false;\r\n\r\n        CustomMaterial.ShaderIndexer++;\r\n        const name: string = \"custom_\" + CustomMaterial.ShaderIndexer;\r\n\r\n        const fn_afterBind = this._afterBind.bind(this);\r\n        this._afterBind = (m, e) => {\r\n            if (!e) {\r\n                return;\r\n            }\r\n            this.AttachAfterBind(m, e);\r\n            try {\r\n                fn_afterBind(m, e);\r\n            } catch (e) {}\r\n        };\r\n\r\n        Effect.ShadersStore[name + \"VertexShader\"] = this.VertexShader.replace(\"#define CUSTOM_VERTEX_BEGIN\", this.CustomParts.Vertex_Begin ? this.CustomParts.Vertex_Begin : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_VERTEX_DEFINITIONS\",\r\n                (this._customUniform ? this._customUniform.join(\"\\n\") : \"\") + (this.CustomParts.Vertex_Definitions ? this.CustomParts.Vertex_Definitions : \"\")\r\n            )\r\n            .replace(\"#define CUSTOM_VERTEX_MAIN_BEGIN\", this.CustomParts.Vertex_MainBegin ? this.CustomParts.Vertex_MainBegin : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_UPDATE_POSITION\", this.CustomParts.Vertex_Before_PositionUpdated ? this.CustomParts.Vertex_Before_PositionUpdated : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_UPDATE_NORMAL\", this.CustomParts.Vertex_Before_NormalUpdated ? this.CustomParts.Vertex_Before_NormalUpdated : \"\")\r\n            .replace(\"#define CUSTOM_VERTEX_MAIN_END\", this.CustomParts.Vertex_MainEnd ? this.CustomParts.Vertex_MainEnd : \"\");\r\n\r\n        if (this.CustomParts.Vertex_After_WorldPosComputed) {\r\n            Effect.ShadersStore[name + \"VertexShader\"] = Effect.ShadersStore[name + \"VertexShader\"].replace(\r\n                \"#define CUSTOM_VERTEX_UPDATE_WORLDPOS\",\r\n                this.CustomParts.Vertex_After_WorldPosComputed\r\n            );\r\n        }\r\n\r\n        Effect.ShadersStore[name + \"PixelShader\"] = this.FragmentShader.replace(\r\n            \"#define CUSTOM_FRAGMENT_BEGIN\",\r\n            this.CustomParts.Fragment_Begin ? this.CustomParts.Fragment_Begin : \"\"\r\n        )\r\n            .replace(\"#define CUSTOM_FRAGMENT_MAIN_BEGIN\", this.CustomParts.Fragment_MainBegin ? this.CustomParts.Fragment_MainBegin : \"\")\r\n            .replace(\r\n                \"#define CUSTOM_FRAGMENT_DEFINITIONS\",\r\n                (this._customUniform ? this._customUniform.join(\"\\n\") : \"\") + (this.CustomParts.Fragment_Definitions ? this.CustomParts.Fragment_Definitions : \"\")\r\n            )\r\n            .replace(\"#define CUSTOM_FRAGMENT_UPDATE_DIFFUSE\", this.CustomParts.Fragment_Custom_Diffuse ? this.CustomParts.Fragment_Custom_Diffuse : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_UPDATE_ALPHA\", this.CustomParts.Fragment_Custom_Alpha ? this.CustomParts.Fragment_Custom_Alpha : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_BEFORE_LIGHTS\", this.CustomParts.Fragment_Before_Lights ? this.CustomParts.Fragment_Before_Lights : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR\", this.CustomParts.Fragment_Before_FragColor ? this.CustomParts.Fragment_Before_FragColor : \"\")\r\n            .replace(\"#define CUSTOM_FRAGMENT_MAIN_END\", this.CustomParts.Fragment_MainEnd ? this.CustomParts.Fragment_MainEnd : \"\");\r\n\r\n        if (this.CustomParts.Fragment_Before_Fog) {\r\n            Effect.ShadersStore[name + \"PixelShader\"] = Effect.ShadersStore[name + \"PixelShader\"].replace(\r\n                \"#define CUSTOM_FRAGMENT_BEFORE_FOG\",\r\n                this.CustomParts.Fragment_Before_Fog\r\n            );\r\n        }\r\n\r\n        this._isCreatedShader = true;\r\n        this._createdShaderName = name;\r\n\r\n        return name;\r\n    }\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this.CustomParts = new ShaderSpecialParts();\r\n        this.customShaderNameResolve = this.Builder;\r\n\r\n        this.FragmentShader = Effect.ShadersStore[\"defaultPixelShader\"];\r\n        this.VertexShader = Effect.ShadersStore[\"defaultVertexShader\"];\r\n    }\r\n\r\n    public AddUniform(name: string, kind: string, param: any): CustomMaterial {\r\n        if (!this._customUniform) {\r\n            this._customUniform = new Array();\r\n            this._newUniforms = new Array();\r\n            this._newSamplerInstances = {};\r\n            this._newUniformInstances = {};\r\n        }\r\n        if (param) {\r\n            if (kind.indexOf(\"sampler\") != -1) {\r\n                (<any>this._newSamplerInstances)[kind + \"-\" + name] = param;\r\n            } else {\r\n                (<any>this._newUniformInstances)[kind + \"-\" + name] = param;\r\n            }\r\n        }\r\n        this._customUniform.push(\"uniform \" + kind + \" \" + name + \";\");\r\n        this._newUniforms.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    public AddAttribute(name: string): CustomMaterial {\r\n        if (!this._customAttributes) {\r\n            this._customAttributes = [];\r\n        }\r\n\r\n        this._customAttributes.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Begin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Definitions(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_MainBegin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_MainEnd(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_Diffuse(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Diffuse = shaderPart.replace(\"result\", \"diffuseColor\");\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Custom_Alpha(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace(\"result\", \"alpha\");\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_Lights(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_Lights = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_Fog(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_Fog = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Fragment_Before_FragColor(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace(\"result\", \"color\");\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Begin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Definitions(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_MainBegin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Before_PositionUpdated(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace(\"result\", \"positionUpdated\");\r\n        return this;\r\n    }\r\n\r\n    public Vertex_Before_NormalUpdated(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace(\"result\", \"normalUpdated\");\r\n        return this;\r\n    }\r\n\r\n    public Vertex_After_WorldPosComputed(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    public Vertex_MainEnd(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.CustomMaterial\", CustomMaterial);\r\n"]}