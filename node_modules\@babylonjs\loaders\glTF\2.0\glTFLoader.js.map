{"version": 3, "file": "glTFLoader.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/2.0/glTFLoader.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,yCAA2B;AAC9C,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,6CAA+B;AACjF,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,KAAK,EAAE,sCAAwB;AACxC,OAAO,EAAE,MAAM,EAAE,0CAA4B;AAC7C,OAAO,EAAE,UAAU,EAAE,8CAAgC;AAIrD,OAAO,EAAE,yBAAyB,EAAE,mDAAqC;AACzE,OAAO,EAAE,cAAc,EAAE,qDAAuC;AAChE,OAAO,EAAE,IAAI,EAAE,sCAAwB;AACvC,OAAO,EAAE,QAAQ,EAAE,0CAA4B;AAC/C,OAAO,EAAE,QAAQ,EAAE,8CAAgC;AACnD,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAG7D,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAC1D,OAAO,EAAE,aAAa,EAAE,gDAAkC;AAC1D,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,0CAA4B;AAC3D,OAAO,EAAE,QAAQ,EAAE,2CAA6B;AAGhD,OAAO,EAAE,IAAI,EAAE,uCAAyB;AACxC,OAAO,EAAE,WAAW,EAAE,6CAA+B;AACrD,OAAO,EAAE,kBAAkB,EAAE,oDAAsC;AA0CnE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,8BAA8B,EAAE,4BAA4B,EAAE,MAAM,mBAAmB,CAAC;AAElI,OAAO,EAAE,uBAAuB,EAAE,eAAe,EAAE,aAAa,EAAE,0CAA4B;AAC9F,OAAO,EAAE,MAAM,EAAE,uCAAyB;AAE1C,OAAO,EAAE,YAAY,EAAE,gDAAkC;AAGzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AA2B1D,uCAAuC;AACvC,SAAS,SAAS,CAAC,GAAG,OAAc;IAChC,MAAM,QAAQ,GAAG,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;IAE9D,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;QAChC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACvB,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAEtB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC5C,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC;aACpC;iBAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACzC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACrC;iBAAM;gBACH,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;aACpB;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,SAAS;IAClB;;;;;;OAMG;IACI,MAAM,CAAC,GAAG,CAAI,OAAe,EAAE,KAA+B,EAAE,KAAyB;QAC5F,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC/C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,2BAA2B,KAAK,GAAG,CAAC,CAAC;SAClE;QAED,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAAM,CAAC,KAAoB;QACrC,IAAI,KAAK,EAAE;YACP,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/C,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;aAC9B;SACJ;IACL,CAAC;CACJ;AAWD;;GAEG;AACH,MAAM,OAAO,UAAU;IAiCnB;;;;OAIG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAY,EAAE,OAAqD;QAC/F,IAAI,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YACtC,MAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,kBAAkB,CAAC,CAAC;SACnE;QAED,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG;YACrC,OAAO,EAAE,OAAO;SACnB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,mBAAmB,CAAC,IAAY;QAC1C,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;YACzC,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;SACjD;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC7C;QAED,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,YAAY,MAAsB;QA3GlC,gBAAgB;QACA,sBAAiB,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE9D,gBAAgB;QACT,oBAAe,GAA6B,IAAI,CAAC;QAExD,cAAc;QACP,mBAAc,GAAY,EAAE,CAAC;QAEpC,gBAAgB;QACT,0BAAqB,GAAG,CAAC,CAAC;QAGhB,gBAAW,GAAG,IAAI,KAAK,EAAwB,CAAC;QACzD,cAAS,GAAG,KAAK,CAAC;QAClB,aAAQ,GAAqB,IAAI,CAAC;QAClC,cAAS,GAAqB,IAAI,CAAC;QACnC,mBAAc,GAAqB,IAAI,CAAC;QAExC,SAAI,GAA0B,IAAI,CAAC;QAEnC,qBAAgB,GAAmB,IAAI,CAAC;QACxC,gCAA2B,GAAqC,EAAE,CAAC;QAC1D,0BAAqB,GAAG,IAAI,KAAK,EAAc,CAAC;QAqF7D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAE3B,IAAI,CAAC,KAAyB,GAAG,IAAI,CAAC,CAAC,OAAO;QAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,aAAiC,GAAG,IAAI,CAAC,CAAC,OAAO;QACvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;QACtC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,eAAe,CAClB,WAAgB,EAChB,KAAY,EACZ,SAAmC,EACnC,IAAqB,EACrB,OAAe,EACf,UAAuD,EACvD,QAAQ,GAAG,EAAE;QAEb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAErB,IAAI,KAAK,GAA4B,IAAI,CAAC;YAE1C,IAAI,WAAW,EAAE;gBACb,MAAM,OAAO,GAA+B,EAAE,CAAC;gBAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;oBAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;wBACjC,IAAI,IAAI,CAAC,IAAI,EAAE;4BACX,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;yBACnC;qBACJ;iBACJ;gBAED,MAAM,KAAK,GAAG,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;gBACzE,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBACvB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC3B,IAAI,IAAI,KAAK,SAAS,EAAE;wBACpB,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,GAAG,CAAC,CAAC;qBACpD;oBAED,OAAO,IAAI,CAAC;gBAChB,CAAC,CAAC,CAAC;aACN;YAED,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE;gBAClD,OAAO;oBACH,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE;oBACzB,eAAe,EAAE,EAAE;oBACnB,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE;oBAC/B,eAAe,EAAE,IAAI,CAAC,mBAAmB,EAAE;oBAC3C,MAAM,EAAE,IAAI,CAAC,cAAc;oBAC3B,cAAc,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBACzC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE;iBACpC,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,KAAY,EAAE,IAAqB,EAAE,OAAe,EAAE,UAAuD,EAAE,QAAQ,GAAG,EAAE;QACzI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACrB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,UAAU,CAAI,OAAe,EAAE,QAAgB,EAAE,KAA8B,EAAE,UAAmB;QACxG,OAAO,OAAO,CAAC,OAAO,EAAE;aACnB,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,cAAc,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;YACtG,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAE1B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAExB,MAAM,yBAAyB,GAAG,GAAG,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7H,MAAM,4BAA4B,GAAG,GAAG,eAAe,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,eAAe,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAEnI,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,yBAAyB,CAAC,CAAC;YACjE,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,4BAA4B,CAAC,CAAC;YAEpE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAE3C,kEAAkE;YAClE,MAAM,8BAA8B,GAAG,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC;YACtF,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,IAAI,CAAC;YAEtD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;gBAChC,IAAI,KAAK,EAAE;oBACP,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC7E;qBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;oBACrF,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;oBAChF,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;iBACvE;aACJ;YAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACpF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;oBAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACzC,MAAM,OAAO,GAAG,aAAa,GAAG,CAAC,CAAC;oBAClC,MAAM,eAAe,GAAG,QAAQ,CAAC,gBAAgB,CAAC;oBAElD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC,CAAC;iBAC9F;aACJ;YAED,0CAA0C;YAC1C,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,8BAA8B,CAAC;YAEhF,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;aAChD;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBACtC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC,CAAC;aACvD;YAED,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBAC1C;gBAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAE9C,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAExB,OAAO,UAAU,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACjC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;gBAE/D,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE;oBACpB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBACjB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CACpC,GAAG,EAAE;4BACD,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;4BAElE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;4BAEjD,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;4BAC7D,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;4BAE1C,IAAI,CAAC,OAAO,EAAE,CAAC;wBACnB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;4BACN,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;4BACtD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;4BAEvC,IAAI,CAAC,OAAO,EAAE,CAAC;wBACnB,CAAC,CACJ,CAAC;qBACL;gBACL,CAAC,CAAC,CAAC;gBAEH,OAAO,MAAM,CAAC;YAClB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACtD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAEvC,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;YAED,MAAM,KAAK,CAAC;QAChB,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,SAAS,CAAC,IAAqB;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAa,CAAC;QAChC,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,IAAI,IAAI,CAAC,GAAG,EAAE;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACnC,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;gBAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAChC,IAAI,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;oBACpG,MAAM,CAAC,IAAI,CAAC,yBAAyB,YAAY,CAAC,UAAU,4CAA4C,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;iBACnI;gBAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;aACxB;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;aACvC;SACJ;IACL,CAAC;IAEO,UAAU;QACd,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACxC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACzC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACnC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEtC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,GAAgC,EAAE,CAAC;YACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBACjC,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;wBAC/B,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;qBACnC;iBACJ;aACJ;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACxC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;gBACjC,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,CAAC,MAAM,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;aACtF;SACJ;IACL,CAAC;IAEO,eAAe;QACnB,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,qBAAqB,EAAE;YACjD,MAAM,SAAS,GAAG,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvE,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,EAAE;gBACzB,MAAM,CAAC,IAAI,CAAC,sFAAsF,SAAS,CAAC,IAAI,QAAQ,IAAI,EAAE,CAAC,CAAC;aACnI;YAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;SACvE;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/F,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;IACrD,CAAC;IAEO,gBAAgB;QACpB,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC/B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;gBAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;gBACrG,IAAI,CAAC,SAAS,EAAE;oBACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,mBAAmB,CAAC,CAAC;iBACjE;aACJ;SACJ;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,IAAI,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAExC,MAAM,QAAQ,GAAU;YACpB,qBAAqB,EAAE,IAAI,CAAC,gBAAgB;YAC5C,KAAK,EAAE,CAAC,CAAC;SACZ,CAAC;QAEF,QAAQ,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;YACvC,KAAK,8BAA8B,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE;oBAC1C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC5B,UAAU,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;iBAC9D;gBACD,MAAM;aACT;YACD,KAAK,8BAA8B,CAAC,kBAAkB,CAAC,CAAC;gBACpD,IAAI,CAAC,aAAa,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBAC/C,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC;aAC5F;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,OAAe,EAAE,KAAa;QAChD,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxE,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAE/C,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;gBAC7B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,UAAU,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACjF,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;oBAC7D,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC/C,CAAC,CAAC,CACL,CAAC;aACL;SACJ;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC7C,MAAM,EAAE,CAAC;SACZ;QAED,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAEO,iBAAiB,CAAC,IAAW,EAAE,QAA6C;QAChF,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,uBAAuB,EAAE;gBACpD,QAAQ,CAAC,WAAW,CAAC,CAAC;aACzB;SACJ;IACL,CAAC;IAEO,cAAc;QAClB,MAAM,UAAU,GAAG,IAAI,KAAK,EAAY,CAAC;QAEzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/B,IAAI,KAAK,EAAE;YACP,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;oBACzC,MAAM,QAAQ,GAAI,WAAoB,CAAC,QAAQ,CAAC;oBAChD,IAAI,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;wBACjD,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAC7B;gBACL,CAAC,CAAC,CAAC;aACN;SACJ;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,UAAU;QACd,MAAM,MAAM,GAAG,IAAI,KAAK,EAAgB,CAAC;QAEzC,2CAA2C;QAC3C,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACtC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/B,IAAI,KAAK,EAAE;YACP,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;oBACzC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACN;SACJ;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,kBAAkB;QACtB,MAAM,cAAc,GAAG,IAAI,KAAK,EAAiB,CAAC;QAElD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/B,IAAI,KAAK,EAAE;YACP,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,KAAK,eAAe,EAAE;oBAC7F,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;iBACnD;gBACD,IAAI,IAAI,CAAC,4BAA4B,EAAE;oBACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;iBAC1D;aACJ;SACJ;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,aAAa;QACjB,MAAM,SAAS,GAAG,IAAI,KAAK,EAAY,CAAC;QAExC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;QAC/B,IAAI,KAAK,EAAE;YACP,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;iBAC9C;aACJ;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,mBAAmB;QACvB,MAAM,eAAe,GAAG,IAAI,KAAK,EAAkB,CAAC;QAEpD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QACzC,IAAI,UAAU,EAAE;YACZ,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAChC,IAAI,SAAS,CAAC,sBAAsB,EAAE;oBAClC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;iBAC1D;aACJ;SACJ;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;IAEO,gBAAgB;QACpB,QAAQ,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YACrC,KAAK,4BAA4B,CAAC,IAAI,CAAC,CAAC;gBACpC,aAAa;gBACb,MAAM;aACT;YACD,KAAK,4BAA4B,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM,sBAAsB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC1D,IAAI,sBAAsB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrC,sBAAsB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACzC;gBACD,MAAM;aACT;YACD,KAAK,4BAA4B,CAAC,GAAG,CAAC,CAAC;gBACnC,MAAM,sBAAsB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC1D,KAAK,MAAM,qBAAqB,IAAI,sBAAsB,EAAE;oBACxD,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBACrC;gBACD,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,CAAC,KAAK,CAAC,iCAAiC,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC;gBAClF,OAAO;aACV;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,OAAe,EAAE,IAAW,EAAE,SAAwD,GAAG,EAAE,GAAE,CAAC;QAC/G,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC9E,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,oCAAoC,CAAC,CAAC;SACnE;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAE9C,MAAM,QAAQ,GAAG,CAAC,oBAAmC,EAAE,EAAE;YACrD,UAAU,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;YAC7D,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,oBAAoB,CAAC,CAAC;YAEtD,IAAI,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE;gBAC1B,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnF,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,eAAe,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,aAAa,EAAE,EAAE;oBACvE,aAAa,CAAC,MAAM,GAAG,oBAAoB,CAAC;gBAChD,CAAC,CAAC,CACL,CAAC;aACL;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE;oBAC/B,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBACzF,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,aAAa,CAAC,UAAU,SAAS,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,gBAAgB,EAAE,EAAE;wBAC5E,gBAAgB,CAAC,MAAM,GAAG,oBAAoB,CAAC;oBACnD,CAAC,CAAC,CACL,CAAC;iBACL;aACJ;YAED,MAAM,CAAC,oBAAoB,CAAC,CAAC;QACjC,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;YAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;YAClD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACnE,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACtE,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;YAClD,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;gBACxB,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC;aAC9C;iBAAM;gBACH,IAAI,CAAC,4BAA4B,GAAG,aAAa,CAAC;aACrD;YACD,QAAQ,CAAC,aAAa,CAAC,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;YACxB,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;gBACxB,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;aACrF;iBAAM;gBACH,4GAA4G;gBAC5G,wGAAwG;gBACxG,uFAAuF;gBAEvF,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5E,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,oBAAoB,EAAE,EAAE;oBAC9E,MAAM,2BAA2B,GAAG,IAAI,CAAC,4BAA6B,CAAC;oBAEvE,uGAAuG;oBACvG,oBAAoB,CAAC,QAAQ,GAAG,SAAS,CAAC,2BAA2B,CAAC,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;oBAErH,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3E,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,eAAe,EAAE,EAAE;wBACxE,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;4BACzC,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;wBAC3C,CAAC,CAAC,CAAC;wBAEH,2EAA2E;wBAC3E,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,EAAE;4BACjC,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,EAAE;gCAC5B,sEAAsE;gCACtE,gFAAgF;gCAChF,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAO,CAAC;gCAC3G,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,EAAE;oCACjC,oBAAoB,CAAC,MAAM,GAAG,2BAA2B,CAAC,MAAM,CAAC;iCACpE;qCAAM;oCACH,oBAAoB,CAAC,MAAM,GAAG,UAAU,CAAC,qBAAsB,CAAC;iCACnE;6BACJ;iCAAM;gCACH,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;6BACvD;4BAED,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,2BAA2B,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC,CAAC;wBAClI,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CACL,CAAC;gBACN,CAAC,CAAC,CACL,CAAC;aACL;SACJ;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;gBACzC,IAAK,WAAoB,CAAC,QAAQ,IAAK,WAAoB,CAAC,QAAS,CAAC,2BAA2B,EAAE;oBAC/F,oFAAoF;oBACpF,WAAW,CAAC,mBAAmB,EAAE,CAAC;iBACrC;qBAAM;oBACH,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;iBACzC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,qBAAsB,CAAC;QACvC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,IAAW,EAAE,IAAW,EAAE,MAAqD;QACnH,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,0BAA0B,CAAC,CAAC;SACzD;QAED,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,SAAS,EAAE;YAClC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAE9C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;QAE9C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,uBAAuB,CAAC,GAAG,OAAO,eAAe,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;gBAClH,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC;gBACzC,IAAI,CAAC,uBAAuB,GAAG,CAAC,WAAW,CAAC,CAAC;YACjD,CAAC,CAAC,CACL,CAAC;SACL;aAAM;YACH,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACzE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YACnE,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;YAClD,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;YAClC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAChC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,uBAAuB,CAAC,GAAG,OAAO,eAAe,SAAS,CAAC,KAAK,EAAE,EAAE,GAAG,IAAI,aAAa,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;oBACnJ,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAsB,CAAC;oBACjD,IAAI,CAAC,uBAAwB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpD,CAAC,CAAC,CACL,CAAC;aACL;SACJ;QAED,MAAM,CAAC,IAAI,CAAC,qBAAsB,CAAC,CAAC;QAEpC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,OAAO,IAAI,CAAC,qBAAsB,CAAC;QACvC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;OASG;IACI,uBAAuB,CAC1B,OAAe,EACf,IAAY,EACZ,IAAW,EACX,IAAW,EACX,SAAyB,EACzB,MAA2C;QAE3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAC9G,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC;QAE3B,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAEjJ,IAAI,mBAAiC,CAAC;QACtC,IAAI,OAAqB,CAAC;QAE1B,IAAI,cAAc,IAAI,SAAS,CAAC,aAAa,EAAE;YAC3C,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACnE,mBAAmB,GAAG,SAAS,CAAC,aAAa,CAAC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAkB,CAAC;YACtG,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;YAClD,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC;SAC7C;aAAM;YACH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAE3C,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;YACnE,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACvD,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;YACpD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;YAClD,WAAW,CAAC,+BAA+B,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;YAErK,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YACtE,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,EAAE;gBAChF,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC3F,IAAI,IAAI,CAAC,SAAS,EAAE;wBAChB,OAAO;qBACV;oBAED,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;oBACnE,eAAe,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;oBACzC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;oBACxD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBACtD,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CACL,CAAC;YAEF,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;YACzE,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,EAAE;gBACjC,IAAI,eAAe,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,CAAC;gBACxE,IAAI,CAAC,eAAe,EAAE;oBAClB,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,uBAAuB,EAAE,eAAe,CAAC,CAAC;oBACxF,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;oBACzE,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC;iBACvE;gBACD,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;aAC1C;iBAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;gBACnC,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAChG,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,kBAAkB,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,eAAe,EAAE,EAAE;oBAChH,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC;gBAC3C,CAAC,CAAC,CACL,CAAC;aACL;YAED,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEhC,IAAI,cAAc,EAAE;gBAChB,SAAS,CAAC,aAAa,GAAG;oBACtB,iBAAiB,EAAE,WAAW;oBAC9B,OAAO,EAAE,OAAO;iBACnB,CAAC;aACL;YAED,mBAAmB,GAAG,WAAW,CAAC;SACrC;QAED,UAAU,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QACzE,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAE5B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,mBAAmB,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB,CAAC,OAAe,EAAE,SAAyB,EAAE,WAAiB;QACtF,MAAM,gBAAgB,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;QAC9F,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,UAAU,EAAE;YACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,0BAA0B,CAAC,CAAC;SACzD;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE3E,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,EAAE;YAChC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC;SAClC;aAAM;YACH,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;YAC9F,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,yBAAyB,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACnF,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC,CAAC,CACL,CAAC;SACL;QAED,MAAM,aAAa,GAAG,CAAC,SAAiB,EAAE,IAAY,EAAE,QAAwC,EAAE,EAAE;YAChG,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE;gBACpC,OAAO;aACV;YAED,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC;YACtD,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC7C,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrC;YAED,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,eAAe,SAAS,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YAClH,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,wBAAwB,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,EAAE;gBACvG,IAAI,mBAAmB,CAAC,OAAO,EAAE,KAAK,YAAY,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE;oBAC/H,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE;wBAC9B,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAI,QAAQ,CAAC,GAAgC,CAAC,CAAC;wBAChG,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAI,QAAQ,CAAC,GAAgC,CAAC,CAAC;wBAChG,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,aAAa,2CAAgC,EAAE;4BAC/E,IAAI,OAAO,GAAG,CAAC,CAAC;4BAChB,QAAQ,QAAQ,CAAC,aAAa,EAAE;gCAC5B;oCACI,OAAO,GAAG,KAAK,CAAC;oCAChB,MAAM;gCACV;oCACI,OAAO,GAAG,KAAK,CAAC;oCAChB,MAAM;gCACV;oCACI,OAAO,GAAG,OAAO,CAAC;oCAClB,MAAM;gCACV;oCACI,OAAO,GAAG,OAAO,CAAC;oCAClB,MAAM;6BACb;4BACD,MAAM,cAAc,GAAG,CAAC,GAAG,OAAO,CAAC;4BACnC,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;4BACjC,GAAG,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;yBACpC;wBACD,eAAe,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;wBAC3D,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC;qBACtD;iBACJ;gBACD,eAAe,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC3E,CAAC,CAAC,CACL,CAAC;YAEF,IAAI,IAAI,IAAI,YAAY,CAAC,wBAAwB,EAAE;gBAC/C,WAAW,CAAC,kBAAkB,GAAG,CAAC,CAAC;aACtC;YAED,IAAI,QAAQ,EAAE;gBACV,QAAQ,CAAC,QAAQ,CAAC,CAAC;aACtB;QACL,CAAC,CAAC;QAEF,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;QACrD,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;QACjD,aAAa,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;QACnD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;QAClD,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAC5D,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAC7D,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,wBAAwB,CAAC,CAAC;QACjE,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,wBAAwB,CAAC,CAAC;QAClE,aAAa,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,EAAE;YAC1D,IAAI,QAAQ,CAAC,IAAI,mCAAsB,EAAE;gBACrC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;aACrC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,OAAO,eAAe,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,OAAe,EAAE,IAAW,EAAE,IAAW,EAAE,SAAyB,EAAE,WAAiB;QAC/G,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACpB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,SAAS,EAAE;YACpC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;SACpD;aAAM,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,qDAAqD,CAAC,CAAC;SACpF;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC;QAEjE,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,WAAW,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5E,WAAW,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACvE,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAElD,WAAW,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAEvD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAAC;YACtE,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAChG,oEAAoE;SACvE;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAe,EAAE,SAAyB,EAAE,WAAiB,EAAE,eAAyB;QACnH,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACpB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAmB,CAAC;QAC3D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;YAChE,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/D,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,GAAG,OAAO,YAAY,KAAK,EAAE,EAAE,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC;SACrJ;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,kBAAkB,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,+BAA+B,CAAC,OAAe,EAAE,eAAyB,EAAE,UAAsC,EAAE,kBAA+B;QACvJ,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,MAAM,aAAa,GAAG,CAAC,SAAiB,EAAE,IAAY,EAAE,OAAwE,EAAE,EAAE;YAChI,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE;gBACpC,OAAO;aACV;YAED,MAAM,mBAAmB,GAAG,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,mBAAmB,EAAE;gBACtB,OAAO;aACV;YAED,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,IAAI,SAAS,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YACvG,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,uBAAuB,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACjF,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC,CAAC,CACL,CAAC;QACN,CAAC,CAAC;QAEF,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,IAAI,EAAE,EAAE;YAC/E,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChD,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACtD,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YAC3C,CAAC,CAAC,CAAC;YAEH,kBAAkB,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC,mBAAmB,EAAE,IAAI,EAAE,EAAE;YAC3E,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzD,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,kBAAkB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC,mBAAmB,EAAE,IAAI,EAAE,EAAE;YAC7E,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBAChE,yDAAyD;gBACzD,4CAA4C;gBAC5C,sDAAsD;gBACtD,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACvB,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;oBAC9C,SAAS,EAAE,CAAC;iBACf;YACL,CAAC,CAAC,CAAC;YACH,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,IAAW,EAAE,WAA0B;QACjE,mCAAmC;QACnC,4GAA4G;QAC5G,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE;YACxB,OAAO;SACV;QAED,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,IAAI,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAClD;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClD;YACD,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC3C;SACJ;QAED,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAChC,WAAW,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QAC1C,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,CAAC;IAEO,cAAc,CAAC,OAAe,EAAE,IAAW,EAAE,IAAW,EAAE,MAA2C;QACzG,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC5E,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;SAC7B;QAED,MAAM,UAAU,GAAG,WAAW,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9F,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAElD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,IAAI,CAAC,qCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB,EAAE,EAAE;YACvG,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,uBAAuB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG;YACT,eAAe,EAAE,eAAe;YAChC,OAAO,EAAE,OAAO;SACnB,CAAC;QAEF,MAAM,CAAC,eAAe,CAAC,CAAC;QAExB,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,UAAU,CAAC,OAAe,EAAE,IAAW,EAAE,eAAyB;QACtE,IAAI,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,6BAA6B,EAAE;YAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,OAAO,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9E,IAAI,QAAQ,EAAE;gBACV,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;oBAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;iBAClC;qBAAM;oBACH,MAAM,QAAQ,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAW,EAAE;wBAC7C,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;4BAC3B,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;gCAChB,OAAO,IAAI,CAAC;6BACf;yBACJ;wBAED,OAAO,KAAK,CAAC;oBACjB,CAAC,CAAC;oBAEF,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3F,IAAI,YAAY,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;wBAChE,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,0FAA0F,CAAC,CAAC;wBAClH,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC;qBAClC;iBACJ;aACJ;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,8BAA8B,CAAC,CAAC;aACzD;SACJ;QAED,MAAM,YAAY,GAA8B,EAAE,CAAC;QACnD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;YAC7B,MAAM,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,WAAW,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;SAC7D;IACL,CAAC;IAEO,qBAAqB,CAAC,OAAe,EAAE,MAAqB;QAChE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAsC,EAAE,CAAC;QACpD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,MAAM,IAAI,GAAG,IAAI,KAAK,EAAS,CAAC;YAChC,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACnB,IAAI,GAAG,IAAI,CAAC,MAAO,CAAC;aACvB;YACD,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;SACvB;QAED,IAAI,QAAQ,GAAoB,IAAI,CAAC;QACrC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAI,EAAE,CAAC,EAAE;YACnB,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAClB,OAAO,QAAQ,CAAC;aACnB;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;oBACtC,OAAO,QAAQ,CAAC;iBACnB;aACJ;YAED,QAAQ,GAAG,IAAI,CAAC;SACnB;IACL,CAAC;IAEO,SAAS,CAAC,IAAW,EAAE,IAAW,EAAE,eAAyB,EAAE,YAAuC;QAC1G,IAAI,WAAW,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,IAAI,WAAW,EAAE;YACb,OAAO,WAAW,CAAC;SACtB;QAED,IAAI,iBAAiB,GAAmB,IAAI,CAAC;QAC7C,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC9B,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;gBACzC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;aACxF;iBAAM,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;gBACpC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,+CAA+C,CAAC,CAAC;aACpF;SACJ;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAChJ,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC;QAEvC,2EAA2E;QAC3E,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,EAAE;YACjC,uEAAuE;YACvE,0FAA0F;YAC1F,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,qBAAsB,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,qCAAqC,CAAC,OAAe,EAAE,IAAW;QACtE,IAAI,IAAI,CAAC,mBAAmB,IAAI,SAAS,EAAE;YACvC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAChC;QAED,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,sBAAsB,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACjH,OAAO,IAAI,CAAC,uBAAuB,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAEO,mBAAmB,CAAC,eAAyB,EAAE,uBAA+C;QAClG,KAAK,MAAM,WAAW,IAAI,eAAe,CAAC,KAAK,EAAE;YAC7C,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,SAAS,GAAG,WAAW,CAAC,MAAO,CAAC;YACtC,IAAI,uBAAuB,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;gBAC7C,MAAM,CAAC,cAAc,CAAC,uBAAuB,EAAE,SAAS,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;gBAC3E,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;aACtC;YAED,MAAM,iBAAiB,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;YAClD,IAAI,iBAAiB,EAAE;gBACnB,UAAU,CAAC,aAAa,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,EAAE,UAAU,CAAC,CAAC;aAC1F;YAED,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACnD,WAAW,CAAC,uBAAuB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACzD;IACL,CAAC;IAEO,cAAc,CAAC,IAAW;QAC9B,OAAO,IAAI,CAAC,MAAM;YACd,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;YAC/B,CAAC,CAAC,MAAM,CAAC,OAAO,CACV,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAC1D,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,EAC3E,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAC1E,CAAC;IACZ,CAAC;IAED;;;;;;OAMG;IACI,eAAe,CAAC,OAAe,EAAE,MAAe,EAAE,SAA0C,GAAG,EAAE,GAAE,CAAC;QACvG,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAClF,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAEhD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,MAAM,aAAa,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACxH,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACtD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAClD,aAAa,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACzC,MAAM,CAAC,cAAc,GAAG,aAAa,CAAC;QAEtC,aAAa,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAEpD,QAAQ,MAAM,CAAC,IAAI,EAAE;YACjB,+CAA2B,CAAC,CAAC;gBACzB,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;gBACvC,IAAI,CAAC,WAAW,EAAE;oBACd,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,6CAA6C,CAAC,CAAC;iBAC5E;gBAED,aAAa,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC;gBACrC,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC;gBACvC,aAAa,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC;gBAC3C,MAAM;aACT;YACD,iDAA4B,CAAC,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;oBACtB,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,8CAA8C,CAAC,CAAC;iBAC7E;gBAED,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,mBAAmB,CAAC;gBAChD,aAAa,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACpD,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACpD,aAAa,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBACtD,aAAa,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBAClD,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;gBAC/C,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC;gBAC9C,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,0BAA0B,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;aACvE;SACJ;QAED,UAAU,CAAC,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACrE,MAAM,CAAC,aAAa,CAAC,CAAC;QAEtB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,OAAO,aAAa,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAiB,CAAC;QAE5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACpD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YACpC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,kBAAkB,CAAC,eAAe,SAAS,CAAC,KAAK,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;gBACzF,6EAA6E;gBAC7E,IAAI,cAAc,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;oBAChD,cAAc,CAAC,OAAO,EAAE,CAAC;iBAC5B;YACL,CAAC,CAAC,CACL,CAAC;SACL;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,OAAe,EAAE,SAAqB;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACvE,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC;SAClB;QAED,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,MAAM,qBAAqB,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,YAAY,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACtH,qBAAqB,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9D,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAClD,SAAS,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QAEzD,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACrC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAErC,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE;YACtC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,0BAA0B,CAAC,GAAG,OAAO,aAAa,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,EAAE;gBACrI,aAAa,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,IAAI,EAAE,CAAC;gBAC1D,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChD,qBAAqB,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAChF,CAAC,CAAC,CACL,CAAC;SACL;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,OAAO,qBAAqB,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAC7B,OAAe,EACf,gBAAwB,EACxB,SAAqB,EACrB,OAA0B,EAC1B,MAA6E;QAE7E,MAAM,OAAO,GAAG,IAAI,CAAC,oCAAoC,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACjH,IAAI,OAAO,EAAE;YACT,OAAO,OAAO,CAAC;SAClB;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,SAAS,EAAE;YAClC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAElG,oDAAoD;QACpD,IACI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,uDAAuC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC;YAC5F,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,uDAAuC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,EACnG;YACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,IAAI,UAAwC,CAAC;QAC7C,QAAQ,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE;YACzB,+DAA2C,CAAC,CAAC;gBACzC,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;gBAC3C,MAAM;aACT;YACD,yDAAwC,CAAC,CAAC;gBACtC,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;gBACxC,MAAM;aACT;YACD,mDAAqC,CAAC,CAAC;gBACnC,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;gBACrC,MAAM;aACT;YACD,uDAAuC,CAAC,CAAC;gBACrC,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC;gBACvC,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,gCAAgC,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;aACrF;SACJ;QAED,MAAM,UAAU,GAAyB;YACrC,MAAM,EAAE,UAAU;YAClB,UAAU,EAAE,UAAU;SACzB,CAAC;QAEF,OAAO,IAAI,CAAC,wCAAwC,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAC5H,CAAC;IAED;;;;;;;;;;OAUG;IACI,wCAAwC,CAC3C,OAAe,EACf,gBAAwB,EACxB,SAAqB,EACrB,OAA0B,EAC1B,UAAgC,EAChC,MAA6E;QAE7E,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAClC,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;QAEvB,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,UAAU,EAAE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC,0BAA0B,CAAC,GAAG,gBAAgB,aAAa,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC7G,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,wDAAwD;YACxD,+DAA+D;YAC/D,qEAAqE;YACrE,yCAAyC;YACzC,iBAAiB;YACjB,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,UAAU,EAAE;gBAC1C,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACrD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,MAAM,IAAI,GAAG,IAAI,KAAK,CAAgB,KAAK,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,YAAY,GAAG,CAAC,CAAC;gBAErB,QAAQ,IAAI,CAAC,aAAa,EAAE;oBACxB,oDAAuC,CAAC,CAAC;wBACrC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;4BAC5E,YAAY,IAAI,MAAM,CAAC;4BAEvB,IAAI,CAAC,KAAK,CAAC,GAAG;gCACV,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG;gCACzB,KAAK,EAAE,KAAK;gCACZ,aAAa,EAAE,yBAAyB,CAAC,IAAI;6BAChD,CAAC;yBACL;wBACD,MAAM;qBACT;oBACD,kEAA8C,CAAC,CAAC;wBAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAC/C,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;4BACrF,YAAY,IAAI,MAAM,CAAC;4BACvB,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;4BAC5E,YAAY,IAAI,MAAM,CAAC;4BACvB,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;4BACtF,YAAY,IAAI,MAAM,CAAC;4BAEvB,IAAI,CAAC,KAAK,CAAC,GAAG;gCACV,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG;gCACzB,SAAS,EAAE,SAAS;gCACpB,KAAK,EAAE,KAAK;gCACZ,UAAU,EAAE,UAAU;6BACzB,CAAC;yBACL;wBACD,MAAM;qBACT;oBACD,wDAAyC,CAAC,CAAC;wBACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;4BAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;4BAC5E,YAAY,IAAI,MAAM,CAAC;4BAEvB,IAAI,CAAC,KAAK,CAAC,GAAG;gCACV,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG;gCACzB,KAAK,EAAE,KAAK;6BACf,CAAC;yBACL;wBACD,MAAM;qBACT;iBACJ;gBAED,IAAI,YAAY,GAAG,CAAC,EAAE;oBAClB,MAAM,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,IAAI,YAAY,SAAS,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,KAAK,IAAI,aAAa,EAAE,CAAC;oBAC3G,QAAQ,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,EAAE;wBACjG,EAAE,aAAa,CAAC;wBAChB,MAAM,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;oBAChD,CAAC,CAAC,CAAC;iBACN;aACJ;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B,CAAC,OAAe,EAAE,OAA0B;QAC1E,IAAI,OAAO,CAAC,KAAK,EAAE;YACf,OAAO,OAAO,CAAC,KAAK,CAAC;SACxB;QAED,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,uDAAwC,CAAC;QACpF,QAAQ,aAAa,EAAE;YACnB,qDAAwC;YACxC,yDAA0C;YAC1C,kEAA8C,CAAC,CAAC;gBAC5C,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,kCAAkC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;aACzF;SACJ;QAED,MAAM,aAAa,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7F,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAChG,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC;YACxB,IAAI,CAAC,uBAAuB,CAAC,cAAc,aAAa,CAAC,KAAK,EAAE,EAAE,aAAa,CAAC;YAChF,IAAI,CAAC,uBAAuB,CAAC,cAAc,cAAc,CAAC,KAAK,EAAE,EAAE,cAAc,CAAC;SACrF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE;YAChC,OAAO;gBACH,KAAK,EAAE,SAAS;gBAChB,aAAa,EAAE,aAAa;gBAC5B,MAAM,EAAE,UAAU;aACrB,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC,KAAK,CAAC;IACzB,CAAC;IAED;;;;;;;OAOG;IACI,eAAe,CAAC,OAAe,EAAE,MAAe,EAAE,UAAkB,EAAE,UAAkB;QAC3F,MAAM,gBAAgB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAClG,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACf,IAAI,MAAM,CAAC,GAAG,EAAE;gBACZ,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,OAAO,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;aAC1E;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;oBACZ,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,iEAAiE,CAAC,CAAC;iBAChG;gBAED,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;aAC5D;SACJ;QAED,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC9B,IAAI;gBACA,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,GAAG,UAAU,EAAE,UAAU,CAAC,CAAC;aAChF;YAAC,OAAO,CAAC,EAAE;gBACR,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;aAC/C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACI,mBAAmB,CAAC,OAAe,EAAE,UAAuB;QAC/D,MAAM,gBAAgB,GAAG,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAClF,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,UAAU,CAAC,KAAK,EAAE;YAClB,OAAO,UAAU,CAAC,KAAK,CAAC;SAC3B;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;QACzF,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,UAAU,CAAC,UAAU,IAAI,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QAE/H,OAAO,UAAU,CAAC,KAAK,CAAC;IAC5B,CAAC;IAEO,kBAAkB,CAAC,OAAe,EAAE,QAAmB,EAAE,WAAkC;QAC/F,IAAI,QAAQ,CAAC,KAAK,EAAE;YAChB,OAAO,QAAQ,CAAC,KAAK,CAAC;SACzB;QAED,MAAM,aAAa,GAAG,UAAU,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3E,MAAM,UAAU,GAAG,aAAa,GAAG,YAAY,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1F,MAAM,MAAM,GAAG,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC;QAE9C,IAAI,QAAQ,CAAC,UAAU,IAAI,SAAS,EAAE;YAClC,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;SAC7D;aAAM;YACH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACvG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpG,IAAI,QAAQ,CAAC,aAAa,2CAAgC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,EAAE;oBACpJ,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;iBACxG;qBAAM;oBACH,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;oBAC3C,YAAY,CAAC,OAAO,CAChB,IAAI,EACJ,QAAQ,CAAC,UAAU,IAAI,CAAC,EACxB,UAAU,CAAC,UAAU,IAAI,UAAU,EACnC,aAAa,EACb,QAAQ,CAAC,aAAa,EACtB,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,UAAU,IAAI,KAAK,EAC5B,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;wBACb,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;oBAC9B,CAAC,CACJ,CAAC;oBACF,OAAO,UAAU,CAAC;iBACrB;YACL,CAAC,CAAC,CAAC;SACN;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC/B,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC1C,MAAM,UAAU,GAAG,IAAsB,CAAC;gBAC1C,MAAM,iBAAiB,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,4BAA4B,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACnI,MAAM,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,2BAA2B,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAChI,OAAO,OAAO,CAAC,GAAG,CAAC;oBACf,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,iBAAiB,CAAC,KAAK,EAAE,EAAE,iBAAiB,CAAC;oBACtF,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,gBAAgB,CAAC,KAAK,EAAE,EAAE,gBAAgB,CAAC;iBACvF,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE,EAAE;oBAClC,MAAM,OAAO,GAAG,UAAU,CAAC,cAAc,CACrC,GAAG,OAAO,iBAAiB,EAC3B,MAAM,CAAC,OAAO,CAAC,aAAa,EAC5B,WAAW,EACX,MAAM,CAAC,OAAO,CAAC,UAAU,EACzB,MAAM,CAAC,KAAK,CACC,CAAC;oBAElB,MAAM,YAAY,GAAG,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;oBAClD,IAAI,MAAsB,CAAC;oBAE3B,IAAI,QAAQ,CAAC,aAAa,2CAAgC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;wBAChF,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,OAAO,gBAAgB,EAAE,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;qBAC9I;yBAAM;wBACH,MAAM,UAAU,GAAG,UAAU,CAAC,cAAc,CAAC,GAAG,OAAO,gBAAgB,EAAE,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;wBACrJ,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC;wBACvC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;4BACjJ,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;wBAC1B,CAAC,CAAC,CAAC;qBACN;oBAED,IAAI,WAAW,GAAG,CAAC,CAAC;oBACpB,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;wBACtE,IAAI,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,aAAa,CAAC;wBACtD,KAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,aAAa,EAAE,cAAc,EAAE,EAAE;4BAC3E,UAAU,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;yBACnD;qBACJ;oBAED,OAAO,UAAU,CAAC;gBACtB,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,OAAe,EAAE,QAAmB;QAC/D,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,YAAY,CAA0B,CAAC;IAC7F,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,OAAe,EAAE,QAAmB;QACjE,IAAI,QAAQ,CAAC,IAAI,uCAAwB,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,wBAAwB,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;SACtE;QAED,IACI,QAAQ,CAAC,aAAa,mDAAwC;YAC9D,QAAQ,CAAC,aAAa,oDAAyC;YAC/D,QAAQ,CAAC,aAAa,kDAAuC,EAC/D;YACE,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,iCAAiC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;SACxF;QAED,IAAI,QAAQ,CAAC,KAAK,EAAE;YAChB,OAAO,QAAQ,CAAC,KAA8B,CAAC;SAClD;QAED,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,MAAM,WAAW,GAAG,UAAU,CAAC,yBAAyB,CAAC,GAAG,OAAO,gBAAgB,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC7G,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;SAC5E;aAAM;YACH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACvG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpG,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YACjH,CAAC,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC,KAA8B,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,0BAA0B,CAAC,UAAuB;QACrD,IAAI,UAAU,CAAC,cAAc,EAAE;YAC3B,OAAO,UAAU,CAAC,cAAc,CAAC;SACpC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/G,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC,cAAc,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,OAAe,EAAE,QAAmB,EAAE,IAAY;;QAC9E,IAAI,MAAA,QAAQ,CAAC,oBAAoB,0CAAG,IAAI,CAAC,EAAE;YACvC,OAAO,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;YAChC,QAAQ,CAAC,oBAAoB,GAAG,EAAE,CAAC;SACtC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,QAAQ,CAAC,MAAM,EAAE;YACjB,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChG,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SACN;QACD,6GAA6G;QAC7G,kGAAkG;aAC7F,IAAI,IAAI,KAAK,YAAY,CAAC,mBAAmB,IAAI,IAAI,KAAK,YAAY,CAAC,wBAAwB,EAAE;YAClG,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAChG,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;SACN;aAAM;YACH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;YACvG,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,EAAE;gBACrG,MAAM,IAAI,GAAG,UAAU,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAClE,OAAO,IAAI,YAAY,CACnB,MAAM,EACN,aAAa,EACb,IAAI,EACJ,KAAK,EACL,KAAK,EACL,UAAU,CAAC,UAAU,EACrB,KAAK,EACL,QAAQ,CAAC,UAAU,EACnB,IAAI,EACJ,QAAQ,CAAC,aAAa,EACtB,QAAQ,CAAC,UAAU,EACnB,IAAI,EACJ,CAAC,EACD,IAAI,CACP,CAAC;YACN,CAAC,CAAC,CAAC;SACN;QAED,OAAO,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAEO,6CAA6C,CAAC,OAAe,EAAE,UAAyC,EAAE,eAAyB;QACvI,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,UAAU,EAAE;YACZ,IAAI,UAAU,CAAC,eAAe,EAAE;gBAC5B,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;gBAC3E,eAAe,CAAC,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;aACzD;iBAAM;gBACH,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;aAChD;YAED,eAAe,CAAC,QAAQ,GAAG,UAAU,CAAC,cAAc,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC;YAClG,eAAe,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC;YAErG,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC7B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,OAAO,mBAAmB,EAAE,UAAU,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,EAAE;oBAC9F,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,eAAe,CAAC;oBACtD,eAAe,CAAC,aAAa,GAAG,OAAO,CAAC;gBAC5C,CAAC,CAAC,CACL,CAAC;aACL;YAED,IAAI,UAAU,CAAC,wBAAwB,EAAE;gBACrC,UAAU,CAAC,wBAAwB,CAAC,YAAY,GAAG,IAAI,CAAC;gBACxD,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,OAAO,2BAA2B,EAAE,UAAU,CAAC,wBAAwB,EAAE,CAAC,OAAO,EAAE,EAAE;oBAC9G,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,uBAAuB,CAAC;oBAC9D,eAAe,CAAC,eAAe,GAAG,OAAO,CAAC;gBAC9C,CAAC,CAAC,CACL,CAAC;gBAEF,eAAe,CAAC,oCAAoC,GAAG,IAAI,CAAC;gBAC5D,eAAe,CAAC,oCAAoC,GAAG,IAAI,CAAC;gBAC5D,eAAe,CAAC,oCAAoC,GAAG,KAAK,CAAC;aAChE;SACJ;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,kBAAkB,CACrB,OAAe,EACf,QAAmB,EACnB,WAA2B,EAC3B,eAAuB,EACvB,SAA8C,GAAG,EAAE,GAAE,CAAC;QAEtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QACpH,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;QACtC,IAAI,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,QAAQ,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YAElD,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;YAEhF,WAAW,GAAG;gBACV,eAAe,EAAE,eAAe;gBAChC,aAAa,EAAE,EAAE;gBACjB,OAAO,EAAE,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC;aAChF,CAAC;YAEF,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,WAAW,CAAC;YAE9C,UAAU,CAAC,kBAAkB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACxD,IAAI,CAAC,OAAO,CAAC,0BAA0B,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAEzE,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;QAED,IAAI,WAAW,EAAE;YACb,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE5C,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACzC,MAAM,KAAK,GAAG,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAC7D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;oBACd,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBAC9C;YACL,CAAC,CAAC,CAAC;SACN;QAED,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAEpC,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;YACjC,OAAO,WAAW,CAAC,eAAe,CAAC;QACvC,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,sBAAsB,CAAC,IAAY,EAAE,eAAuB;QAChE,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,MAAM,eAAe,GAAG,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClE,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAClD,uNAAuN;QACvN,eAAe,CAAC,QAAQ,GAAG,eAAe,CAAC;QAC3C,eAAe,CAAC,0BAA0B,GAAG,IAAI,CAAC;QAClD,eAAe,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC;QAC5E,eAAe,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC;QAC5E,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,kBAAkB,CAAC;QAClE,eAAe,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC7B,eAAe,CAAC,SAAS,GAAG,CAAC,CAAC;QAC9B,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAuB;QAC/E,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC5F,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,WAAW,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC1D,MAAM,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;QAE3E,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,MAAM,gBAAgB,GAAG,IAAI,CAAC,sCAAsC,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QACzG,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;QAExF,IAAI,QAAQ,CAAC,oBAAoB,EAAE;YAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,6CAA6C,CAAC,GAAG,OAAO,uBAAuB,EAAE,QAAQ,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC,CAAC;SACxJ;QAED,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAErE,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACI,+BAA+B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAClG,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,eAAe,CAAC,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1H,IAAI,QAAQ,CAAC,WAAW,EAAE;YACtB,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;YACxC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAC3C;QAED,IAAI,QAAQ,CAAC,aAAa,EAAE;YACxB,QAAQ,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3C,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,OAAO,gBAAgB,EAAE,QAAQ,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,EAAE;gBACtF,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,WAAW,CAAC;gBAClD,eAAe,CAAC,WAAW,GAAG,OAAO,CAAC;YAC1C,CAAC,CAAC,CACL,CAAC;YAEF,eAAe,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC5E,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC3E,IAAI,QAAQ,CAAC,aAAa,CAAC,KAAK,IAAI,SAAS,IAAI,eAAe,CAAC,WAAW,EAAE;gBAC1E,eAAe,CAAC,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;aACpE;YAED,eAAe,CAAC,yBAAyB,GAAG,IAAI,CAAC;SACpD;QAED,IAAI,QAAQ,CAAC,gBAAgB,EAAE;YAC3B,QAAQ,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9C,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,OAAO,mBAAmB,EAAE,QAAQ,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC5F,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,cAAc,CAAC;gBACrD,eAAe,CAAC,cAAc,GAAG,OAAO,CAAC;YAC7C,CAAC,CAAC,CACL,CAAC;YAEF,eAAe,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAC7C,IAAI,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,IAAI,SAAS,EAAE;gBACjD,eAAe,CAAC,sBAAsB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;aAC/E;SACJ;QAED,IAAI,QAAQ,CAAC,eAAe,EAAE;YAC1B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,oBAAoB,CAAC,GAAG,OAAO,kBAAkB,EAAE,QAAQ,CAAC,eAAe,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC1F,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,aAAa,CAAC;gBACpD,eAAe,CAAC,eAAe,GAAG,OAAO,CAAC;YAC9C,CAAC,CAAC,CACL,CAAC;SACL;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,2CAA4B,CAAC;QACjE,QAAQ,SAAS,EAAE;YACf,4CAA6B,CAAC,CAAC;gBAC3B,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,kBAAkB,CAAC;gBAClE,MAAM;aACT;YACD,wCAA2B,CAAC,CAAC;gBACzB,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,qBAAqB,CAAC;gBACrE,eAAe,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAC7F,IAAI,eAAe,CAAC,aAAa,EAAE;oBAC/B,eAAe,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACjD;gBACD,MAAM;aACT;YACD,0CAA4B,CAAC,CAAC;gBAC1B,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,sBAAsB,CAAC;gBACtE,IAAI,eAAe,CAAC,aAAa,EAAE;oBAC/B,eAAe,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;oBAC9C,eAAe,CAAC,yBAAyB,GAAG,IAAI,CAAC;iBACpD;gBACD,MAAM;aACT;YACD,OAAO,CAAC,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,8BAA8B,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;aAClF;SACJ;IACL,CAAC;IAED;;;;;;OAMG;IACI,oBAAoB,CAAC,OAAe,EAAE,WAAyB,EAAE,SAAgD,GAAG,EAAE,GAAE,CAAC;QAC5H,MAAM,gBAAgB,GAAG,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QAC5F,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,EAAE,CAAC,CAAC;QAE3B,IAAI,WAAW,CAAC,QAAS,IAAI,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,6BAA6B,WAAW,CAAC,QAAQ,GAAG,CAAC,CAAC;SACnF;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1F,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QAEnC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,WAAW,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,cAAc,EAAE,EAAE;YACjG,cAAc,CAAC,gBAAgB,GAAG,WAAW,CAAC,QAAQ,IAAI,CAAC,CAAC;YAC5D,UAAU,CAAC,kBAAkB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;YACvE,MAAM,CAAC,cAAc,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,OAAe,EAAE,OAAiB,EAAE,SAAgD,GAAG,EAAE,GAAE,CAAC;QACjH,MAAM,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACpF,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;QAEjD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACrJ,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACpF,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAEzH,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,mBAAmB,CACtB,OAAe,EACf,OAAiB,EACjB,KAAa,EACb,SAAgD,GAAG,EAAE,GAAE,CAAC,EACxD,oBAA0B,EAC1B,aAAuB;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;QAE7E,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAQ,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC;QACnE,MAAM,sBAAsB,GAA4B;YACpD,QAAQ,EAAE,WAAW,CAAC,SAAS;YAC/B,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,MAAM,EAAE,GAAG,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACjB,QAAQ,CAAC,OAAO,EAAE,CAAC;iBACtB;YACL,CAAC;YACD,OAAO,EAAE,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;gBAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACjB,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,OAAO,KAAK,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI,wBAAwB,EAAE,CAAC,CAAC,CAAC;iBACzI;YACL,CAAC;YACD,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,aAAa,EAAE,oBAAoB;YACnC,aAAa,EAAE,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;SAChE,CAAC;QACF,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,sBAAsB,CAAC,CAAC;QACrF,cAAc,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;QAClD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEhC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,cAAc,CAAC,WAAW,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/D,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,SAAS,KAAK,CAAC,KAAK,EAAE,CAAC;YAClE,MAAM,OAAO,GAAG,QAAQ,IAAI,CAAC,cAAc,GAAG,IAAI,EAAE,CAAC;YACrD,cAAc,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC5C,CAAC,CAAC,CACL,CAAC;QAEF,cAAc,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QACzC,cAAc,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QACzC,MAAM,CAAC,cAAc,CAAC,CAAC;QAEvB,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,OAAO,cAAc,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,YAAY,CAAC,OAAe,EAAE,OAAiB;QACnD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAChB,OAAO,CAAC,KAAK,GAAG;gBACZ,SAAS,EAAE,OAAO,CAAC,SAAS,wCAA6B,IAAI,OAAO,CAAC,SAAS,uCAA4B;gBAC1G,YAAY,EAAE,UAAU,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC;gBAClE,KAAK,EAAE,UAAU,CAAC,mBAAmB,CAAC,GAAG,OAAO,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC;gBACxE,KAAK,EAAE,UAAU,CAAC,mBAAmB,CAAC,GAAG,OAAO,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC;aAC3E,CAAC;SACL;QAED,OAAO,OAAO,CAAC,KAAK,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,OAAe,EAAE,KAAa;QAChD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;YAE/C,IAAI,KAAK,CAAC,GAAG,EAAE;gBACX,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,OAAO,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;aACvE;iBAAM;gBACH,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;gBACpG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC;aAC1F;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;SACnB;QAED,OAAO,KAAK,CAAC,KAAK,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACI,YAAY,CAAC,OAAe,EAAE,QAAmB,EAAE,GAAW;QACjE,MAAM,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC9E,IAAI,gBAAgB,EAAE;YAClB,OAAO,gBAAgB,CAAC;SAC3B;QAED,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,MAAM,GAAG,cAAc,CAAC,CAAC;SACtD;QAED,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE;YACtB,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1D,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;YAC/E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,aAAa,GAAG,EAAE,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACrE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,SAAS,CAClB,IAAI,CAAC,aAAa,EAClB,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;oBACL,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;wBACjB,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,YAAY,GAAG,KAAM,IAAoB,CAAC,UAAU,SAAS,CAAC,CAAC;wBAClF,OAAO,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,CAAC;qBAChD;gBACL,CAAC,EACD,IAAI,EACJ,CAAC,OAAO,EAAE,EAAE;oBACR,MAAM,CAAC,IAAI,aAAa,CAAC,GAAG,OAAO,qBAAqB,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;gBAChJ,CAAC,CACJ,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,kBAAkB,CAAC,aAA4B,EAAE,OAAe;QAC1E,aAAa,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE,CAAC;QACtD,MAAM,QAAQ,GAAG,CAAC,aAAa,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;QAC3F,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QACvD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,OAAe,EAAE,IAAiC;QACjF,4BAA4B;QAC5B,IAAI,GAAG,IAAI,IAAI,SAAS,CAAC,CAAC,oCAAwB,CAAC,CAAC,IAAI,CAAC;QAEzD,QAAQ,IAAI,EAAE;YACV;gBACI,OAAO,OAAO,CAAC,iBAAiB,CAAC;YACrC;gBACI,OAAO,OAAO,CAAC,kBAAkB,CAAC;YACtC;gBACI,OAAO,OAAO,CAAC,gBAAgB,CAAC;YACpC;gBACI,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,oBAAoB,IAAI,GAAG,CAAC,CAAC;gBACnD,OAAO,OAAO,CAAC,gBAAgB,CAAC;SACvC;IACL,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,OAAe,EAAE,OAAiB;QACrE,4BAA4B;QAC5B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,oCAAyB,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;QAC/F,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC,kDAAuC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;QAE7G,IAAI,SAAS,uCAA4B,EAAE;YACvC,QAAQ,SAAS,EAAE;gBACf;oBACI,OAAO,OAAO,CAAC,cAAc,CAAC;gBAClC;oBACI,OAAO,OAAO,CAAC,aAAa,CAAC;gBACjC;oBACI,OAAO,OAAO,CAAC,yBAAyB,CAAC;gBAC7C;oBACI,OAAO,OAAO,CAAC,wBAAwB,CAAC;gBAC5C;oBACI,OAAO,OAAO,CAAC,wBAAwB,CAAC;gBAC5C;oBACI,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC3C;oBACI,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,8BAA8B,SAAS,GAAG,CAAC,CAAC;oBAClE,OAAO,OAAO,CAAC,uBAAuB,CAAC;aAC9C;SACJ;aAAM;YACH,IAAI,SAAS,wCAA6B,EAAE;gBACxC,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,8BAA8B,SAAS,GAAG,CAAC,CAAC;aACrE;YAED,QAAQ,SAAS,EAAE;gBACf;oBACI,OAAO,OAAO,CAAC,eAAe,CAAC;gBACnC;oBACI,OAAO,OAAO,CAAC,cAAc,CAAC;gBAClC;oBACI,OAAO,OAAO,CAAC,0BAA0B,CAAC;gBAC9C;oBACI,OAAO,OAAO,CAAC,yBAAyB,CAAC;gBAC7C;oBACI,OAAO,OAAO,CAAC,yBAAyB,CAAC;gBAC7C;oBACI,OAAO,OAAO,CAAC,wBAAwB,CAAC;gBAC5C;oBACI,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,8BAA8B,SAAS,GAAG,CAAC,CAAC;oBAClE,OAAO,OAAO,CAAC,0BAA0B,CAAC;aACjD;SACJ;IACL,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,OAAe,EAAE,aAAoC;QAC1F,QAAQ,aAAa,EAAE;YACnB;gBACI,OAAO,SAAS,CAAC;YACrB;gBACI,OAAO,UAAU,CAAC;YACtB;gBACI,OAAO,UAAU,CAAC;YACtB;gBACI,OAAO,WAAW,CAAC;YACvB;gBACI,OAAO,WAAW,CAAC;YACvB;gBACI,OAAO,YAAY,CAAC;YACxB;gBACI,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,4BAA4B,aAAa,EAAE,CAAC,CAAC;SAC9E;IACL,CAAC;IAEO,MAAM,CAAC,cAAc,CACzB,OAAe,EACf,aAAoC,EACpC,UAA2B,EAC3B,UAA8B,EAC9B,MAAc;QAEd,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACjC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;QAEvD,MAAM,WAAW,GAAG,UAAU,CAAC,yBAAyB,CAAC,GAAG,OAAO,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEpG,MAAM,mBAAmB,GAAG,YAAY,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;QAC1E,IAAI,UAAU,GAAG,mBAAmB,KAAK,CAAC,EAAE;YACxC,wFAAwF;YACxF,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,oCAAoC,UAAU,sDAAsD,mBAAmB,GAAG,CAAC,CAAC;YAClJ,OAAO,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,GAAG,MAAM,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;SAClG;QAED,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACvD,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,OAAe,EAAE,IAAY;QAC1D,QAAQ,IAAI,EAAE;YACV,KAAK,QAAQ;gBACT,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,CAAC,CAAC;YACb,KAAK,MAAM;gBACP,OAAO,EAAE,CAAC;SACjB;QAED,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,mBAAmB,IAAI,GAAG,CAAC,CAAC;IAC1D,CAAC;IAEO,MAAM,CAAC,YAAY,CAAC,GAAW;QACnC,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,OAAe,EAAE,IAAwB;QAChE,IAAI,IAAI,IAAI,SAAS,EAAE;YACnB,IAAI,sCAA8B,CAAC;SACtC;QAED,QAAQ,IAAI,EAAE;YACV;gBACI,OAAO,QAAQ,CAAC,iBAAiB,CAAC;YACtC;gBACI,OAAO,QAAQ,CAAC,gBAAgB,CAAC;YACrC;gBACI,OAAO,QAAQ,CAAC,gBAAgB,CAAC;YACrC;gBACI,OAAO,QAAQ,CAAC,iBAAiB,CAAC;YACtC;gBACI,OAAO,QAAQ,CAAC,gBAAgB,CAAC;YACrC;gBACI,OAAO,QAAQ,CAAC,qBAAqB,CAAC;YAC1C;gBACI,OAAO,QAAQ,CAAC,mBAAmB,CAAC;SAC3C;QAED,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,kCAAkC,IAAI,GAAG,CAAC,CAAC;IACzE,CAAC;IAEO,sBAAsB;QAC1B,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;QAE3D,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACtB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACzC,IAAI,QAAQ,CAAC,KAAK,EAAE;oBAChB,KAAK,MAAM,eAAe,IAAI,QAAQ,CAAC,KAAK,EAAE;wBAC1C,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;wBACpD,KAAK,MAAM,WAAW,IAAI,WAAW,CAAC,aAAa,EAAE;4BACjD,gDAAgD;4BAChD,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;4BAErC,MAAM,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;4BACpD,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC;4BAClE,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;4BAC1F,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;gCAC3B,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gCACvF,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;6BAC9G;yBACJ;qBACJ;iBACJ;aACJ;SACJ;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,6BAA6B;QACjC,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,2BAA2B,CAAC,CAAC;QAEnE,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QACzC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YACxB,MAAM,SAAS,GAAG,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAC7C,IAAI,SAAS,EAAE;gBACX,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC;aACpD;SACJ;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,CAAC,MAAiD;QACxE,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,MAAM,CAAC,SAAS,CAAC,CAAC;aACrB;SACJ;IACL,CAAC;IAEO,gBAAgB,CAAI,QAAmB,EAAE,YAAoB,EAAE,WAAyE;QAC5I,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,IAAI,SAAS,CAAC,OAAO,EAAE;gBACnB,MAAM,EAAE,GAAG,GAAG,SAAS,CAAC,IAAI,IAAI,YAAY,EAAE,CAAC;gBAC/C,MAAM,cAAc,GAAG,QAA2B,CAAC;gBACnD,cAAc,CAAC,+BAA+B,GAAG,cAAc,CAAC,+BAA+B,IAAI,EAAE,CAAC;gBACtG,MAAM,8BAA8B,GAAG,cAAc,CAAC,+BAA+B,CAAC;gBACtF,IAAI,CAAC,8BAA8B,CAAC,EAAE,CAAC,EAAE;oBACrC,8BAA8B,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;oBAE1C,IAAI;wBACA,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;wBACtC,IAAI,MAAM,EAAE;4BACR,OAAO,MAAM,CAAC;yBACjB;qBACJ;4BAAS;wBACN,OAAO,8BAA8B,CAAC,EAAE,CAAC,CAAC;qBAC7C;iBACJ;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,kBAAkB,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;IACzF,CAAC;IAEO,kBAAkB;QACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IACrF,CAAC;IAEO,yBAAyB,CAAC,OAAe,EAAE,KAAa;QAC5D,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,WAAW,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;IAC1I,CAAC;IAEO,wBAAwB,CAAC,OAAe,EAAE,IAAW,EAAE,MAAqD;QAChH,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;IAC7I,CAAC;IAEO,0BAA0B,CAAC,OAAe,EAAE,MAAe,EAAE,MAAuC;QACxG,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,eAAe,IAAI,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IACvJ,CAAC;IAEO,8BAA8B,CAAC,OAAe,EAAE,SAAyB,EAAE,WAAiB;QAChG,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC;IAChL,CAAC;IAEO,iCAAiC,CACrC,OAAe,EACf,IAAY,EACZ,IAAW,EACX,IAAW,EACX,SAAyB,EACzB,MAA2C;QAE3C,OAAO,IAAI,CAAC,gBAAgB,CACxB,SAAS,EACT,mBAAmB,EACnB,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,uBAAuB,IAAI,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CACtI,CAAC;IACN,CAAC;IAEO,4BAA4B,CAChC,OAAe,EACf,QAAmB,EACnB,WAA2B,EAC3B,eAAuB,EACvB,MAA2C;QAE3C,OAAO,IAAI,CAAC,gBAAgB,CACxB,QAAQ,EACR,cAAc,EACd,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,kBAAkB,IAAI,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,CAAC,CACvI,CAAC;IACN,CAAC;IAEO,yBAAyB,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAuB;QAC3F,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;IACtK,CAAC;IAEO,sCAAsC,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC1G,OAAO,IAAI,CAAC,gBAAgB,CACxB,QAAQ,EACR,wBAAwB,EACxB,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,2BAA2B,IAAI,SAAS,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CACpI,CAAC;IACN,CAAC;IAEO,+BAA+B,CAAC,OAAe,EAAE,WAAyB,EAAE,MAA6C;QAC7H,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,oBAAoB,CAAC,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;IAChL,CAAC;IAEO,2BAA2B,CAAC,OAAe,EAAE,OAAiB,EAAE,MAA6C;QACjH,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,iBAAiB,IAAI,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9J,CAAC;IAEO,6BAA6B,CAAC,OAAe,EAAE,SAAqB;QACxE,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,eAAe,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,kBAAkB,IAAI,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;IAC9J,CAAC;IAEO,oCAAoC,CACxC,OAAe,EACf,gBAAwB,EACxB,SAAqB,EACrB,OAA0B,EAC1B,MAA6E;QAE7E,OAAO,IAAI,CAAC,gBAAgB,CACxB,SAAS,EACT,sBAAsB,EACtB,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,0BAA0B,IAAI,SAAS,CAAC,0BAA0B,CAAC,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC,CACrJ,CAAC;IACN,CAAC;IAEO,wBAAwB,CAAC,OAAe,EAAE,IAAW,EAAE,IAAW;QACtE,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7I,CAAC;IAEO,uBAAuB,CAAC,OAAe,EAAE,QAAmB,EAAE,GAAW;QAC7E,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACjJ,CAAC;IAEO,8BAA8B,CAAC,OAAe,EAAE,UAAuB;QAC3E,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC;IACnK,CAAC;IAEO,0BAA0B,CAAC,OAAe,EAAE,MAAe,EAAE,UAAkB,EAAE,UAAkB;QACvG,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,eAAe,IAAI,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;IACvK,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,kBAAkB,CAC5B,OAAe,EACf,QAAmB,EACnB,aAAqB,EACrB,WAA4F;QAE5F,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YACtB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QAEvC,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAe,CAAC;QAC1D,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,OAAO,WAAW,CAAC,GAAG,OAAO,eAAe,aAAa,EAAE,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,cAAc,CACxB,OAAe,EACf,QAAmB,EACnB,aAAqB,EACrB,WAAgF;QAEhF,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE/B,MAAM,KAAK,GAAG,MAAM,CAAC,aAAa,CAAW,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,OAAO,WAAW,CAAC,GAAG,OAAO,WAAW,aAAa,EAAE,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,IAAY;QAC/B,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACzF,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,OAAe;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,GAAG,CAAC,OAAe;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,WAAmB;QAC9C,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,qBAAqB,CAAC,WAAmB;QAC5C,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;;AA1qFc,gCAAqB,GAA6C,EAAE,CAAC;AAEpF;;GAEG;AACoB,yBAAc,GAAa,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC;AAwqFpE,cAAc,CAAC,kBAAkB,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC", "sourcesContent": ["import type { IndicesArray, Nullable } from \"core/types\";\r\nimport { Deferred } from \"core/Misc/deferred\";\r\nimport { Quaternion, Vector3, Matrix, TmpVectors } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport { Camera } from \"core/Cameras/camera\";\r\nimport { FreeCamera } from \"core/Cameras/freeCamera\";\r\nimport type { Animation } from \"core/Animations/animation\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\nimport type { IAnimationKey } from \"core/Animations/animationKey\";\r\nimport { AnimationKeyInterpolation } from \"core/Animations/animationKey\";\r\nimport { AnimationGroup } from \"core/Animations/animationGroup\";\r\nimport { Bone } from \"core/Bones/bone\";\r\nimport { Skeleton } from \"core/Bones/skeleton\";\r\nimport { Material } from \"core/Materials/material\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { ITextureCreationOptions } from \"core/Materials/Textures/texture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport { TransformNode } from \"core/Meshes/transformNode\";\r\nimport { Buffer, VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Geometry } from \"core/Meshes/geometry\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { InstancedMesh } from \"core/Meshes/instancedMesh\";\r\nimport { Mesh } from \"core/Meshes/mesh\";\r\nimport { MorphTarget } from \"core/Morph/morphTarget\";\r\nimport { MorphTargetManager } from \"core/Morph/morphTargetManager\";\r\nimport type { ISceneLoaderAsyncResult, ISceneLoaderProgressEvent } from \"core/Loading/sceneLoader\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { IProperty } from \"babylonjs-gltf2interface\";\r\nimport {\r\n    AnimationChannelTargetPath,\r\n    AnimationSamplerInterpolation,\r\n    AccessorType,\r\n    CameraType,\r\n    AccessorComponentType,\r\n    MaterialAlphaMode,\r\n    TextureMinFilter,\r\n    TextureWrapMode,\r\n    TextureMagFilter,\r\n    MeshPrimitiveMode,\r\n} from \"babylonjs-gltf2interface\";\r\nimport type {\r\n    IGLTF,\r\n    ISampler,\r\n    INode,\r\n    IScene,\r\n    IMesh,\r\n    IAccessor,\r\n    ISkin,\r\n    ICamera,\r\n    IAnimation,\r\n    IBuffer,\r\n    IBufferView,\r\n    IMaterialPbrMetallicRoughness,\r\n    IMaterial,\r\n    ITextureInfo,\r\n    ITexture,\r\n    IImage,\r\n    IMeshPrimitive,\r\n    IArrayItem,\r\n    _ISamplerData,\r\n    IAnimationChannel,\r\n    IAnimationSampler,\r\n    _IAnimationSamplerData,\r\n} from \"./glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"./glTFLoaderExtension\";\r\nimport type { IGLTFLoader, IGLTFLoaderData } from \"../glTFFileLoader\";\r\nimport { GLTFFileLoader, GLTFLoaderState, GLTFLoaderCoordinateSystemMode, GLTFLoaderAnimationStartMode } from \"../glTFFileLoader\";\r\nimport type { IDataBuffer } from \"core/Misc/dataReader\";\r\nimport { DecodeBase64UrlToBinary, IsBase64DataUrl, LoadFileError } from \"core/Misc/fileTools\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport type { Light } from \"core/Lights/light\";\r\nimport { BoundingInfo } from \"core/Culling/boundingInfo\";\r\nimport type { AssetContainer } from \"core/assetContainer\";\r\nimport type { AnimationPropertyInfo } from \"./glTFLoaderAnimation\";\r\nimport { nodeAnimationData } from \"./glTFLoaderAnimation\";\r\n\r\ninterface TypedArrayLike extends ArrayBufferView {\r\n    readonly length: number;\r\n    [n: number]: number;\r\n}\r\n\r\ninterface TypedArrayConstructor {\r\n    new (length: number): TypedArrayLike;\r\n    new (buffer: ArrayBufferLike, byteOffset: number, length?: number): TypedArrayLike;\r\n}\r\n\r\ninterface ILoaderProperty extends IProperty {\r\n    _activeLoaderExtensionFunctions: {\r\n        [id: string]: boolean;\r\n    };\r\n}\r\n\r\ninterface IRegisteredExtension {\r\n    factory: (loader: GLTFLoader) => IGLTFLoaderExtension;\r\n}\r\n\r\ninterface IWithMetadata {\r\n    metadata: any;\r\n    _internalMetadata: any;\r\n}\r\n\r\n// https://stackoverflow.com/a/48218209\r\nfunction mergeDeep(...objects: any[]): any {\r\n    const isObject = (obj: any) => obj && typeof obj === \"object\";\r\n\r\n    return objects.reduce((prev, obj) => {\r\n        Object.keys(obj).forEach((key) => {\r\n            const pVal = prev[key];\r\n            const oVal = obj[key];\r\n\r\n            if (Array.isArray(pVal) && Array.isArray(oVal)) {\r\n                prev[key] = pVal.concat(...oVal);\r\n            } else if (isObject(pVal) && isObject(oVal)) {\r\n                prev[key] = mergeDeep(pVal, oVal);\r\n            } else {\r\n                prev[key] = oVal;\r\n            }\r\n        });\r\n\r\n        return prev;\r\n    }, {});\r\n}\r\n\r\n/**\r\n * Helper class for working with arrays when loading the glTF asset\r\n */\r\nexport class ArrayItem {\r\n    /**\r\n     * Gets an item from the given array.\r\n     * @param context The context when loading the asset\r\n     * @param array The array to get the item from\r\n     * @param index The index to the array\r\n     * @returns The array item\r\n     */\r\n    public static Get<T>(context: string, array: ArrayLike<T> | undefined, index: number | undefined): T {\r\n        if (!array || index == undefined || !array[index]) {\r\n            throw new Error(`${context}: Failed to find index (${index})`);\r\n        }\r\n\r\n        return array[index];\r\n    }\r\n\r\n    /**\r\n     * Assign an `index` field to each item of the given array.\r\n     * @param array The array of items\r\n     */\r\n    public static Assign(array?: IArrayItem[]): void {\r\n        if (array) {\r\n            for (let index = 0; index < array.length; index++) {\r\n                array[index].index = index;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport interface IAnimationTargetInfo {\r\n    /** @internal */\r\n    target: any;\r\n\r\n    /** @internal */\r\n    properties: Array<AnimationPropertyInfo>;\r\n}\r\n\r\n/**\r\n * The glTF 2.0 loader\r\n */\r\nexport class GLTFLoader implements IGLTFLoader {\r\n    /** @internal */\r\n    public readonly _completePromises = new Array<Promise<any>>();\r\n\r\n    /** @internal */\r\n    public _assetContainer: Nullable<AssetContainer> = null;\r\n\r\n    /** Storage */\r\n    public _babylonLights: Light[] = [];\r\n\r\n    /** @internal */\r\n    public _disableInstancedMesh = 0;\r\n\r\n    private readonly _parent: GLTFFileLoader;\r\n    private readonly _extensions = new Array<IGLTFLoaderExtension>();\r\n    private _disposed = false;\r\n    private _rootUrl: Nullable<string> = null;\r\n    private _fileName: Nullable<string> = null;\r\n    private _uniqueRootUrl: Nullable<string> = null;\r\n    private _gltf: IGLTF;\r\n    private _bin: Nullable<IDataBuffer> = null;\r\n    private _babylonScene: Scene;\r\n    private _rootBabylonMesh: Nullable<Mesh> = null;\r\n    private _defaultBabylonMaterialData: { [drawMode: number]: Material } = {};\r\n    private readonly _postSceneLoadActions = new Array<() => void>();\r\n\r\n    private static _RegisteredExtensions: { [name: string]: IRegisteredExtension } = {};\r\n\r\n    /**\r\n     * The default glTF sampler.\r\n     */\r\n    public static readonly DefaultSampler: ISampler = { index: -1 };\r\n\r\n    /**\r\n     * Registers a loader extension.\r\n     * @param name The name of the loader extension.\r\n     * @param factory The factory function that creates the loader extension.\r\n     */\r\n    public static RegisterExtension(name: string, factory: (loader: GLTFLoader) => IGLTFLoaderExtension): void {\r\n        if (GLTFLoader.UnregisterExtension(name)) {\r\n            Logger.Warn(`Extension with the name '${name}' already exists`);\r\n        }\r\n\r\n        GLTFLoader._RegisteredExtensions[name] = {\r\n            factory: factory,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Unregisters a loader extension.\r\n     * @param name The name of the loader extension.\r\n     * @returns A boolean indicating whether the extension has been unregistered\r\n     */\r\n    public static UnregisterExtension(name: string): boolean {\r\n        if (!GLTFLoader._RegisteredExtensions[name]) {\r\n            return false;\r\n        }\r\n\r\n        delete GLTFLoader._RegisteredExtensions[name];\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * The object that represents the glTF JSON.\r\n     */\r\n    public get gltf(): IGLTF {\r\n        if (!this._gltf) {\r\n            throw new Error(\"glTF JSON is not available\");\r\n        }\r\n\r\n        return this._gltf;\r\n    }\r\n\r\n    /**\r\n     * The BIN chunk of a binary glTF.\r\n     */\r\n    public get bin(): Nullable<IDataBuffer> {\r\n        return this._bin;\r\n    }\r\n\r\n    /**\r\n     * The parent file loader.\r\n     */\r\n    public get parent(): GLTFFileLoader {\r\n        return this._parent;\r\n    }\r\n\r\n    /**\r\n     * The Babylon scene when loading the asset.\r\n     */\r\n    public get babylonScene(): Scene {\r\n        if (!this._babylonScene) {\r\n            throw new Error(\"Scene is not available\");\r\n        }\r\n\r\n        return this._babylonScene;\r\n    }\r\n\r\n    /**\r\n     * The root Babylon mesh when loading the asset.\r\n     */\r\n    public get rootBabylonMesh(): Nullable<Mesh> {\r\n        return this._rootBabylonMesh;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(parent: GLTFFileLoader) {\r\n        this._parent = parent;\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        if (this._disposed) {\r\n            return;\r\n        }\r\n\r\n        this._disposed = true;\r\n\r\n        this._completePromises.length = 0;\r\n\r\n        this._extensions.forEach((extension) => extension.dispose && extension.dispose());\r\n        this._extensions.length = 0;\r\n\r\n        (this._gltf as Nullable<IGLTF>) = null; // TODO\r\n        this._bin = null;\r\n        (this._babylonScene as Nullable<Scene>) = null; // TODO\r\n        this._rootBabylonMesh = null;\r\n        this._defaultBabylonMaterialData = {};\r\n        this._postSceneLoadActions.length = 0;\r\n\r\n        this._parent.dispose();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public importMeshAsync(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        container: Nullable<AssetContainer>,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        fileName = \"\"\r\n    ): Promise<ISceneLoaderAsyncResult> {\r\n        return Promise.resolve().then(() => {\r\n            this._babylonScene = scene;\r\n            this._assetContainer = container;\r\n            this._loadData(data);\r\n\r\n            let nodes: Nullable<Array<number>> = null;\r\n\r\n            if (meshesNames) {\r\n                const nodeMap: { [name: string]: number } = {};\r\n                if (this._gltf.nodes) {\r\n                    for (const node of this._gltf.nodes) {\r\n                        if (node.name) {\r\n                            nodeMap[node.name] = node.index;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                const names = meshesNames instanceof Array ? meshesNames : [meshesNames];\r\n                nodes = names.map((name) => {\r\n                    const node = nodeMap[name];\r\n                    if (node === undefined) {\r\n                        throw new Error(`Failed to find node '${name}'`);\r\n                    }\r\n\r\n                    return node;\r\n                });\r\n            }\r\n\r\n            return this._loadAsync(rootUrl, fileName, nodes, () => {\r\n                return {\r\n                    meshes: this._getMeshes(),\r\n                    particleSystems: [],\r\n                    skeletons: this._getSkeletons(),\r\n                    animationGroups: this._getAnimationGroups(),\r\n                    lights: this._babylonLights,\r\n                    transformNodes: this._getTransformNodes(),\r\n                    geometries: this._getGeometries(),\r\n                };\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadAsync(scene: Scene, data: IGLTFLoaderData, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName = \"\"): Promise<void> {\r\n        return Promise.resolve().then(() => {\r\n            this._babylonScene = scene;\r\n            this._loadData(data);\r\n            return this._loadAsync(rootUrl, fileName, null, () => undefined);\r\n        });\r\n    }\r\n\r\n    private _loadAsync<T>(rootUrl: string, fileName: string, nodes: Nullable<Array<number>>, resultFunc: () => T): Promise<T> {\r\n        return Promise.resolve()\r\n            .then(() => {\r\n                this._rootUrl = rootUrl;\r\n                this._uniqueRootUrl = !rootUrl.startsWith(\"file:\") && fileName ? rootUrl : `${rootUrl}${Date.now()}/`;\r\n                this._fileName = fileName;\r\n\r\n                this._loadExtensions();\r\n                this._checkExtensions();\r\n\r\n                const loadingToReadyCounterName = `${GLTFLoaderState[GLTFLoaderState.LOADING]} => ${GLTFLoaderState[GLTFLoaderState.READY]}`;\r\n                const loadingToCompleteCounterName = `${GLTFLoaderState[GLTFLoaderState.LOADING]} => ${GLTFLoaderState[GLTFLoaderState.COMPLETE]}`;\r\n\r\n                this._parent._startPerformanceCounter(loadingToReadyCounterName);\r\n                this._parent._startPerformanceCounter(loadingToCompleteCounterName);\r\n\r\n                this._parent._setState(GLTFLoaderState.LOADING);\r\n                this._extensionsOnLoading();\r\n\r\n                const promises = new Array<Promise<any>>();\r\n\r\n                // Block the marking of materials dirty until the scene is loaded.\r\n                const oldBlockMaterialDirtyMechanism = this._babylonScene.blockMaterialDirtyMechanism;\r\n                this._babylonScene.blockMaterialDirtyMechanism = true;\r\n\r\n                if (!this.parent.loadOnlyMaterials) {\r\n                    if (nodes) {\r\n                        promises.push(this.loadSceneAsync(\"/nodes\", { nodes: nodes, index: -1 }));\r\n                    } else if (this._gltf.scene != undefined || (this._gltf.scenes && this._gltf.scenes[0])) {\r\n                        const scene = ArrayItem.Get(`/scene`, this._gltf.scenes, this._gltf.scene || 0);\r\n                        promises.push(this.loadSceneAsync(`/scenes/${scene.index}`, scene));\r\n                    }\r\n                }\r\n\r\n                if (!this.parent.skipMaterials && this.parent.loadAllMaterials && this._gltf.materials) {\r\n                    for (let m = 0; m < this._gltf.materials.length; ++m) {\r\n                        const material = this._gltf.materials[m];\r\n                        const context = \"/materials/\" + m;\r\n                        const babylonDrawMode = Material.TriangleFillMode;\r\n\r\n                        promises.push(this._loadMaterialAsync(context, material, null, babylonDrawMode, () => {}));\r\n                    }\r\n                }\r\n\r\n                // Restore the blocking of material dirty.\r\n                this._babylonScene.blockMaterialDirtyMechanism = oldBlockMaterialDirtyMechanism;\r\n\r\n                if (this._parent.compileMaterials) {\r\n                    promises.push(this._compileMaterialsAsync());\r\n                }\r\n\r\n                if (this._parent.compileShadowGenerators) {\r\n                    promises.push(this._compileShadowGeneratorsAsync());\r\n                }\r\n\r\n                const resultPromise = Promise.all(promises).then(() => {\r\n                    if (this._rootBabylonMesh) {\r\n                        this._rootBabylonMesh.setEnabled(true);\r\n                    }\r\n\r\n                    this._extensionsOnReady();\r\n                    this._parent._setState(GLTFLoaderState.READY);\r\n\r\n                    this._startAnimations();\r\n\r\n                    return resultFunc();\r\n                });\r\n\r\n                return resultPromise.then((result) => {\r\n                    this._parent._endPerformanceCounter(loadingToReadyCounterName);\r\n\r\n                    Tools.SetImmediate(() => {\r\n                        if (!this._disposed) {\r\n                            Promise.all(this._completePromises).then(\r\n                                () => {\r\n                                    this._parent._endPerformanceCounter(loadingToCompleteCounterName);\r\n\r\n                                    this._parent._setState(GLTFLoaderState.COMPLETE);\r\n\r\n                                    this._parent.onCompleteObservable.notifyObservers(undefined);\r\n                                    this._parent.onCompleteObservable.clear();\r\n\r\n                                    this.dispose();\r\n                                },\r\n                                (error) => {\r\n                                    this._parent.onErrorObservable.notifyObservers(error);\r\n                                    this._parent.onErrorObservable.clear();\r\n\r\n                                    this.dispose();\r\n                                }\r\n                            );\r\n                        }\r\n                    });\r\n\r\n                    return result;\r\n                });\r\n            })\r\n            .catch((error) => {\r\n                if (!this._disposed) {\r\n                    this._parent.onErrorObservable.notifyObservers(error);\r\n                    this._parent.onErrorObservable.clear();\r\n\r\n                    this.dispose();\r\n                }\r\n\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    private _loadData(data: IGLTFLoaderData): void {\r\n        this._gltf = data.json as IGLTF;\r\n        this._setupData();\r\n\r\n        if (data.bin) {\r\n            const buffers = this._gltf.buffers;\r\n            if (buffers && buffers[0] && !buffers[0].uri) {\r\n                const binaryBuffer = buffers[0];\r\n                if (binaryBuffer.byteLength < data.bin.byteLength - 3 || binaryBuffer.byteLength > data.bin.byteLength) {\r\n                    Logger.Warn(`Binary buffer length (${binaryBuffer.byteLength}) from JSON does not match chunk length (${data.bin.byteLength})`);\r\n                }\r\n\r\n                this._bin = data.bin;\r\n            } else {\r\n                Logger.Warn(\"Unexpected BIN chunk\");\r\n            }\r\n        }\r\n    }\r\n\r\n    private _setupData(): void {\r\n        ArrayItem.Assign(this._gltf.accessors);\r\n        ArrayItem.Assign(this._gltf.animations);\r\n        ArrayItem.Assign(this._gltf.buffers);\r\n        ArrayItem.Assign(this._gltf.bufferViews);\r\n        ArrayItem.Assign(this._gltf.cameras);\r\n        ArrayItem.Assign(this._gltf.images);\r\n        ArrayItem.Assign(this._gltf.materials);\r\n        ArrayItem.Assign(this._gltf.meshes);\r\n        ArrayItem.Assign(this._gltf.nodes);\r\n        ArrayItem.Assign(this._gltf.samplers);\r\n        ArrayItem.Assign(this._gltf.scenes);\r\n        ArrayItem.Assign(this._gltf.skins);\r\n        ArrayItem.Assign(this._gltf.textures);\r\n\r\n        if (this._gltf.nodes) {\r\n            const nodeParents: { [index: number]: number } = {};\r\n            for (const node of this._gltf.nodes) {\r\n                if (node.children) {\r\n                    for (const index of node.children) {\r\n                        nodeParents[index] = node.index;\r\n                    }\r\n                }\r\n            }\r\n\r\n            const rootNode = this._createRootNode();\r\n            for (const node of this._gltf.nodes) {\r\n                const parentIndex = nodeParents[node.index];\r\n                node.parent = parentIndex === undefined ? rootNode : this._gltf.nodes[parentIndex];\r\n            }\r\n        }\r\n    }\r\n\r\n    private _loadExtensions(): void {\r\n        for (const name in GLTFLoader._RegisteredExtensions) {\r\n            const extension = GLTFLoader._RegisteredExtensions[name].factory(this);\r\n            if (extension.name !== name) {\r\n                Logger.Warn(`The name of the glTF loader extension instance does not match the registered name: ${extension.name} !== ${name}`);\r\n            }\r\n\r\n            this._extensions.push(extension);\r\n            this._parent.onExtensionLoadedObservable.notifyObservers(extension);\r\n        }\r\n\r\n        this._extensions.sort((a, b) => (a.order || Number.MAX_VALUE) - (b.order || Number.MAX_VALUE));\r\n        this._parent.onExtensionLoadedObservable.clear();\r\n    }\r\n\r\n    private _checkExtensions(): void {\r\n        if (this._gltf.extensionsRequired) {\r\n            for (const name of this._gltf.extensionsRequired) {\r\n                const available = this._extensions.some((extension) => extension.name === name && extension.enabled);\r\n                if (!available) {\r\n                    throw new Error(`Require extension ${name} is not available`);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createRootNode(): INode {\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        this._rootBabylonMesh = new Mesh(\"__root__\", this._babylonScene);\r\n        this._rootBabylonMesh._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n        this._rootBabylonMesh.setEnabled(false);\r\n\r\n        const rootNode: INode = {\r\n            _babylonTransformNode: this._rootBabylonMesh,\r\n            index: -1,\r\n        };\r\n\r\n        switch (this._parent.coordinateSystemMode) {\r\n            case GLTFLoaderCoordinateSystemMode.AUTO: {\r\n                if (!this._babylonScene.useRightHandedSystem) {\r\n                    rootNode.rotation = [0, 1, 0, 0];\r\n                    rootNode.scale = [1, 1, -1];\r\n                    GLTFLoader._LoadTransform(rootNode, this._rootBabylonMesh);\r\n                }\r\n                break;\r\n            }\r\n            case GLTFLoaderCoordinateSystemMode.FORCE_RIGHT_HANDED: {\r\n                this._babylonScene.useRightHandedSystem = true;\r\n                break;\r\n            }\r\n            default: {\r\n                throw new Error(`Invalid coordinate system mode (${this._parent.coordinateSystemMode})`);\r\n            }\r\n        }\r\n\r\n        this._parent.onMeshLoadedObservable.notifyObservers(this._rootBabylonMesh);\r\n        return rootNode;\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF scene.\r\n     * @param context The context when loading the asset\r\n     * @param scene The glTF scene property\r\n     * @returns A promise that resolves when the load is complete\r\n     */\r\n    public loadSceneAsync(context: string, scene: IScene): Promise<void> {\r\n        const extensionPromise = this._extensionsLoadSceneAsync(context, scene);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        this.logOpen(`${context} ${scene.name || \"\"}`);\r\n\r\n        if (scene.nodes) {\r\n            for (const index of scene.nodes) {\r\n                const node = ArrayItem.Get(`${context}/nodes/${index}`, this._gltf.nodes, index);\r\n                promises.push(\r\n                    this.loadNodeAsync(`/nodes/${node.index}`, node, (babylonMesh) => {\r\n                        babylonMesh.parent = this._rootBabylonMesh;\r\n                    })\r\n                );\r\n            }\r\n        }\r\n\r\n        for (const action of this._postSceneLoadActions) {\r\n            action();\r\n        }\r\n\r\n        promises.push(this._loadAnimationsAsync());\r\n\r\n        this.logClose();\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    private _forEachPrimitive(node: INode, callback: (babylonMesh: AbstractMesh) => void): void {\r\n        if (node._primitiveBabylonMeshes) {\r\n            for (const babylonMesh of node._primitiveBabylonMeshes) {\r\n                callback(babylonMesh);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getGeometries(): Geometry[] {\r\n        const geometries = new Array<Geometry>();\r\n\r\n        const nodes = this._gltf.nodes;\r\n        if (nodes) {\r\n            for (const node of nodes) {\r\n                this._forEachPrimitive(node, (babylonMesh) => {\r\n                    const geometry = (babylonMesh as Mesh).geometry;\r\n                    if (geometry && geometries.indexOf(geometry) === -1) {\r\n                        geometries.push(geometry);\r\n                    }\r\n                });\r\n            }\r\n        }\r\n\r\n        return geometries;\r\n    }\r\n\r\n    private _getMeshes(): AbstractMesh[] {\r\n        const meshes = new Array<AbstractMesh>();\r\n\r\n        // Root mesh is always first, if available.\r\n        if (this._rootBabylonMesh) {\r\n            meshes.push(this._rootBabylonMesh);\r\n        }\r\n\r\n        const nodes = this._gltf.nodes;\r\n        if (nodes) {\r\n            for (const node of nodes) {\r\n                this._forEachPrimitive(node, (babylonMesh) => {\r\n                    meshes.push(babylonMesh);\r\n                });\r\n            }\r\n        }\r\n\r\n        return meshes;\r\n    }\r\n\r\n    private _getTransformNodes(): TransformNode[] {\r\n        const transformNodes = new Array<TransformNode>();\r\n\r\n        const nodes = this._gltf.nodes;\r\n        if (nodes) {\r\n            for (const node of nodes) {\r\n                if (node._babylonTransformNode && node._babylonTransformNode.getClassName() === \"TransformNode\") {\r\n                    transformNodes.push(node._babylonTransformNode);\r\n                }\r\n                if (node._babylonTransformNodeForSkin) {\r\n                    transformNodes.push(node._babylonTransformNodeForSkin);\r\n                }\r\n            }\r\n        }\r\n\r\n        return transformNodes;\r\n    }\r\n\r\n    private _getSkeletons(): Skeleton[] {\r\n        const skeletons = new Array<Skeleton>();\r\n\r\n        const skins = this._gltf.skins;\r\n        if (skins) {\r\n            for (const skin of skins) {\r\n                if (skin._data) {\r\n                    skeletons.push(skin._data.babylonSkeleton);\r\n                }\r\n            }\r\n        }\r\n\r\n        return skeletons;\r\n    }\r\n\r\n    private _getAnimationGroups(): AnimationGroup[] {\r\n        const animationGroups = new Array<AnimationGroup>();\r\n\r\n        const animations = this._gltf.animations;\r\n        if (animations) {\r\n            for (const animation of animations) {\r\n                if (animation._babylonAnimationGroup) {\r\n                    animationGroups.push(animation._babylonAnimationGroup);\r\n                }\r\n            }\r\n        }\r\n\r\n        return animationGroups;\r\n    }\r\n\r\n    private _startAnimations(): void {\r\n        switch (this._parent.animationStartMode) {\r\n            case GLTFLoaderAnimationStartMode.NONE: {\r\n                // do nothing\r\n                break;\r\n            }\r\n            case GLTFLoaderAnimationStartMode.FIRST: {\r\n                const babylonAnimationGroups = this._getAnimationGroups();\r\n                if (babylonAnimationGroups.length !== 0) {\r\n                    babylonAnimationGroups[0].start(true);\r\n                }\r\n                break;\r\n            }\r\n            case GLTFLoaderAnimationStartMode.ALL: {\r\n                const babylonAnimationGroups = this._getAnimationGroups();\r\n                for (const babylonAnimationGroup of babylonAnimationGroups) {\r\n                    babylonAnimationGroup.start(true);\r\n                }\r\n                break;\r\n            }\r\n            default: {\r\n                Logger.Error(`Invalid animation start mode (${this._parent.animationStartMode})`);\r\n                return;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF node.\r\n     * @param context The context when loading the asset\r\n     * @param node The glTF node property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon mesh when the load is complete\r\n     */\r\n    public loadNodeAsync(context: string, node: INode, assign: (babylonTransformNode: TransformNode) => void = () => {}): Promise<TransformNode> {\r\n        const extensionPromise = this._extensionsLoadNodeAsync(context, node, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        if (node._babylonTransformNode) {\r\n            throw new Error(`${context}: Invalid recursive node hierarchy`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        this.logOpen(`${context} ${node.name || \"\"}`);\r\n\r\n        const loadNode = (babylonTransformNode: TransformNode) => {\r\n            GLTFLoader.AddPointerMetadata(babylonTransformNode, context);\r\n            GLTFLoader._LoadTransform(node, babylonTransformNode);\r\n\r\n            if (node.camera != undefined) {\r\n                const camera = ArrayItem.Get(`${context}/camera`, this._gltf.cameras, node.camera);\r\n                promises.push(\r\n                    this.loadCameraAsync(`/cameras/${camera.index}`, camera, (babylonCamera) => {\r\n                        babylonCamera.parent = babylonTransformNode;\r\n                    })\r\n                );\r\n            }\r\n\r\n            if (node.children) {\r\n                for (const index of node.children) {\r\n                    const childNode = ArrayItem.Get(`${context}/children/${index}`, this._gltf.nodes, index);\r\n                    promises.push(\r\n                        this.loadNodeAsync(`/nodes/${childNode.index}`, childNode, (childBabylonMesh) => {\r\n                            childBabylonMesh.parent = babylonTransformNode;\r\n                        })\r\n                    );\r\n                }\r\n            }\r\n\r\n            assign(babylonTransformNode);\r\n        };\r\n\r\n        if (node.mesh == undefined || node.skin != undefined) {\r\n            const nodeName = node.name || `node${node.index}`;\r\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n            const transformNode = new TransformNode(nodeName, this._babylonScene);\r\n            transformNode._parentContainer = this._assetContainer;\r\n            this._babylonScene._blockEntityCollection = false;\r\n            if (node.mesh == undefined) {\r\n                node._babylonTransformNode = transformNode;\r\n            } else {\r\n                node._babylonTransformNodeForSkin = transformNode;\r\n            }\r\n            loadNode(transformNode);\r\n        }\r\n\r\n        if (node.mesh != undefined) {\r\n            if (node.skin == undefined) {\r\n                const mesh = ArrayItem.Get(`${context}/mesh`, this._gltf.meshes, node.mesh);\r\n                promises.push(this._loadMeshAsync(`/meshes/${mesh.index}`, node, mesh, loadNode));\r\n            } else {\r\n                // See https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins (second implementation note)\r\n                // This code path will place the skinned mesh as a sibling of the skeleton root node without loading the\r\n                // transform, which effectively ignores the transform of the skinned mesh, as per spec.\r\n\r\n                const mesh = ArrayItem.Get(`${context}/mesh`, this._gltf.meshes, node.mesh);\r\n                promises.push(\r\n                    this._loadMeshAsync(`/meshes/${mesh.index}`, node, mesh, (babylonTransformNode) => {\r\n                        const babylonTransformNodeForSkin = node._babylonTransformNodeForSkin!;\r\n\r\n                        // Merge the metadata from the skin node to the skinned mesh in case a loader extension added metadata.\r\n                        babylonTransformNode.metadata = mergeDeep(babylonTransformNodeForSkin.metadata, babylonTransformNode.metadata || {});\r\n\r\n                        const skin = ArrayItem.Get(`${context}/skin`, this._gltf.skins, node.skin);\r\n                        promises.push(\r\n                            this._loadSkinAsync(`/skins/${skin.index}`, node, skin, (babylonSkeleton) => {\r\n                                this._forEachPrimitive(node, (babylonMesh) => {\r\n                                    babylonMesh.skeleton = babylonSkeleton;\r\n                                });\r\n\r\n                                // Wait until all the nodes are parented before parenting the skinned mesh.\r\n                                this._postSceneLoadActions.push(() => {\r\n                                    if (skin.skeleton != undefined) {\r\n                                        // Place the skinned mesh node as a sibling of the skeleton root node.\r\n                                        // Handle special case when the parent of the skeleton root is the skinned mesh.\r\n                                        const parentNode = ArrayItem.Get(`/skins/${skin.index}/skeleton`, this._gltf.nodes, skin.skeleton).parent!;\r\n                                        if (node.index === parentNode.index) {\r\n                                            babylonTransformNode.parent = babylonTransformNodeForSkin.parent;\r\n                                        } else {\r\n                                            babylonTransformNode.parent = parentNode._babylonTransformNode!;\r\n                                        }\r\n                                    } else {\r\n                                        babylonTransformNode.parent = this._rootBabylonMesh;\r\n                                    }\r\n\r\n                                    this._parent.onSkinLoadedObservable.notifyObservers({ node: babylonTransformNodeForSkin, skinnedNode: babylonTransformNode });\r\n                                });\r\n                            })\r\n                        );\r\n                    })\r\n                );\r\n            }\r\n        }\r\n\r\n        this.logClose();\r\n\r\n        return Promise.all(promises).then(() => {\r\n            this._forEachPrimitive(node, (babylonMesh) => {\r\n                if ((babylonMesh as Mesh).geometry && (babylonMesh as Mesh).geometry!.useBoundingInfoFromGeometry) {\r\n                    // simply apply the world matrices to the bounding info - the extends are already ok\r\n                    babylonMesh._updateBoundingInfo();\r\n                } else {\r\n                    babylonMesh.refreshBoundingInfo(true);\r\n                }\r\n            });\r\n\r\n            return node._babylonTransformNode!;\r\n        });\r\n    }\r\n\r\n    private _loadMeshAsync(context: string, node: INode, mesh: IMesh, assign: (babylonTransformNode: TransformNode) => void): Promise<TransformNode> {\r\n        const primitives = mesh.primitives;\r\n        if (!primitives || !primitives.length) {\r\n            throw new Error(`${context}: Primitives are missing`);\r\n        }\r\n\r\n        if (primitives[0].index == undefined) {\r\n            ArrayItem.Assign(primitives);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        this.logOpen(`${context} ${mesh.name || \"\"}`);\r\n\r\n        const name = node.name || `node${node.index}`;\r\n\r\n        if (primitives.length === 1) {\r\n            const primitive = mesh.primitives[0];\r\n            promises.push(\r\n                this._loadMeshPrimitiveAsync(`${context}/primitives/${primitive.index}`, name, node, mesh, primitive, (babylonMesh) => {\r\n                    node._babylonTransformNode = babylonMesh;\r\n                    node._primitiveBabylonMeshes = [babylonMesh];\r\n                })\r\n            );\r\n        } else {\r\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n            node._babylonTransformNode = new TransformNode(name, this._babylonScene);\r\n            node._babylonTransformNode._parentContainer = this._assetContainer;\r\n            this._babylonScene._blockEntityCollection = false;\r\n            node._primitiveBabylonMeshes = [];\r\n            for (const primitive of primitives) {\r\n                promises.push(\r\n                    this._loadMeshPrimitiveAsync(`${context}/primitives/${primitive.index}`, `${name}_primitive${primitive.index}`, node, mesh, primitive, (babylonMesh) => {\r\n                        babylonMesh.parent = node._babylonTransformNode!;\r\n                        node._primitiveBabylonMeshes!.push(babylonMesh);\r\n                    })\r\n                );\r\n            }\r\n        }\r\n\r\n        assign(node._babylonTransformNode!);\r\n\r\n        this.logClose();\r\n\r\n        return Promise.all(promises).then(() => {\r\n            return node._babylonTransformNode!;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal Define this method to modify the default behavior when loading data for mesh primitives.\r\n     * @param context The context when loading the asset\r\n     * @param name The mesh name when loading the asset\r\n     * @param node The glTF node when loading the asset\r\n     * @param mesh The glTF mesh when loading the asset\r\n     * @param primitive The glTF mesh primitive property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded mesh when the load is complete or null if not handled\r\n     */\r\n    public _loadMeshPrimitiveAsync(\r\n        context: string,\r\n        name: string,\r\n        node: INode,\r\n        mesh: IMesh,\r\n        primitive: IMeshPrimitive,\r\n        assign: (babylonMesh: AbstractMesh) => void\r\n    ): Promise<AbstractMesh> {\r\n        const extensionPromise = this._extensionsLoadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        this.logOpen(`${context}`);\r\n\r\n        const shouldInstance = this._disableInstancedMesh === 0 && this._parent.createInstances && node.skin == undefined && !mesh.primitives[0].targets;\r\n\r\n        let babylonAbstractMesh: AbstractMesh;\r\n        let promise: Promise<any>;\r\n\r\n        if (shouldInstance && primitive._instanceData) {\r\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n            babylonAbstractMesh = primitive._instanceData.babylonSourceMesh.createInstance(name) as InstancedMesh;\r\n            babylonAbstractMesh._parentContainer = this._assetContainer;\r\n            this._babylonScene._blockEntityCollection = false;\r\n            promise = primitive._instanceData.promise;\r\n        } else {\r\n            const promises = new Array<Promise<any>>();\r\n\r\n            this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n            const babylonMesh = new Mesh(name, this._babylonScene);\r\n            babylonMesh._parentContainer = this._assetContainer;\r\n            this._babylonScene._blockEntityCollection = false;\r\n            babylonMesh.overrideMaterialSideOrientation = this._babylonScene.useRightHandedSystem ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\r\n\r\n            this._createMorphTargets(context, node, mesh, primitive, babylonMesh);\r\n            promises.push(\r\n                this._loadVertexDataAsync(context, primitive, babylonMesh).then((babylonGeometry) => {\r\n                    return this._loadMorphTargetsAsync(context, primitive, babylonMesh, babylonGeometry).then(() => {\r\n                        if (this._disposed) {\r\n                            return;\r\n                        }\r\n\r\n                        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n                        babylonGeometry.applyToMesh(babylonMesh);\r\n                        babylonGeometry._parentContainer = this._assetContainer;\r\n                        this._babylonScene._blockEntityCollection = false;\r\n                    });\r\n                })\r\n            );\r\n\r\n            const babylonDrawMode = GLTFLoader._GetDrawMode(context, primitive.mode);\r\n            if (primitive.material == undefined) {\r\n                let babylonMaterial = this._defaultBabylonMaterialData[babylonDrawMode];\r\n                if (!babylonMaterial) {\r\n                    babylonMaterial = this._createDefaultMaterial(\"__GLTFLoader._default\", babylonDrawMode);\r\n                    this._parent.onMaterialLoadedObservable.notifyObservers(babylonMaterial);\r\n                    this._defaultBabylonMaterialData[babylonDrawMode] = babylonMaterial;\r\n                }\r\n                babylonMesh.material = babylonMaterial;\r\n            } else if (!this.parent.skipMaterials) {\r\n                const material = ArrayItem.Get(`${context}/material`, this._gltf.materials, primitive.material);\r\n                promises.push(\r\n                    this._loadMaterialAsync(`/materials/${material.index}`, material, babylonMesh, babylonDrawMode, (babylonMaterial) => {\r\n                        babylonMesh.material = babylonMaterial;\r\n                    })\r\n                );\r\n            }\r\n\r\n            promise = Promise.all(promises);\r\n\r\n            if (shouldInstance) {\r\n                primitive._instanceData = {\r\n                    babylonSourceMesh: babylonMesh,\r\n                    promise: promise,\r\n                };\r\n            }\r\n\r\n            babylonAbstractMesh = babylonMesh;\r\n        }\r\n\r\n        GLTFLoader.AddPointerMetadata(babylonAbstractMesh, context);\r\n        this._parent.onMeshLoadedObservable.notifyObservers(babylonAbstractMesh);\r\n        assign(babylonAbstractMesh);\r\n\r\n        this.logClose();\r\n\r\n        return promise.then(() => {\r\n            return babylonAbstractMesh;\r\n        });\r\n    }\r\n\r\n    private _loadVertexDataAsync(context: string, primitive: IMeshPrimitive, babylonMesh: Mesh): Promise<Geometry> {\r\n        const extensionPromise = this._extensionsLoadVertexDataAsync(context, primitive, babylonMesh);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        const attributes = primitive.attributes;\r\n        if (!attributes) {\r\n            throw new Error(`${context}: Attributes are missing`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        const babylonGeometry = new Geometry(babylonMesh.name, this._babylonScene);\r\n\r\n        if (primitive.indices == undefined) {\r\n            babylonMesh.isUnIndexed = true;\r\n        } else {\r\n            const accessor = ArrayItem.Get(`${context}/indices`, this._gltf.accessors, primitive.indices);\r\n            promises.push(\r\n                this._loadIndicesAccessorAsync(`/accessors/${accessor.index}`, accessor).then((data) => {\r\n                    babylonGeometry.setIndices(data);\r\n                })\r\n            );\r\n        }\r\n\r\n        const loadAttribute = (attribute: string, kind: string, callback?: (accessor: IAccessor) => void) => {\r\n            if (attributes[attribute] == undefined) {\r\n                return;\r\n            }\r\n\r\n            babylonMesh._delayInfo = babylonMesh._delayInfo || [];\r\n            if (babylonMesh._delayInfo.indexOf(kind) === -1) {\r\n                babylonMesh._delayInfo.push(kind);\r\n            }\r\n\r\n            const accessor = ArrayItem.Get(`${context}/attributes/${attribute}`, this._gltf.accessors, attributes[attribute]);\r\n            promises.push(\r\n                this._loadVertexAccessorAsync(`/accessors/${accessor.index}`, accessor, kind).then((babylonVertexBuffer) => {\r\n                    if (babylonVertexBuffer.getKind() === VertexBuffer.PositionKind && !this.parent.alwaysComputeBoundingBox && !babylonMesh.skeleton) {\r\n                        if (accessor.min && accessor.max) {\r\n                            const min = TmpVectors.Vector3[0].copyFromFloats(...(accessor.min as [number, number, number]));\r\n                            const max = TmpVectors.Vector3[1].copyFromFloats(...(accessor.max as [number, number, number]));\r\n                            if (accessor.normalized && accessor.componentType !== AccessorComponentType.FLOAT) {\r\n                                let divider = 1;\r\n                                switch (accessor.componentType) {\r\n                                    case AccessorComponentType.BYTE:\r\n                                        divider = 127.0;\r\n                                        break;\r\n                                    case AccessorComponentType.UNSIGNED_BYTE:\r\n                                        divider = 255.0;\r\n                                        break;\r\n                                    case AccessorComponentType.SHORT:\r\n                                        divider = 32767.0;\r\n                                        break;\r\n                                    case AccessorComponentType.UNSIGNED_SHORT:\r\n                                        divider = 65535.0;\r\n                                        break;\r\n                                }\r\n                                const oneOverDivider = 1 / divider;\r\n                                min.scaleInPlace(oneOverDivider);\r\n                                max.scaleInPlace(oneOverDivider);\r\n                            }\r\n                            babylonGeometry._boundingInfo = new BoundingInfo(min, max);\r\n                            babylonGeometry.useBoundingInfoFromGeometry = true;\r\n                        }\r\n                    }\r\n                    babylonGeometry.setVerticesBuffer(babylonVertexBuffer, accessor.count);\r\n                })\r\n            );\r\n\r\n            if (kind == VertexBuffer.MatricesIndicesExtraKind) {\r\n                babylonMesh.numBoneInfluencers = 8;\r\n            }\r\n\r\n            if (callback) {\r\n                callback(accessor);\r\n            }\r\n        };\r\n\r\n        loadAttribute(\"POSITION\", VertexBuffer.PositionKind);\r\n        loadAttribute(\"NORMAL\", VertexBuffer.NormalKind);\r\n        loadAttribute(\"TANGENT\", VertexBuffer.TangentKind);\r\n        loadAttribute(\"TEXCOORD_0\", VertexBuffer.UVKind);\r\n        loadAttribute(\"TEXCOORD_1\", VertexBuffer.UV2Kind);\r\n        loadAttribute(\"TEXCOORD_2\", VertexBuffer.UV3Kind);\r\n        loadAttribute(\"TEXCOORD_3\", VertexBuffer.UV4Kind);\r\n        loadAttribute(\"TEXCOORD_4\", VertexBuffer.UV5Kind);\r\n        loadAttribute(\"TEXCOORD_5\", VertexBuffer.UV6Kind);\r\n        loadAttribute(\"JOINTS_0\", VertexBuffer.MatricesIndicesKind);\r\n        loadAttribute(\"WEIGHTS_0\", VertexBuffer.MatricesWeightsKind);\r\n        loadAttribute(\"JOINTS_1\", VertexBuffer.MatricesIndicesExtraKind);\r\n        loadAttribute(\"WEIGHTS_1\", VertexBuffer.MatricesWeightsExtraKind);\r\n        loadAttribute(\"COLOR_0\", VertexBuffer.ColorKind, (accessor) => {\r\n            if (accessor.type === AccessorType.VEC4) {\r\n                babylonMesh.hasVertexAlpha = true;\r\n            }\r\n        });\r\n\r\n        return Promise.all(promises).then(() => {\r\n            return babylonGeometry;\r\n        });\r\n    }\r\n\r\n    private _createMorphTargets(context: string, node: INode, mesh: IMesh, primitive: IMeshPrimitive, babylonMesh: Mesh): void {\r\n        if (!primitive.targets) {\r\n            return;\r\n        }\r\n\r\n        if (node._numMorphTargets == undefined) {\r\n            node._numMorphTargets = primitive.targets.length;\r\n        } else if (primitive.targets.length !== node._numMorphTargets) {\r\n            throw new Error(`${context}: Primitives do not have the same number of targets`);\r\n        }\r\n\r\n        const targetNames = mesh.extras ? mesh.extras.targetNames : null;\r\n\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        babylonMesh.morphTargetManager = new MorphTargetManager(this._babylonScene);\r\n        babylonMesh.morphTargetManager._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n\r\n        babylonMesh.morphTargetManager.areUpdatesFrozen = true;\r\n\r\n        for (let index = 0; index < primitive.targets.length; index++) {\r\n            const weight = node.weights ? node.weights[index] : mesh.weights ? mesh.weights[index] : 0;\r\n            const name = targetNames ? targetNames[index] : `morphTarget${index}`;\r\n            babylonMesh.morphTargetManager.addTarget(new MorphTarget(name, weight, babylonMesh.getScene()));\r\n            // TODO: tell the target whether it has positions, normals, tangents\r\n        }\r\n    }\r\n\r\n    private _loadMorphTargetsAsync(context: string, primitive: IMeshPrimitive, babylonMesh: Mesh, babylonGeometry: Geometry): Promise<void> {\r\n        if (!primitive.targets) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        const morphTargetManager = babylonMesh.morphTargetManager!;\r\n        for (let index = 0; index < morphTargetManager.numTargets; index++) {\r\n            const babylonMorphTarget = morphTargetManager.getTarget(index);\r\n            promises.push(this._loadMorphTargetVertexDataAsync(`${context}/targets/${index}`, babylonGeometry, primitive.targets[index], babylonMorphTarget));\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {\r\n            morphTargetManager.areUpdatesFrozen = false;\r\n        });\r\n    }\r\n\r\n    private _loadMorphTargetVertexDataAsync(context: string, babylonGeometry: Geometry, attributes: { [name: string]: number }, babylonMorphTarget: MorphTarget): Promise<void> {\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        const loadAttribute = (attribute: string, kind: string, setData: (babylonVertexBuffer: VertexBuffer, data: Float32Array) => void) => {\r\n            if (attributes[attribute] == undefined) {\r\n                return;\r\n            }\r\n\r\n            const babylonVertexBuffer = babylonGeometry.getVertexBuffer(kind);\r\n            if (!babylonVertexBuffer) {\r\n                return;\r\n            }\r\n\r\n            const accessor = ArrayItem.Get(`${context}/${attribute}`, this._gltf.accessors, attributes[attribute]);\r\n            promises.push(\r\n                this._loadFloatAccessorAsync(`/accessors/${accessor.index}`, accessor).then((data) => {\r\n                    setData(babylonVertexBuffer, data);\r\n                })\r\n            );\r\n        };\r\n\r\n        loadAttribute(\"POSITION\", VertexBuffer.PositionKind, (babylonVertexBuffer, data) => {\r\n            const positions = new Float32Array(data.length);\r\n            babylonVertexBuffer.forEach(data.length, (value, index) => {\r\n                positions[index] = data[index] + value;\r\n            });\r\n\r\n            babylonMorphTarget.setPositions(positions);\r\n        });\r\n\r\n        loadAttribute(\"NORMAL\", VertexBuffer.NormalKind, (babylonVertexBuffer, data) => {\r\n            const normals = new Float32Array(data.length);\r\n            babylonVertexBuffer.forEach(normals.length, (value, index) => {\r\n                normals[index] = data[index] + value;\r\n            });\r\n\r\n            babylonMorphTarget.setNormals(normals);\r\n        });\r\n\r\n        loadAttribute(\"TANGENT\", VertexBuffer.TangentKind, (babylonVertexBuffer, data) => {\r\n            const tangents = new Float32Array((data.length / 3) * 4);\r\n            let dataIndex = 0;\r\n            babylonVertexBuffer.forEach((data.length / 3) * 4, (value, index) => {\r\n                // Tangent data for morph targets is stored as xyz delta.\r\n                // The vertexData.tangent is stored as xyzw.\r\n                // So we need to skip every fourth vertexData.tangent.\r\n                if ((index + 1) % 4 !== 0) {\r\n                    tangents[dataIndex] = data[dataIndex] + value;\r\n                    dataIndex++;\r\n                }\r\n            });\r\n            babylonMorphTarget.setTangents(tangents);\r\n        });\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    private static _LoadTransform(node: INode, babylonNode: TransformNode): void {\r\n        // Ignore the TRS of skinned nodes.\r\n        // See https://github.com/KhronosGroup/glTF/tree/master/specification/2.0#skins (second implementation note)\r\n        if (node.skin != undefined) {\r\n            return;\r\n        }\r\n\r\n        let position = Vector3.Zero();\r\n        let rotation = Quaternion.Identity();\r\n        let scaling = Vector3.One();\r\n\r\n        if (node.matrix) {\r\n            const matrix = Matrix.FromArray(node.matrix);\r\n            matrix.decompose(scaling, rotation, position);\r\n        } else {\r\n            if (node.translation) {\r\n                position = Vector3.FromArray(node.translation);\r\n            }\r\n            if (node.rotation) {\r\n                rotation = Quaternion.FromArray(node.rotation);\r\n            }\r\n            if (node.scale) {\r\n                scaling = Vector3.FromArray(node.scale);\r\n            }\r\n        }\r\n\r\n        babylonNode.position = position;\r\n        babylonNode.rotationQuaternion = rotation;\r\n        babylonNode.scaling = scaling;\r\n    }\r\n\r\n    private _loadSkinAsync(context: string, node: INode, skin: ISkin, assign: (babylonSkeleton: Skeleton) => void): Promise<void> {\r\n        const extensionPromise = this._extensionsLoadSkinAsync(context, node, skin);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        if (skin._data) {\r\n            assign(skin._data.babylonSkeleton);\r\n            return skin._data.promise;\r\n        }\r\n\r\n        const skeletonId = `skeleton${skin.index}`;\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        const babylonSkeleton = new Skeleton(skin.name || skeletonId, skeletonId, this._babylonScene);\r\n        babylonSkeleton._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n\r\n        this._loadBones(context, skin, babylonSkeleton);\r\n        const promise = this._loadSkinInverseBindMatricesDataAsync(context, skin).then((inverseBindMatricesData) => {\r\n            this._updateBoneMatrices(babylonSkeleton, inverseBindMatricesData);\r\n        });\r\n\r\n        skin._data = {\r\n            babylonSkeleton: babylonSkeleton,\r\n            promise: promise,\r\n        };\r\n\r\n        assign(babylonSkeleton);\r\n\r\n        return promise;\r\n    }\r\n\r\n    private _loadBones(context: string, skin: ISkin, babylonSkeleton: Skeleton): void {\r\n        if (skin.skeleton == undefined || this._parent.alwaysComputeSkeletonRootNode) {\r\n            const rootNode = this._findSkeletonRootNode(`${context}/joints`, skin.joints);\r\n            if (rootNode) {\r\n                if (skin.skeleton === undefined) {\r\n                    skin.skeleton = rootNode.index;\r\n                } else {\r\n                    const isParent = (a: INode, b: INode): boolean => {\r\n                        for (; b.parent; b = b.parent) {\r\n                            if (b.parent === a) {\r\n                                return true;\r\n                            }\r\n                        }\r\n\r\n                        return false;\r\n                    };\r\n\r\n                    const skeletonNode = ArrayItem.Get(`${context}/skeleton`, this._gltf.nodes, skin.skeleton);\r\n                    if (skeletonNode !== rootNode && !isParent(skeletonNode, rootNode)) {\r\n                        Logger.Warn(`${context}/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root`);\r\n                        skin.skeleton = rootNode.index;\r\n                    }\r\n                }\r\n            } else {\r\n                Logger.Warn(`${context}: Failed to find common root`);\r\n            }\r\n        }\r\n\r\n        const babylonBones: { [index: number]: Bone } = {};\r\n        for (const index of skin.joints) {\r\n            const node = ArrayItem.Get(`${context}/joints/${index}`, this._gltf.nodes, index);\r\n            this._loadBone(node, skin, babylonSkeleton, babylonBones);\r\n        }\r\n    }\r\n\r\n    private _findSkeletonRootNode(context: string, joints: Array<number>): Nullable<INode> {\r\n        if (joints.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        const paths: { [joint: number]: Array<INode> } = {};\r\n        for (const index of joints) {\r\n            const path = new Array<INode>();\r\n            let node = ArrayItem.Get(`${context}/${index}`, this._gltf.nodes, index);\r\n            while (node.index !== -1) {\r\n                path.unshift(node);\r\n                node = node.parent!;\r\n            }\r\n            paths[index] = path;\r\n        }\r\n\r\n        let rootNode: Nullable<INode> = null;\r\n        for (let i = 0; ; ++i) {\r\n            let path = paths[joints[0]];\r\n            if (i >= path.length) {\r\n                return rootNode;\r\n            }\r\n\r\n            const node = path[i];\r\n            for (let j = 1; j < joints.length; ++j) {\r\n                path = paths[joints[j]];\r\n                if (i >= path.length || node !== path[i]) {\r\n                    return rootNode;\r\n                }\r\n            }\r\n\r\n            rootNode = node;\r\n        }\r\n    }\r\n\r\n    private _loadBone(node: INode, skin: ISkin, babylonSkeleton: Skeleton, babylonBones: { [index: number]: Bone }): Bone {\r\n        let babylonBone = babylonBones[node.index];\r\n        if (babylonBone) {\r\n            return babylonBone;\r\n        }\r\n\r\n        let parentBabylonBone: Nullable<Bone> = null;\r\n        if (node.index !== skin.skeleton) {\r\n            if (node.parent && node.parent.index !== -1) {\r\n                parentBabylonBone = this._loadBone(node.parent, skin, babylonSkeleton, babylonBones);\r\n            } else if (skin.skeleton !== undefined) {\r\n                Logger.Warn(`/skins/${skin.index}/skeleton: Skeleton node is not a common root`);\r\n            }\r\n        }\r\n\r\n        const boneIndex = skin.joints.indexOf(node.index);\r\n        babylonBone = new Bone(node.name || `joint${node.index}`, babylonSkeleton, parentBabylonBone, this._getNodeMatrix(node), null, null, boneIndex);\r\n        babylonBones[node.index] = babylonBone;\r\n\r\n        // Wait until the scene is loaded to ensure the transform nodes are loaded.\r\n        this._postSceneLoadActions.push(() => {\r\n            // Link the Babylon bone with the corresponding Babylon transform node.\r\n            // A glTF joint is a pointer to a glTF node in the glTF node hierarchy similar to Unity3D.\r\n            babylonBone.linkTransformNode(node._babylonTransformNode!);\r\n        });\r\n\r\n        return babylonBone;\r\n    }\r\n\r\n    private _loadSkinInverseBindMatricesDataAsync(context: string, skin: ISkin): Promise<Nullable<Float32Array>> {\r\n        if (skin.inverseBindMatrices == undefined) {\r\n            return Promise.resolve(null);\r\n        }\r\n\r\n        const accessor = ArrayItem.Get(`${context}/inverseBindMatrices`, this._gltf.accessors, skin.inverseBindMatrices);\r\n        return this._loadFloatAccessorAsync(`/accessors/${accessor.index}`, accessor);\r\n    }\r\n\r\n    private _updateBoneMatrices(babylonSkeleton: Skeleton, inverseBindMatricesData: Nullable<Float32Array>): void {\r\n        for (const babylonBone of babylonSkeleton.bones) {\r\n            const baseMatrix = Matrix.Identity();\r\n            const boneIndex = babylonBone._index!;\r\n            if (inverseBindMatricesData && boneIndex !== -1) {\r\n                Matrix.FromArrayToRef(inverseBindMatricesData, boneIndex * 16, baseMatrix);\r\n                baseMatrix.invertToRef(baseMatrix);\r\n            }\r\n\r\n            const babylonParentBone = babylonBone.getParent();\r\n            if (babylonParentBone) {\r\n                baseMatrix.multiplyToRef(babylonParentBone.getInvertedAbsoluteTransform(), baseMatrix);\r\n            }\r\n\r\n            babylonBone.updateMatrix(baseMatrix, false, false);\r\n            babylonBone._updateDifferenceMatrix(undefined, false);\r\n        }\r\n    }\r\n\r\n    private _getNodeMatrix(node: INode): Matrix {\r\n        return node.matrix\r\n            ? Matrix.FromArray(node.matrix)\r\n            : Matrix.Compose(\r\n                  node.scale ? Vector3.FromArray(node.scale) : Vector3.One(),\r\n                  node.rotation ? Quaternion.FromArray(node.rotation) : Quaternion.Identity(),\r\n                  node.translation ? Vector3.FromArray(node.translation) : Vector3.Zero()\r\n              );\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF camera.\r\n     * @param context The context when loading the asset\r\n     * @param camera The glTF camera property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon camera when the load is complete\r\n     */\r\n    public loadCameraAsync(context: string, camera: ICamera, assign: (babylonCamera: Camera) => void = () => {}): Promise<Camera> {\r\n        const extensionPromise = this._extensionsLoadCameraAsync(context, camera, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        this.logOpen(`${context} ${camera.name || \"\"}`);\r\n\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        const babylonCamera = new FreeCamera(camera.name || `camera${camera.index}`, Vector3.Zero(), this._babylonScene, false);\r\n        babylonCamera._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n        babylonCamera.ignoreParentScaling = true;\r\n        camera._babylonCamera = babylonCamera;\r\n\r\n        babylonCamera.rotation = new Vector3(0, Math.PI, 0);\r\n\r\n        switch (camera.type) {\r\n            case CameraType.PERSPECTIVE: {\r\n                const perspective = camera.perspective;\r\n                if (!perspective) {\r\n                    throw new Error(`${context}: Camera perspective properties are missing`);\r\n                }\r\n\r\n                babylonCamera.fov = perspective.yfov;\r\n                babylonCamera.minZ = perspective.znear;\r\n                babylonCamera.maxZ = perspective.zfar || 0;\r\n                break;\r\n            }\r\n            case CameraType.ORTHOGRAPHIC: {\r\n                if (!camera.orthographic) {\r\n                    throw new Error(`${context}: Camera orthographic properties are missing`);\r\n                }\r\n\r\n                babylonCamera.mode = Camera.ORTHOGRAPHIC_CAMERA;\r\n                babylonCamera.orthoLeft = -camera.orthographic.xmag;\r\n                babylonCamera.orthoRight = camera.orthographic.xmag;\r\n                babylonCamera.orthoBottom = -camera.orthographic.ymag;\r\n                babylonCamera.orthoTop = camera.orthographic.ymag;\r\n                babylonCamera.minZ = camera.orthographic.znear;\r\n                babylonCamera.maxZ = camera.orthographic.zfar;\r\n                break;\r\n            }\r\n            default: {\r\n                throw new Error(`${context}: Invalid camera type (${camera.type})`);\r\n            }\r\n        }\r\n\r\n        GLTFLoader.AddPointerMetadata(babylonCamera, context);\r\n        this._parent.onCameraLoadedObservable.notifyObservers(babylonCamera);\r\n        assign(babylonCamera);\r\n\r\n        this.logClose();\r\n\r\n        return Promise.all(promises).then(() => {\r\n            return babylonCamera;\r\n        });\r\n    }\r\n\r\n    private _loadAnimationsAsync(): Promise<void> {\r\n        const animations = this._gltf.animations;\r\n        if (!animations) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        const promises = new Array<Promise<void>>();\r\n\r\n        for (let index = 0; index < animations.length; index++) {\r\n            const animation = animations[index];\r\n            promises.push(\r\n                this.loadAnimationAsync(`/animations/${animation.index}`, animation).then((animationGroup) => {\r\n                    // Delete the animation group if it ended up not having any animations in it.\r\n                    if (animationGroup.targetedAnimations.length === 0) {\r\n                        animationGroup.dispose();\r\n                    }\r\n                })\r\n            );\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF animation.\r\n     * @param context The context when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @returns A promise that resolves with the loaded Babylon animation group when the load is complete\r\n     */\r\n    public loadAnimationAsync(context: string, animation: IAnimation): Promise<AnimationGroup> {\r\n        const promise = this._extensionsLoadAnimationAsync(context, animation);\r\n        if (promise) {\r\n            return promise;\r\n        }\r\n\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        const babylonAnimationGroup = new AnimationGroup(animation.name || `animation${animation.index}`, this._babylonScene);\r\n        babylonAnimationGroup._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n        animation._babylonAnimationGroup = babylonAnimationGroup;\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        ArrayItem.Assign(animation.channels);\r\n        ArrayItem.Assign(animation.samplers);\r\n\r\n        for (const channel of animation.channels) {\r\n            promises.push(\r\n                this._loadAnimationChannelAsync(`${context}/channels/${channel.index}`, context, animation, channel, (babylonTarget, babylonAnimation) => {\r\n                    babylonTarget.animations = babylonTarget.animations || [];\r\n                    babylonTarget.animations.push(babylonAnimation);\r\n                    babylonAnimationGroup.addTargetedAnimation(babylonAnimation, babylonTarget);\r\n                })\r\n            );\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {\r\n            babylonAnimationGroup.normalize(0);\r\n            return babylonAnimationGroup;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Loads a glTF animation channel.\r\n     * @param context The context when loading the asset\r\n     * @param animationContext The context of the animation when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @param channel The glTF animation channel property\r\n     * @param onLoad Called for each animation loaded\r\n     * @returns A void promise that resolves when the load is complete\r\n     */\r\n    public _loadAnimationChannelAsync(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        channel: IAnimationChannel,\r\n        onLoad: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): Promise<void> {\r\n        const promise = this._extensionsLoadAnimationChannelAsync(context, animationContext, animation, channel, onLoad);\r\n        if (promise) {\r\n            return promise;\r\n        }\r\n\r\n        if (channel.target.node == undefined) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        const targetNode = ArrayItem.Get(`${context}/target/node`, this._gltf.nodes, channel.target.node);\r\n\r\n        // Ignore animations that have no animation targets.\r\n        if (\r\n            (channel.target.path === AnimationChannelTargetPath.WEIGHTS && !targetNode._numMorphTargets) ||\r\n            (channel.target.path !== AnimationChannelTargetPath.WEIGHTS && !targetNode._babylonTransformNode)\r\n        ) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        let properties: Array<AnimationPropertyInfo>;\r\n        switch (channel.target.path) {\r\n            case AnimationChannelTargetPath.TRANSLATION: {\r\n                properties = nodeAnimationData.translation;\r\n                break;\r\n            }\r\n            case AnimationChannelTargetPath.ROTATION: {\r\n                properties = nodeAnimationData.rotation;\r\n                break;\r\n            }\r\n            case AnimationChannelTargetPath.SCALE: {\r\n                properties = nodeAnimationData.scale;\r\n                break;\r\n            }\r\n            case AnimationChannelTargetPath.WEIGHTS: {\r\n                properties = nodeAnimationData.weights;\r\n                break;\r\n            }\r\n            default: {\r\n                throw new Error(`${context}/target/path: Invalid value (${channel.target.path})`);\r\n            }\r\n        }\r\n\r\n        const targetInfo: IAnimationTargetInfo = {\r\n            target: targetNode,\r\n            properties: properties,\r\n        };\r\n\r\n        return this._loadAnimationChannelFromTargetInfoAsync(context, animationContext, animation, channel, targetInfo, onLoad);\r\n    }\r\n\r\n    /**\r\n     * @hidden\r\n     * Loads a glTF animation channel.\r\n     * @param context The context when loading the asset\r\n     * @param animationContext The context of the animation when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @param channel The glTF animation channel property\r\n     * @param targetInfo The glTF target and properties\r\n     * @param onLoad Called for each animation loaded\r\n     * @returns A void promise that resolves when the load is complete\r\n     */\r\n    public _loadAnimationChannelFromTargetInfoAsync(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        channel: IAnimationChannel,\r\n        targetInfo: IAnimationTargetInfo,\r\n        onLoad: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): Promise<void> {\r\n        const fps = this.parent.targetFps;\r\n        const invfps = 1 / fps;\r\n\r\n        const sampler = ArrayItem.Get(`${context}/sampler`, animation.samplers, channel.sampler);\r\n        return this._loadAnimationSamplerAsync(`${animationContext}/samplers/${channel.sampler}`, sampler).then((data) => {\r\n            let numAnimations = 0;\r\n\r\n            // Extract the corresponding values from the read value.\r\n            // GLTF values may be dispatched to several Babylon properties.\r\n            // For example, baseColorFactor [`r`, `g`, `b`, `a`] is dispatched to\r\n            // - albedoColor as Color3(`r`, `g`, `b`)\r\n            // - alpha as `a`\r\n            for (const property of targetInfo.properties) {\r\n                const stride = property.getStride(targetInfo.target);\r\n                const input = data.input;\r\n                const output = data.output;\r\n                const keys = new Array<IAnimationKey>(input.length);\r\n                let outputOffset = 0;\r\n\r\n                switch (data.interpolation) {\r\n                    case AnimationSamplerInterpolation.STEP: {\r\n                        for (let index = 0; index < input.length; index++) {\r\n                            const value = property.getValue(targetInfo.target, output, outputOffset, 1);\r\n                            outputOffset += stride;\r\n\r\n                            keys[index] = {\r\n                                frame: input[index] * fps,\r\n                                value: value,\r\n                                interpolation: AnimationKeyInterpolation.STEP,\r\n                            };\r\n                        }\r\n                        break;\r\n                    }\r\n                    case AnimationSamplerInterpolation.CUBICSPLINE: {\r\n                        for (let index = 0; index < input.length; index++) {\r\n                            const inTangent = property.getValue(targetInfo.target, output, outputOffset, invfps);\r\n                            outputOffset += stride;\r\n                            const value = property.getValue(targetInfo.target, output, outputOffset, 1);\r\n                            outputOffset += stride;\r\n                            const outTangent = property.getValue(targetInfo.target, output, outputOffset, invfps);\r\n                            outputOffset += stride;\r\n\r\n                            keys[index] = {\r\n                                frame: input[index] * fps,\r\n                                inTangent: inTangent,\r\n                                value: value,\r\n                                outTangent: outTangent,\r\n                            };\r\n                        }\r\n                        break;\r\n                    }\r\n                    case AnimationSamplerInterpolation.LINEAR: {\r\n                        for (let index = 0; index < input.length; index++) {\r\n                            const value = property.getValue(targetInfo.target, output, outputOffset, 1);\r\n                            outputOffset += stride;\r\n\r\n                            keys[index] = {\r\n                                frame: input[index] * fps,\r\n                                value: value,\r\n                            };\r\n                        }\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                if (outputOffset > 0) {\r\n                    const name = `${animation.name || `animation${animation.index}`}_channel${channel.index}_${numAnimations}`;\r\n                    property.buildAnimations(targetInfo.target, name, fps, keys, (babylonAnimatable, babylonAnimation) => {\r\n                        ++numAnimations;\r\n                        onLoad(babylonAnimatable, babylonAnimation);\r\n                    });\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _loadAnimationSamplerAsync(context: string, sampler: IAnimationSampler): Promise<_IAnimationSamplerData> {\r\n        if (sampler._data) {\r\n            return sampler._data;\r\n        }\r\n\r\n        const interpolation = sampler.interpolation || AnimationSamplerInterpolation.LINEAR;\r\n        switch (interpolation) {\r\n            case AnimationSamplerInterpolation.STEP:\r\n            case AnimationSamplerInterpolation.LINEAR:\r\n            case AnimationSamplerInterpolation.CUBICSPLINE: {\r\n                break;\r\n            }\r\n            default: {\r\n                throw new Error(`${context}/interpolation: Invalid value (${sampler.interpolation})`);\r\n            }\r\n        }\r\n\r\n        const inputAccessor = ArrayItem.Get(`${context}/input`, this._gltf.accessors, sampler.input);\r\n        const outputAccessor = ArrayItem.Get(`${context}/output`, this._gltf.accessors, sampler.output);\r\n        sampler._data = Promise.all([\r\n            this._loadFloatAccessorAsync(`/accessors/${inputAccessor.index}`, inputAccessor),\r\n            this._loadFloatAccessorAsync(`/accessors/${outputAccessor.index}`, outputAccessor),\r\n        ]).then(([inputData, outputData]) => {\r\n            return {\r\n                input: inputData,\r\n                interpolation: interpolation,\r\n                output: outputData,\r\n            };\r\n        });\r\n\r\n        return sampler._data;\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF buffer.\r\n     * @param context The context when loading the asset\r\n     * @param buffer The glTF buffer property\r\n     * @param byteOffset The byte offset to use\r\n     * @param byteLength The byte length to use\r\n     * @returns A promise that resolves with the loaded data when the load is complete\r\n     */\r\n    public loadBufferAsync(context: string, buffer: IBuffer, byteOffset: number, byteLength: number): Promise<ArrayBufferView> {\r\n        const extensionPromise = this._extensionsLoadBufferAsync(context, buffer, byteOffset, byteLength);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        if (!buffer._data) {\r\n            if (buffer.uri) {\r\n                buffer._data = this.loadUriAsync(`${context}/uri`, buffer, buffer.uri);\r\n            } else {\r\n                if (!this._bin) {\r\n                    throw new Error(`${context}: Uri is missing or the binary glTF is missing its binary chunk`);\r\n                }\r\n\r\n                buffer._data = this._bin.readAsync(0, buffer.byteLength);\r\n            }\r\n        }\r\n\r\n        return buffer._data.then((data) => {\r\n            try {\r\n                return new Uint8Array(data.buffer, data.byteOffset + byteOffset, byteLength);\r\n            } catch (e) {\r\n                throw new Error(`${context}: ${e.message}`);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF buffer view.\r\n     * @param context The context when loading the asset\r\n     * @param bufferView The glTF buffer view property\r\n     * @returns A promise that resolves with the loaded data when the load is complete\r\n     */\r\n    public loadBufferViewAsync(context: string, bufferView: IBufferView): Promise<ArrayBufferView> {\r\n        const extensionPromise = this._extensionsLoadBufferViewAsync(context, bufferView);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        if (bufferView._data) {\r\n            return bufferView._data;\r\n        }\r\n\r\n        const buffer = ArrayItem.Get(`${context}/buffer`, this._gltf.buffers, bufferView.buffer);\r\n        bufferView._data = this.loadBufferAsync(`/buffers/${buffer.index}`, buffer, bufferView.byteOffset || 0, bufferView.byteLength);\r\n\r\n        return bufferView._data;\r\n    }\r\n\r\n    private _loadAccessorAsync(context: string, accessor: IAccessor, constructor: TypedArrayConstructor): Promise<ArrayBufferView> {\r\n        if (accessor._data) {\r\n            return accessor._data;\r\n        }\r\n\r\n        const numComponents = GLTFLoader._GetNumComponents(context, accessor.type);\r\n        const byteStride = numComponents * VertexBuffer.GetTypeByteLength(accessor.componentType);\r\n        const length = numComponents * accessor.count;\r\n\r\n        if (accessor.bufferView == undefined) {\r\n            accessor._data = Promise.resolve(new constructor(length));\r\n        } else {\r\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\r\n            accessor._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\r\n                if (accessor.componentType === AccessorComponentType.FLOAT && !accessor.normalized && (!bufferView.byteStride || bufferView.byteStride === byteStride)) {\r\n                    return GLTFLoader._GetTypedArray(context, accessor.componentType, data, accessor.byteOffset, length);\r\n                } else {\r\n                    const typedArray = new constructor(length);\r\n                    VertexBuffer.ForEach(\r\n                        data,\r\n                        accessor.byteOffset || 0,\r\n                        bufferView.byteStride || byteStride,\r\n                        numComponents,\r\n                        accessor.componentType,\r\n                        typedArray.length,\r\n                        accessor.normalized || false,\r\n                        (value, index) => {\r\n                            typedArray[index] = value;\r\n                        }\r\n                    );\r\n                    return typedArray;\r\n                }\r\n            });\r\n        }\r\n\r\n        if (accessor.sparse) {\r\n            const sparse = accessor.sparse;\r\n            accessor._data = accessor._data.then((data) => {\r\n                const typedArray = data as TypedArrayLike;\r\n                const indicesBufferView = ArrayItem.Get(`${context}/sparse/indices/bufferView`, this._gltf.bufferViews, sparse.indices.bufferView);\r\n                const valuesBufferView = ArrayItem.Get(`${context}/sparse/values/bufferView`, this._gltf.bufferViews, sparse.values.bufferView);\r\n                return Promise.all([\r\n                    this.loadBufferViewAsync(`/bufferViews/${indicesBufferView.index}`, indicesBufferView),\r\n                    this.loadBufferViewAsync(`/bufferViews/${valuesBufferView.index}`, valuesBufferView),\r\n                ]).then(([indicesData, valuesData]) => {\r\n                    const indices = GLTFLoader._GetTypedArray(\r\n                        `${context}/sparse/indices`,\r\n                        sparse.indices.componentType,\r\n                        indicesData,\r\n                        sparse.indices.byteOffset,\r\n                        sparse.count\r\n                    ) as IndicesArray;\r\n\r\n                    const sparseLength = numComponents * sparse.count;\r\n                    let values: TypedArrayLike;\r\n\r\n                    if (accessor.componentType === AccessorComponentType.FLOAT && !accessor.normalized) {\r\n                        values = GLTFLoader._GetTypedArray(`${context}/sparse/values`, accessor.componentType, valuesData, sparse.values.byteOffset, sparseLength);\r\n                    } else {\r\n                        const sparseData = GLTFLoader._GetTypedArray(`${context}/sparse/values`, accessor.componentType, valuesData, sparse.values.byteOffset, sparseLength);\r\n                        values = new constructor(sparseLength);\r\n                        VertexBuffer.ForEach(sparseData, 0, byteStride, numComponents, accessor.componentType, values.length, accessor.normalized || false, (value, index) => {\r\n                            values[index] = value;\r\n                        });\r\n                    }\r\n\r\n                    let valuesIndex = 0;\r\n                    for (let indicesIndex = 0; indicesIndex < indices.length; indicesIndex++) {\r\n                        let dataIndex = indices[indicesIndex] * numComponents;\r\n                        for (let componentIndex = 0; componentIndex < numComponents; componentIndex++) {\r\n                            typedArray[dataIndex++] = values[valuesIndex++];\r\n                        }\r\n                    }\r\n\r\n                    return typedArray;\r\n                });\r\n            });\r\n        }\r\n\r\n        return accessor._data;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFloatAccessorAsync(context: string, accessor: IAccessor): Promise<Float32Array> {\r\n        return this._loadAccessorAsync(context, accessor, Float32Array) as Promise<Float32Array>;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadIndicesAccessorAsync(context: string, accessor: IAccessor): Promise<IndicesArray> {\r\n        if (accessor.type !== AccessorType.SCALAR) {\r\n            throw new Error(`${context}/type: Invalid value ${accessor.type}`);\r\n        }\r\n\r\n        if (\r\n            accessor.componentType !== AccessorComponentType.UNSIGNED_BYTE &&\r\n            accessor.componentType !== AccessorComponentType.UNSIGNED_SHORT &&\r\n            accessor.componentType !== AccessorComponentType.UNSIGNED_INT\r\n        ) {\r\n            throw new Error(`${context}/componentType: Invalid value ${accessor.componentType}`);\r\n        }\r\n\r\n        if (accessor._data) {\r\n            return accessor._data as Promise<IndicesArray>;\r\n        }\r\n\r\n        if (accessor.sparse) {\r\n            const constructor = GLTFLoader._GetTypedArrayConstructor(`${context}/componentType`, accessor.componentType);\r\n            accessor._data = this._loadAccessorAsync(context, accessor, constructor);\r\n        } else {\r\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\r\n            accessor._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\r\n                return GLTFLoader._GetTypedArray(context, accessor.componentType, data, accessor.byteOffset, accessor.count);\r\n            });\r\n        }\r\n\r\n        return accessor._data as Promise<IndicesArray>;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadVertexBufferViewAsync(bufferView: IBufferView): Promise<Buffer> {\r\n        if (bufferView._babylonBuffer) {\r\n            return bufferView._babylonBuffer;\r\n        }\r\n\r\n        const engine = this._babylonScene.getEngine();\r\n        bufferView._babylonBuffer = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\r\n            return new Buffer(engine, data, false);\r\n        });\r\n\r\n        return bufferView._babylonBuffer;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadVertexAccessorAsync(context: string, accessor: IAccessor, kind: string): Promise<VertexBuffer> {\r\n        if (accessor._babylonVertexBuffer?.[kind]) {\r\n            return accessor._babylonVertexBuffer[kind];\r\n        }\r\n\r\n        if (!accessor._babylonVertexBuffer) {\r\n            accessor._babylonVertexBuffer = {};\r\n        }\r\n\r\n        const engine = this._babylonScene.getEngine();\r\n\r\n        if (accessor.sparse) {\r\n            accessor._babylonVertexBuffer[kind] = this._loadFloatAccessorAsync(context, accessor).then((data) => {\r\n                return new VertexBuffer(engine, data, kind, false);\r\n            });\r\n        }\r\n        // Load joint indices as a float array since the shaders expect float data but glTF uses unsigned byte/short.\r\n        // This prevents certain platforms (e.g. D3D) from having to convert the data to float on the fly.\r\n        else if (kind === VertexBuffer.MatricesIndicesKind || kind === VertexBuffer.MatricesIndicesExtraKind) {\r\n            accessor._babylonVertexBuffer[kind] = this._loadFloatAccessorAsync(context, accessor).then((data) => {\r\n                return new VertexBuffer(engine, data, kind, false);\r\n            });\r\n        } else {\r\n            const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, accessor.bufferView);\r\n            accessor._babylonVertexBuffer[kind] = this._loadVertexBufferViewAsync(bufferView).then((babylonBuffer) => {\r\n                const size = GLTFLoader._GetNumComponents(context, accessor.type);\r\n                return new VertexBuffer(\r\n                    engine,\r\n                    babylonBuffer,\r\n                    kind,\r\n                    false,\r\n                    false,\r\n                    bufferView.byteStride,\r\n                    false,\r\n                    accessor.byteOffset,\r\n                    size,\r\n                    accessor.componentType,\r\n                    accessor.normalized,\r\n                    true,\r\n                    1,\r\n                    true\r\n                );\r\n            });\r\n        }\r\n\r\n        return accessor._babylonVertexBuffer[kind];\r\n    }\r\n\r\n    private _loadMaterialMetallicRoughnessPropertiesAsync(context: string, properties: IMaterialPbrMetallicRoughness, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        if (properties) {\r\n            if (properties.baseColorFactor) {\r\n                babylonMaterial.albedoColor = Color3.FromArray(properties.baseColorFactor);\r\n                babylonMaterial.alpha = properties.baseColorFactor[3];\r\n            } else {\r\n                babylonMaterial.albedoColor = Color3.White();\r\n            }\r\n\r\n            babylonMaterial.metallic = properties.metallicFactor == undefined ? 1 : properties.metallicFactor;\r\n            babylonMaterial.roughness = properties.roughnessFactor == undefined ? 1 : properties.roughnessFactor;\r\n\r\n            if (properties.baseColorTexture) {\r\n                promises.push(\r\n                    this.loadTextureInfoAsync(`${context}/baseColorTexture`, properties.baseColorTexture, (texture) => {\r\n                        texture.name = `${babylonMaterial.name} (Base Color)`;\r\n                        babylonMaterial.albedoTexture = texture;\r\n                    })\r\n                );\r\n            }\r\n\r\n            if (properties.metallicRoughnessTexture) {\r\n                properties.metallicRoughnessTexture.nonColorData = true;\r\n                promises.push(\r\n                    this.loadTextureInfoAsync(`${context}/metallicRoughnessTexture`, properties.metallicRoughnessTexture, (texture) => {\r\n                        texture.name = `${babylonMaterial.name} (Metallic Roughness)`;\r\n                        babylonMaterial.metallicTexture = texture;\r\n                    })\r\n                );\r\n\r\n                babylonMaterial.useMetallnessFromMetallicTextureBlue = true;\r\n                babylonMaterial.useRoughnessFromMetallicTextureGreen = true;\r\n                babylonMaterial.useRoughnessFromMetallicTextureAlpha = false;\r\n            }\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadMaterialAsync(\r\n        context: string,\r\n        material: IMaterial,\r\n        babylonMesh: Nullable<Mesh>,\r\n        babylonDrawMode: number,\r\n        assign: (babylonMaterial: Material) => void = () => {}\r\n    ): Promise<Material> {\r\n        const extensionPromise = this._extensionsLoadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        material._data = material._data || {};\r\n        let babylonData = material._data[babylonDrawMode];\r\n        if (!babylonData) {\r\n            this.logOpen(`${context} ${material.name || \"\"}`);\r\n\r\n            const babylonMaterial = this.createMaterial(context, material, babylonDrawMode);\r\n\r\n            babylonData = {\r\n                babylonMaterial: babylonMaterial,\r\n                babylonMeshes: [],\r\n                promise: this.loadMaterialPropertiesAsync(context, material, babylonMaterial),\r\n            };\r\n\r\n            material._data[babylonDrawMode] = babylonData;\r\n\r\n            GLTFLoader.AddPointerMetadata(babylonMaterial, context);\r\n            this._parent.onMaterialLoadedObservable.notifyObservers(babylonMaterial);\r\n\r\n            this.logClose();\r\n        }\r\n\r\n        if (babylonMesh) {\r\n            babylonData.babylonMeshes.push(babylonMesh);\r\n\r\n            babylonMesh.onDisposeObservable.addOnce(() => {\r\n                const index = babylonData.babylonMeshes.indexOf(babylonMesh);\r\n                if (index !== -1) {\r\n                    babylonData.babylonMeshes.splice(index, 1);\r\n                }\r\n            });\r\n        }\r\n\r\n        assign(babylonData.babylonMaterial);\r\n\r\n        return babylonData.promise.then(() => {\r\n            return babylonData.babylonMaterial;\r\n        });\r\n    }\r\n\r\n    private _createDefaultMaterial(name: string, babylonDrawMode: number): Material {\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        const babylonMaterial = new PBRMaterial(name, this._babylonScene);\r\n        babylonMaterial._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n        // Moved to mesh so user can change materials on gltf meshes: babylonMaterial.sideOrientation = this._babylonScene.useRightHandedSystem ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\r\n        babylonMaterial.fillMode = babylonDrawMode;\r\n        babylonMaterial.enableSpecularAntiAliasing = true;\r\n        babylonMaterial.useRadianceOverAlpha = !this._parent.transparencyAsCoverage;\r\n        babylonMaterial.useSpecularOverAlpha = !this._parent.transparencyAsCoverage;\r\n        babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_OPAQUE;\r\n        babylonMaterial.metallic = 1;\r\n        babylonMaterial.roughness = 1;\r\n        return babylonMaterial;\r\n    }\r\n\r\n    /**\r\n     * Creates a Babylon material from a glTF material.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonDrawMode The draw mode for the Babylon material\r\n     * @returns The Babylon material\r\n     */\r\n    public createMaterial(context: string, material: IMaterial, babylonDrawMode: number): Material {\r\n        const extensionPromise = this._extensionsCreateMaterial(context, material, babylonDrawMode);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        const name = material.name || `material${material.index}`;\r\n        const babylonMaterial = this._createDefaultMaterial(name, babylonDrawMode);\r\n\r\n        return babylonMaterial;\r\n    }\r\n\r\n    /**\r\n     * Loads properties from a glTF material into a Babylon material.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonMaterial The Babylon material\r\n     * @returns A promise that resolves when the load is complete\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Promise<void> {\r\n        const extensionPromise = this._extensionsLoadMaterialPropertiesAsync(context, material, babylonMaterial);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        promises.push(this.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\r\n\r\n        if (material.pbrMetallicRoughness) {\r\n            promises.push(this._loadMaterialMetallicRoughnessPropertiesAsync(`${context}/pbrMetallicRoughness`, material.pbrMetallicRoughness, babylonMaterial));\r\n        }\r\n\r\n        this.loadMaterialAlphaProperties(context, material, babylonMaterial);\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    /**\r\n     * Loads the normal, occlusion, and emissive properties from a glTF material into a Babylon material.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonMaterial The Babylon material\r\n     * @returns A promise that resolves when the load is complete\r\n     */\r\n    public loadMaterialBasePropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        babylonMaterial.emissiveColor = material.emissiveFactor ? Color3.FromArray(material.emissiveFactor) : new Color3(0, 0, 0);\r\n        if (material.doubleSided) {\r\n            babylonMaterial.backFaceCulling = false;\r\n            babylonMaterial.twoSidedLighting = true;\r\n        }\r\n\r\n        if (material.normalTexture) {\r\n            material.normalTexture.nonColorData = true;\r\n            promises.push(\r\n                this.loadTextureInfoAsync(`${context}/normalTexture`, material.normalTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Normal)`;\r\n                    babylonMaterial.bumpTexture = texture;\r\n                })\r\n            );\r\n\r\n            babylonMaterial.invertNormalMapX = !this._babylonScene.useRightHandedSystem;\r\n            babylonMaterial.invertNormalMapY = this._babylonScene.useRightHandedSystem;\r\n            if (material.normalTexture.scale != undefined && babylonMaterial.bumpTexture) {\r\n                babylonMaterial.bumpTexture.level = material.normalTexture.scale;\r\n            }\r\n\r\n            babylonMaterial.forceIrradianceInFragment = true;\r\n        }\r\n\r\n        if (material.occlusionTexture) {\r\n            material.occlusionTexture.nonColorData = true;\r\n            promises.push(\r\n                this.loadTextureInfoAsync(`${context}/occlusionTexture`, material.occlusionTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Occlusion)`;\r\n                    babylonMaterial.ambientTexture = texture;\r\n                })\r\n            );\r\n\r\n            babylonMaterial.useAmbientInGrayScale = true;\r\n            if (material.occlusionTexture.strength != undefined) {\r\n                babylonMaterial.ambientTextureStrength = material.occlusionTexture.strength;\r\n            }\r\n        }\r\n\r\n        if (material.emissiveTexture) {\r\n            promises.push(\r\n                this.loadTextureInfoAsync(`${context}/emissiveTexture`, material.emissiveTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Emissive)`;\r\n                    babylonMaterial.emissiveTexture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n\r\n    /**\r\n     * Loads the alpha properties from a glTF material into a Babylon material.\r\n     * Must be called after the setting the albedo texture of the Babylon material when the material has an albedo texture.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonMaterial The Babylon material\r\n     */\r\n    public loadMaterialAlphaProperties(context: string, material: IMaterial, babylonMaterial: Material): void {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const alphaMode = material.alphaMode || MaterialAlphaMode.OPAQUE;\r\n        switch (alphaMode) {\r\n            case MaterialAlphaMode.OPAQUE: {\r\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_OPAQUE;\r\n                break;\r\n            }\r\n            case MaterialAlphaMode.MASK: {\r\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_ALPHATEST;\r\n                babylonMaterial.alphaCutOff = material.alphaCutoff == undefined ? 0.5 : material.alphaCutoff;\r\n                if (babylonMaterial.albedoTexture) {\r\n                    babylonMaterial.albedoTexture.hasAlpha = true;\r\n                }\r\n                break;\r\n            }\r\n            case MaterialAlphaMode.BLEND: {\r\n                babylonMaterial.transparencyMode = PBRMaterial.PBRMATERIAL_ALPHABLEND;\r\n                if (babylonMaterial.albedoTexture) {\r\n                    babylonMaterial.albedoTexture.hasAlpha = true;\r\n                    babylonMaterial.useAlphaFromAlbedoTexture = true;\r\n                }\r\n                break;\r\n            }\r\n            default: {\r\n                throw new Error(`${context}/alphaMode: Invalid value (${material.alphaMode})`);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF texture info.\r\n     * @param context The context when loading the asset\r\n     * @param textureInfo The glTF texture info property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon texture when the load is complete\r\n     */\r\n    public loadTextureInfoAsync(context: string, textureInfo: ITextureInfo, assign: (babylonTexture: BaseTexture) => void = () => {}): Promise<BaseTexture> {\r\n        const extensionPromise = this._extensionsLoadTextureInfoAsync(context, textureInfo, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        this.logOpen(`${context}`);\r\n\r\n        if (textureInfo.texCoord! >= 6) {\r\n            throw new Error(`${context}/texCoord: Invalid value (${textureInfo.texCoord})`);\r\n        }\r\n\r\n        const texture = ArrayItem.Get(`${context}/index`, this._gltf.textures, textureInfo.index);\r\n        texture._textureInfo = textureInfo;\r\n\r\n        const promise = this._loadTextureAsync(`/textures/${textureInfo.index}`, texture, (babylonTexture) => {\r\n            babylonTexture.coordinatesIndex = textureInfo.texCoord || 0;\r\n            GLTFLoader.AddPointerMetadata(babylonTexture, context);\r\n            this._parent.onTextureLoadedObservable.notifyObservers(babylonTexture);\r\n            assign(babylonTexture);\r\n        });\r\n\r\n        this.logClose();\r\n\r\n        return promise;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadTextureAsync(context: string, texture: ITexture, assign: (babylonTexture: BaseTexture) => void = () => {}): Promise<BaseTexture> {\r\n        const extensionPromise = this._extensionsLoadTextureAsync(context, texture, assign);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        this.logOpen(`${context} ${texture.name || \"\"}`);\r\n\r\n        const sampler = texture.sampler == undefined ? GLTFLoader.DefaultSampler : ArrayItem.Get(`${context}/sampler`, this._gltf.samplers, texture.sampler);\r\n        const image = ArrayItem.Get(`${context}/source`, this._gltf.images, texture.source);\r\n        const promise = this._createTextureAsync(context, sampler, image, assign, undefined, !texture._textureInfo.nonColorData);\r\n\r\n        this.logClose();\r\n\r\n        return promise;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createTextureAsync(\r\n        context: string,\r\n        sampler: ISampler,\r\n        image: IImage,\r\n        assign: (babylonTexture: BaseTexture) => void = () => {},\r\n        textureLoaderOptions?: any,\r\n        useSRGBBuffer?: boolean\r\n    ): Promise<BaseTexture> {\r\n        const samplerData = this._loadSampler(`/samplers/${sampler.index}`, sampler);\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        const deferred = new Deferred<void>();\r\n        this._babylonScene._blockEntityCollection = !!this._assetContainer;\r\n        const textureCreationOptions: ITextureCreationOptions = {\r\n            noMipmap: samplerData.noMipMaps,\r\n            invertY: false,\r\n            samplingMode: samplerData.samplingMode,\r\n            onLoad: () => {\r\n                if (!this._disposed) {\r\n                    deferred.resolve();\r\n                }\r\n            },\r\n            onError: (message?: string, exception?: any) => {\r\n                if (!this._disposed) {\r\n                    deferred.reject(new Error(`${context}: ${exception && exception.message ? exception.message : message || \"Failed to load texture\"}`));\r\n                }\r\n            },\r\n            mimeType: image.mimeType,\r\n            loaderOptions: textureLoaderOptions,\r\n            useSRGBBuffer: !!useSRGBBuffer && this._parent.useSRGBBuffers,\r\n        };\r\n        const babylonTexture = new Texture(null, this._babylonScene, textureCreationOptions);\r\n        babylonTexture._parentContainer = this._assetContainer;\r\n        this._babylonScene._blockEntityCollection = false;\r\n        promises.push(deferred.promise);\r\n\r\n        promises.push(\r\n            this.loadImageAsync(`/images/${image.index}`, image).then((data) => {\r\n                const name = image.uri || `${this._fileName}#image${image.index}`;\r\n                const dataUrl = `data:${this._uniqueRootUrl}${name}`;\r\n                babylonTexture.updateURL(dataUrl, data);\r\n            })\r\n        );\r\n\r\n        babylonTexture.wrapU = samplerData.wrapU;\r\n        babylonTexture.wrapV = samplerData.wrapV;\r\n        assign(babylonTexture);\r\n\r\n        return Promise.all(promises).then(() => {\r\n            return babylonTexture;\r\n        });\r\n    }\r\n\r\n    private _loadSampler(context: string, sampler: ISampler): _ISamplerData {\r\n        if (!sampler._data) {\r\n            sampler._data = {\r\n                noMipMaps: sampler.minFilter === TextureMinFilter.NEAREST || sampler.minFilter === TextureMinFilter.LINEAR,\r\n                samplingMode: GLTFLoader._GetTextureSamplingMode(context, sampler),\r\n                wrapU: GLTFLoader._GetTextureWrapMode(`${context}/wrapS`, sampler.wrapS),\r\n                wrapV: GLTFLoader._GetTextureWrapMode(`${context}/wrapT`, sampler.wrapT),\r\n            };\r\n        }\r\n\r\n        return sampler._data;\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF image.\r\n     * @param context The context when loading the asset\r\n     * @param image The glTF image property\r\n     * @returns A promise that resolves with the loaded data when the load is complete\r\n     */\r\n    public loadImageAsync(context: string, image: IImage): Promise<ArrayBufferView> {\r\n        if (!image._data) {\r\n            this.logOpen(`${context} ${image.name || \"\"}`);\r\n\r\n            if (image.uri) {\r\n                image._data = this.loadUriAsync(`${context}/uri`, image, image.uri);\r\n            } else {\r\n                const bufferView = ArrayItem.Get(`${context}/bufferView`, this._gltf.bufferViews, image.bufferView);\r\n                image._data = this.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView);\r\n            }\r\n\r\n            this.logClose();\r\n        }\r\n\r\n        return image._data;\r\n    }\r\n\r\n    /**\r\n     * Loads a glTF uri.\r\n     * @param context The context when loading the asset\r\n     * @param property The glTF property associated with the uri\r\n     * @param uri The base64 or relative uri\r\n     * @returns A promise that resolves with the loaded data when the load is complete\r\n     */\r\n    public loadUriAsync(context: string, property: IProperty, uri: string): Promise<ArrayBufferView> {\r\n        const extensionPromise = this._extensionsLoadUriAsync(context, property, uri);\r\n        if (extensionPromise) {\r\n            return extensionPromise;\r\n        }\r\n\r\n        if (!GLTFLoader._ValidateUri(uri)) {\r\n            throw new Error(`${context}: '${uri}' is invalid`);\r\n        }\r\n\r\n        if (IsBase64DataUrl(uri)) {\r\n            const data = new Uint8Array(DecodeBase64UrlToBinary(uri));\r\n            this.log(`${context}: Decoded ${uri.substr(0, 64)}... (${data.length} bytes)`);\r\n            return Promise.resolve(data);\r\n        }\r\n\r\n        this.log(`${context}: Loading ${uri}`);\r\n\r\n        return this._parent.preprocessUrlAsync(this._rootUrl + uri).then((url) => {\r\n            return new Promise((resolve, reject) => {\r\n                this._parent._loadFile(\r\n                    this._babylonScene,\r\n                    url,\r\n                    (data) => {\r\n                        if (!this._disposed) {\r\n                            this.log(`${context}: Loaded ${uri} (${(data as ArrayBuffer).byteLength} bytes)`);\r\n                            resolve(new Uint8Array(data as ArrayBuffer));\r\n                        }\r\n                    },\r\n                    true,\r\n                    (request) => {\r\n                        reject(new LoadFileError(`${context}: Failed to load '${uri}'${request ? \": \" + request.status + \" \" + request.statusText : \"\"}`, request));\r\n                    }\r\n                );\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds a JSON pointer to the _internalMetadata of the Babylon object at `<object>._internalMetadata.gltf.pointers`.\r\n     * @param babylonObject the Babylon object with _internalMetadata\r\n     * @param pointer the JSON pointer\r\n     */\r\n    public static AddPointerMetadata(babylonObject: IWithMetadata, pointer: string): void {\r\n        babylonObject.metadata = babylonObject.metadata || {};\r\n        const metadata = (babylonObject._internalMetadata = babylonObject._internalMetadata || {});\r\n        const gltf = (metadata.gltf = metadata.gltf || {});\r\n        const pointers = (gltf.pointers = gltf.pointers || []);\r\n        pointers.push(pointer);\r\n    }\r\n\r\n    private static _GetTextureWrapMode(context: string, mode: TextureWrapMode | undefined): number {\r\n        // Set defaults if undefined\r\n        mode = mode == undefined ? TextureWrapMode.REPEAT : mode;\r\n\r\n        switch (mode) {\r\n            case TextureWrapMode.CLAMP_TO_EDGE:\r\n                return Texture.CLAMP_ADDRESSMODE;\r\n            case TextureWrapMode.MIRRORED_REPEAT:\r\n                return Texture.MIRROR_ADDRESSMODE;\r\n            case TextureWrapMode.REPEAT:\r\n                return Texture.WRAP_ADDRESSMODE;\r\n            default:\r\n                Logger.Warn(`${context}: Invalid value (${mode})`);\r\n                return Texture.WRAP_ADDRESSMODE;\r\n        }\r\n    }\r\n\r\n    private static _GetTextureSamplingMode(context: string, sampler: ISampler): number {\r\n        // Set defaults if undefined\r\n        const magFilter = sampler.magFilter == undefined ? TextureMagFilter.LINEAR : sampler.magFilter;\r\n        const minFilter = sampler.minFilter == undefined ? TextureMinFilter.LINEAR_MIPMAP_LINEAR : sampler.minFilter;\r\n\r\n        if (magFilter === TextureMagFilter.LINEAR) {\r\n            switch (minFilter) {\r\n                case TextureMinFilter.NEAREST:\r\n                    return Texture.LINEAR_NEAREST;\r\n                case TextureMinFilter.LINEAR:\r\n                    return Texture.LINEAR_LINEAR;\r\n                case TextureMinFilter.NEAREST_MIPMAP_NEAREST:\r\n                    return Texture.LINEAR_NEAREST_MIPNEAREST;\r\n                case TextureMinFilter.LINEAR_MIPMAP_NEAREST:\r\n                    return Texture.LINEAR_LINEAR_MIPNEAREST;\r\n                case TextureMinFilter.NEAREST_MIPMAP_LINEAR:\r\n                    return Texture.LINEAR_NEAREST_MIPLINEAR;\r\n                case TextureMinFilter.LINEAR_MIPMAP_LINEAR:\r\n                    return Texture.LINEAR_LINEAR_MIPLINEAR;\r\n                default:\r\n                    Logger.Warn(`${context}/minFilter: Invalid value (${minFilter})`);\r\n                    return Texture.LINEAR_LINEAR_MIPLINEAR;\r\n            }\r\n        } else {\r\n            if (magFilter !== TextureMagFilter.NEAREST) {\r\n                Logger.Warn(`${context}/magFilter: Invalid value (${magFilter})`);\r\n            }\r\n\r\n            switch (minFilter) {\r\n                case TextureMinFilter.NEAREST:\r\n                    return Texture.NEAREST_NEAREST;\r\n                case TextureMinFilter.LINEAR:\r\n                    return Texture.NEAREST_LINEAR;\r\n                case TextureMinFilter.NEAREST_MIPMAP_NEAREST:\r\n                    return Texture.NEAREST_NEAREST_MIPNEAREST;\r\n                case TextureMinFilter.LINEAR_MIPMAP_NEAREST:\r\n                    return Texture.NEAREST_LINEAR_MIPNEAREST;\r\n                case TextureMinFilter.NEAREST_MIPMAP_LINEAR:\r\n                    return Texture.NEAREST_NEAREST_MIPLINEAR;\r\n                case TextureMinFilter.LINEAR_MIPMAP_LINEAR:\r\n                    return Texture.NEAREST_LINEAR_MIPLINEAR;\r\n                default:\r\n                    Logger.Warn(`${context}/minFilter: Invalid value (${minFilter})`);\r\n                    return Texture.NEAREST_NEAREST_MIPNEAREST;\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _GetTypedArrayConstructor(context: string, componentType: AccessorComponentType): TypedArrayConstructor {\r\n        switch (componentType) {\r\n            case AccessorComponentType.BYTE:\r\n                return Int8Array;\r\n            case AccessorComponentType.UNSIGNED_BYTE:\r\n                return Uint8Array;\r\n            case AccessorComponentType.SHORT:\r\n                return Int16Array;\r\n            case AccessorComponentType.UNSIGNED_SHORT:\r\n                return Uint16Array;\r\n            case AccessorComponentType.UNSIGNED_INT:\r\n                return Uint32Array;\r\n            case AccessorComponentType.FLOAT:\r\n                return Float32Array;\r\n            default:\r\n                throw new Error(`${context}: Invalid component type ${componentType}`);\r\n        }\r\n    }\r\n\r\n    private static _GetTypedArray(\r\n        context: string,\r\n        componentType: AccessorComponentType,\r\n        bufferView: ArrayBufferView,\r\n        byteOffset: number | undefined,\r\n        length: number\r\n    ): TypedArrayLike {\r\n        const buffer = bufferView.buffer;\r\n        byteOffset = bufferView.byteOffset + (byteOffset || 0);\r\n\r\n        const constructor = GLTFLoader._GetTypedArrayConstructor(`${context}/componentType`, componentType);\r\n\r\n        const componentTypeLength = VertexBuffer.GetTypeByteLength(componentType);\r\n        if (byteOffset % componentTypeLength !== 0) {\r\n            // HACK: Copy the buffer if byte offset is not a multiple of component type byte length.\r\n            Logger.Warn(`${context}: Copying buffer as byte offset (${byteOffset}) is not a multiple of component type byte length (${componentTypeLength})`);\r\n            return new constructor(buffer.slice(byteOffset, byteOffset + length * componentTypeLength), 0);\r\n        }\r\n\r\n        return new constructor(buffer, byteOffset, length);\r\n    }\r\n\r\n    private static _GetNumComponents(context: string, type: string): number {\r\n        switch (type) {\r\n            case \"SCALAR\":\r\n                return 1;\r\n            case \"VEC2\":\r\n                return 2;\r\n            case \"VEC3\":\r\n                return 3;\r\n            case \"VEC4\":\r\n                return 4;\r\n            case \"MAT2\":\r\n                return 4;\r\n            case \"MAT3\":\r\n                return 9;\r\n            case \"MAT4\":\r\n                return 16;\r\n        }\r\n\r\n        throw new Error(`${context}: Invalid type (${type})`);\r\n    }\r\n\r\n    private static _ValidateUri(uri: string): boolean {\r\n        return Tools.IsBase64(uri) || uri.indexOf(\"..\") === -1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _GetDrawMode(context: string, mode: number | undefined): number {\r\n        if (mode == undefined) {\r\n            mode = MeshPrimitiveMode.TRIANGLES;\r\n        }\r\n\r\n        switch (mode) {\r\n            case MeshPrimitiveMode.POINTS:\r\n                return Material.PointListDrawMode;\r\n            case MeshPrimitiveMode.LINES:\r\n                return Material.LineListDrawMode;\r\n            case MeshPrimitiveMode.LINE_LOOP:\r\n                return Material.LineLoopDrawMode;\r\n            case MeshPrimitiveMode.LINE_STRIP:\r\n                return Material.LineStripDrawMode;\r\n            case MeshPrimitiveMode.TRIANGLES:\r\n                return Material.TriangleFillMode;\r\n            case MeshPrimitiveMode.TRIANGLE_STRIP:\r\n                return Material.TriangleStripDrawMode;\r\n            case MeshPrimitiveMode.TRIANGLE_FAN:\r\n                return Material.TriangleFanDrawMode;\r\n        }\r\n\r\n        throw new Error(`${context}: Invalid mesh primitive mode (${mode})`);\r\n    }\r\n\r\n    private _compileMaterialsAsync(): Promise<void> {\r\n        this._parent._startPerformanceCounter(\"Compile materials\");\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        if (this._gltf.materials) {\r\n            for (const material of this._gltf.materials) {\r\n                if (material._data) {\r\n                    for (const babylonDrawMode in material._data) {\r\n                        const babylonData = material._data[babylonDrawMode];\r\n                        for (const babylonMesh of babylonData.babylonMeshes) {\r\n                            // Ensure nonUniformScaling is set if necessary.\r\n                            babylonMesh.computeWorldMatrix(true);\r\n\r\n                            const babylonMaterial = babylonData.babylonMaterial;\r\n                            promises.push(babylonMaterial.forceCompilationAsync(babylonMesh));\r\n                            promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { useInstances: true }));\r\n                            if (this._parent.useClipPlane) {\r\n                                promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { clipPlane: true }));\r\n                                promises.push(babylonMaterial.forceCompilationAsync(babylonMesh, { clipPlane: true, useInstances: true }));\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {\r\n            this._parent._endPerformanceCounter(\"Compile materials\");\r\n        });\r\n    }\r\n\r\n    private _compileShadowGeneratorsAsync(): Promise<void> {\r\n        this._parent._startPerformanceCounter(\"Compile shadow generators\");\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        const lights = this._babylonScene.lights;\r\n        for (const light of lights) {\r\n            const generator = light.getShadowGenerator();\r\n            if (generator) {\r\n                promises.push(generator.forceCompilationAsync());\r\n            }\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {\r\n            this._parent._endPerformanceCounter(\"Compile shadow generators\");\r\n        });\r\n    }\r\n\r\n    private _forEachExtensions(action: (extension: IGLTFLoaderExtension) => void): void {\r\n        for (const extension of this._extensions) {\r\n            if (extension.enabled) {\r\n                action(extension);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _applyExtensions<T>(property: IProperty, functionName: string, actionAsync: (extension: IGLTFLoaderExtension) => Nullable<T> | undefined): Nullable<T> {\r\n        for (const extension of this._extensions) {\r\n            if (extension.enabled) {\r\n                const id = `${extension.name}.${functionName}`;\r\n                const loaderProperty = property as ILoaderProperty;\r\n                loaderProperty._activeLoaderExtensionFunctions = loaderProperty._activeLoaderExtensionFunctions || {};\r\n                const activeLoaderExtensionFunctions = loaderProperty._activeLoaderExtensionFunctions;\r\n                if (!activeLoaderExtensionFunctions[id]) {\r\n                    activeLoaderExtensionFunctions[id] = true;\r\n\r\n                    try {\r\n                        const result = actionAsync(extension);\r\n                        if (result) {\r\n                            return result;\r\n                        }\r\n                    } finally {\r\n                        delete activeLoaderExtensionFunctions[id];\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _extensionsOnLoading(): void {\r\n        this._forEachExtensions((extension) => extension.onLoading && extension.onLoading());\r\n    }\r\n\r\n    private _extensionsOnReady(): void {\r\n        this._forEachExtensions((extension) => extension.onReady && extension.onReady());\r\n    }\r\n\r\n    private _extensionsLoadSceneAsync(context: string, scene: IScene): Nullable<Promise<void>> {\r\n        return this._applyExtensions(scene, \"loadScene\", (extension) => extension.loadSceneAsync && extension.loadSceneAsync(context, scene));\r\n    }\r\n\r\n    private _extensionsLoadNodeAsync(context: string, node: INode, assign: (babylonTransformNode: TransformNode) => void): Nullable<Promise<TransformNode>> {\r\n        return this._applyExtensions(node, \"loadNode\", (extension) => extension.loadNodeAsync && extension.loadNodeAsync(context, node, assign));\r\n    }\r\n\r\n    private _extensionsLoadCameraAsync(context: string, camera: ICamera, assign: (babylonCamera: Camera) => void): Nullable<Promise<Camera>> {\r\n        return this._applyExtensions(camera, \"loadCamera\", (extension) => extension.loadCameraAsync && extension.loadCameraAsync(context, camera, assign));\r\n    }\r\n\r\n    private _extensionsLoadVertexDataAsync(context: string, primitive: IMeshPrimitive, babylonMesh: Mesh): Nullable<Promise<Geometry>> {\r\n        return this._applyExtensions(primitive, \"loadVertexData\", (extension) => extension._loadVertexDataAsync && extension._loadVertexDataAsync(context, primitive, babylonMesh));\r\n    }\r\n\r\n    private _extensionsLoadMeshPrimitiveAsync(\r\n        context: string,\r\n        name: string,\r\n        node: INode,\r\n        mesh: IMesh,\r\n        primitive: IMeshPrimitive,\r\n        assign: (babylonMesh: AbstractMesh) => void\r\n    ): Nullable<Promise<AbstractMesh>> {\r\n        return this._applyExtensions(\r\n            primitive,\r\n            \"loadMeshPrimitive\",\r\n            (extension) => extension._loadMeshPrimitiveAsync && extension._loadMeshPrimitiveAsync(context, name, node, mesh, primitive, assign)\r\n        );\r\n    }\r\n\r\n    private _extensionsLoadMaterialAsync(\r\n        context: string,\r\n        material: IMaterial,\r\n        babylonMesh: Nullable<Mesh>,\r\n        babylonDrawMode: number,\r\n        assign: (babylonMaterial: Material) => void\r\n    ): Nullable<Promise<Material>> {\r\n        return this._applyExtensions(\r\n            material,\r\n            \"loadMaterial\",\r\n            (extension) => extension._loadMaterialAsync && extension._loadMaterialAsync(context, material, babylonMesh, babylonDrawMode, assign)\r\n        );\r\n    }\r\n\r\n    private _extensionsCreateMaterial(context: string, material: IMaterial, babylonDrawMode: number): Nullable<Material> {\r\n        return this._applyExtensions(material, \"createMaterial\", (extension) => extension.createMaterial && extension.createMaterial(context, material, babylonDrawMode));\r\n    }\r\n\r\n    private _extensionsLoadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return this._applyExtensions(\r\n            material,\r\n            \"loadMaterialProperties\",\r\n            (extension) => extension.loadMaterialPropertiesAsync && extension.loadMaterialPropertiesAsync(context, material, babylonMaterial)\r\n        );\r\n    }\r\n\r\n    private _extensionsLoadTextureInfoAsync(context: string, textureInfo: ITextureInfo, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>> {\r\n        return this._applyExtensions(textureInfo, \"loadTextureInfo\", (extension) => extension.loadTextureInfoAsync && extension.loadTextureInfoAsync(context, textureInfo, assign));\r\n    }\r\n\r\n    private _extensionsLoadTextureAsync(context: string, texture: ITexture, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>> {\r\n        return this._applyExtensions(texture, \"loadTexture\", (extension) => extension._loadTextureAsync && extension._loadTextureAsync(context, texture, assign));\r\n    }\r\n\r\n    private _extensionsLoadAnimationAsync(context: string, animation: IAnimation): Nullable<Promise<AnimationGroup>> {\r\n        return this._applyExtensions(animation, \"loadAnimation\", (extension) => extension.loadAnimationAsync && extension.loadAnimationAsync(context, animation));\r\n    }\r\n\r\n    private _extensionsLoadAnimationChannelAsync(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        channel: IAnimationChannel,\r\n        onLoad: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): Nullable<Promise<void>> {\r\n        return this._applyExtensions(\r\n            animation,\r\n            \"loadAnimationChannel\",\r\n            (extension) => extension._loadAnimationChannelAsync && extension._loadAnimationChannelAsync(context, animationContext, animation, channel, onLoad)\r\n        );\r\n    }\r\n\r\n    private _extensionsLoadSkinAsync(context: string, node: INode, skin: ISkin): Nullable<Promise<void>> {\r\n        return this._applyExtensions(skin, \"loadSkin\", (extension) => extension._loadSkinAsync && extension._loadSkinAsync(context, node, skin));\r\n    }\r\n\r\n    private _extensionsLoadUriAsync(context: string, property: IProperty, uri: string): Nullable<Promise<ArrayBufferView>> {\r\n        return this._applyExtensions(property, \"loadUri\", (extension) => extension._loadUriAsync && extension._loadUriAsync(context, property, uri));\r\n    }\r\n\r\n    private _extensionsLoadBufferViewAsync(context: string, bufferView: IBufferView): Nullable<Promise<ArrayBufferView>> {\r\n        return this._applyExtensions(bufferView, \"loadBufferView\", (extension) => extension.loadBufferViewAsync && extension.loadBufferViewAsync(context, bufferView));\r\n    }\r\n\r\n    private _extensionsLoadBufferAsync(context: string, buffer: IBuffer, byteOffset: number, byteLength: number): Nullable<Promise<ArrayBufferView>> {\r\n        return this._applyExtensions(buffer, \"loadBuffer\", (extension) => extension.loadBufferAsync && extension.loadBufferAsync(context, buffer, byteOffset, byteLength));\r\n    }\r\n\r\n    /**\r\n     * Helper method called by a loader extension to load an glTF extension.\r\n     * @param context The context when loading the asset\r\n     * @param property The glTF property to load the extension from\r\n     * @param extensionName The name of the extension to load\r\n     * @param actionAsync The action to run\r\n     * @returns The promise returned by actionAsync or null if the extension does not exist\r\n     */\r\n    public static LoadExtensionAsync<TExtension = any, TResult = void>(\r\n        context: string,\r\n        property: IProperty,\r\n        extensionName: string,\r\n        actionAsync: (extensionContext: string, extension: TExtension) => Nullable<Promise<TResult>>\r\n    ): Nullable<Promise<TResult>> {\r\n        if (!property.extensions) {\r\n            return null;\r\n        }\r\n\r\n        const extensions = property.extensions;\r\n\r\n        const extension = extensions[extensionName] as TExtension;\r\n        if (!extension) {\r\n            return null;\r\n        }\r\n\r\n        return actionAsync(`${context}/extensions/${extensionName}`, extension);\r\n    }\r\n\r\n    /**\r\n     * Helper method called by a loader extension to load a glTF extra.\r\n     * @param context The context when loading the asset\r\n     * @param property The glTF property to load the extra from\r\n     * @param extensionName The name of the extension to load\r\n     * @param actionAsync The action to run\r\n     * @returns The promise returned by actionAsync or null if the extra does not exist\r\n     */\r\n    public static LoadExtraAsync<TExtra = any, TResult = void>(\r\n        context: string,\r\n        property: IProperty,\r\n        extensionName: string,\r\n        actionAsync: (extraContext: string, extra: TExtra) => Nullable<Promise<TResult>>\r\n    ): Nullable<Promise<TResult>> {\r\n        if (!property.extras) {\r\n            return null;\r\n        }\r\n\r\n        const extras = property.extras;\r\n\r\n        const extra = extras[extensionName] as TExtra;\r\n        if (!extra) {\r\n            return null;\r\n        }\r\n\r\n        return actionAsync(`${context}/extras/${extensionName}`, extra);\r\n    }\r\n\r\n    /**\r\n     * Checks for presence of an extension.\r\n     * @param name The name of the extension to check\r\n     * @returns A boolean indicating the presence of the given extension name in `extensionsUsed`\r\n     */\r\n    public isExtensionUsed(name: string): boolean {\r\n        return !!this._gltf.extensionsUsed && this._gltf.extensionsUsed.indexOf(name) !== -1;\r\n    }\r\n\r\n    /**\r\n     * Increments the indentation level and logs a message.\r\n     * @param message The message to log\r\n     */\r\n    public logOpen(message: string): void {\r\n        this._parent._logOpen(message);\r\n    }\r\n\r\n    /**\r\n     * Decrements the indentation level.\r\n     */\r\n    public logClose(): void {\r\n        this._parent._logClose();\r\n    }\r\n\r\n    /**\r\n     * Logs a message\r\n     * @param message The message to log\r\n     */\r\n    public log(message: string): void {\r\n        this._parent._log(message);\r\n    }\r\n\r\n    /**\r\n     * Starts a performance counter.\r\n     * @param counterName The name of the performance counter\r\n     */\r\n    public startPerformanceCounter(counterName: string): void {\r\n        this._parent._startPerformanceCounter(counterName);\r\n    }\r\n\r\n    /**\r\n     * Ends a performance counter.\r\n     * @param counterName The name of the performance counter\r\n     */\r\n    public endPerformanceCounter(counterName: string): void {\r\n        this._parent._endPerformanceCounter(counterName);\r\n    }\r\n}\r\n\r\nGLTFFileLoader._CreateGLTF2Loader = (parent) => new GLTFLoader(parent);\r\n"]}