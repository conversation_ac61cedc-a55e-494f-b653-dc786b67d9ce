{"version": 3, "file": "glTFMaterialsCommonExtension.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/1.0/glTFMaterialsCommonExtension.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAI/E,OAAO,EAAE,OAAO,EAAE,6CAA+B;AACjD,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,KAAK,EAAE,sCAAwB;AACxC,OAAO,EAAE,QAAQ,EAAE,8CAAgC;AACnD,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AACnE,OAAO,EAAE,gBAAgB,EAAE,mDAAqC;AAChE,OAAO,EAAE,gBAAgB,EAAE,mDAAqC;AAChE,OAAO,EAAE,UAAU,EAAE,6CAA+B;AACpD,OAAO,EAAE,SAAS,EAAE,4CAA8B;AAwDlD;;;GAGG;AACH,MAAM,OAAO,4BAA6B,SAAQ,mBAAmB;IACjE;QACI,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAClC,CAAC;IAEM,0BAA0B,CAAC,WAAyB;QACvD,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,SAAS,GAAgC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjF,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;QAED,gBAAgB;QAChB,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAChC,IAAI,MAAM,EAAE;YACR,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBACxB,MAAM,KAAK,GAA8B,MAAM,CAAC,KAAK,CAAC,CAAC;gBAEvD,QAAQ,KAAK,CAAC,IAAI,EAAE;oBAChB,KAAK,SAAS,CAAC,CAAC;wBACZ,MAAM,YAAY,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;wBAC/F,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;wBAC9B,IAAI,OAAO,EAAE;4BACT,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACvE;wBACD,MAAM;qBACT;oBACD,KAAK,OAAO,CAAC,CAAC;wBACV,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;wBAC1F,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;wBAC1B,IAAI,KAAK,EAAE;4BACP,UAAU,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACnE;wBACD,MAAM;qBACT;oBACD,KAAK,aAAa,CAAC,CAAC;wBAChB,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;wBAC5F,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;wBACtC,IAAI,WAAW,EAAE;4BACb,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACvE;wBACD,MAAM;qBACT;oBACD,KAAK,MAAM,CAAC,CAAC;wBACT,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;wBACxB,IAAI,IAAI,EAAE;4BACN,MAAM,SAAS,GAAG,IAAI,SAAS,CAC3B,KAAK,CAAC,IAAI,EACV,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EACrB,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EACrB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,EAC5B,IAAI,CAAC,eAAe,IAAI,GAAG,EAC3B,WAAW,CAAC,KAAK,CACpB,CAAC;4BACF,SAAS,CAAC,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;yBACjE;wBACD,MAAM;qBACT;oBACD;wBACI,KAAK,CAAC,IAAI,CAAC,8CAA8C,GAAG,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC,CAAC;wBAC5F,MAAM;iBACb;aACJ;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,iBAAiB,CAAC,WAAyB,EAAE,EAAU,EAAE,SAAuC,EAAE,OAAkC;QACvI,MAAM,QAAQ,GAAkB,WAAW,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YACnC,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,SAAS,GAAkC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QACrE,gBAAgB,CAAC,eAAe,GAAG,QAAQ,CAAC,+BAA+B,CAAC;QAE5E,IAAI,SAAS,CAAC,SAAS,KAAK,UAAU,EAAE;YACpC,gBAAgB,CAAC,eAAe,GAAG,IAAI,CAAC;SAC3C;QAED,gBAAgB,CAAC,eAAe,GAAG,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC;QACxG,gBAAgB,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC;QAC3G,gBAAgB,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;QAE7G,UAAU;QACV,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,EAAE;YAC9C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;SACzG;aAAM;YACH,gBAAgB,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC3F;QAED,UAAU;QACV,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,KAAK,QAAQ,EAAE;YAC9C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;SACzG;aAAM;YACH,gBAAgB,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC3F;QAED,WAAW;QACX,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC/C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;SAC3G;aAAM;YACH,gBAAgB,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7F;QAED,WAAW;QACX,IAAI,OAAO,SAAS,CAAC,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YAC/C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;SAC3G;aAAM;YACH,gBAAgB,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC7F;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,WAAyB,EAAE,EAAU,EAAE,QAA0B,EAAE,YAAoB,EAAE,OAAkC;QAC5I,iCAAiC;QACjC,cAAc,CAAC,sBAAsB,CACjC,WAAW,EACX,EAAE,EACF,CAAC,MAAM,EAAE,EAAE;YACP,6BAA6B;YAC7B,cAAc,CAAC,kBAAkB,CAAC,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAO,QAAS,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;QACvH,CAAC,EACD,OAAO,CACV,CAAC;IACN,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,4BAA4B,EAAE,CAAC,CAAC", "sourcesContent": ["import { GLTFLoaderExtension, GLT<PERSON>oaderBase, GLTFLoader } from \"./glTFLoader\";\r\n\r\nimport type { IGLTFRuntime, IGLTFMaterial } from \"./glTFLoaderInterfaces\";\r\n\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport { Material } from \"core/Materials/material\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport { HemisphericLight } from \"core/Lights/hemisphericLight\";\r\nimport { DirectionalLight } from \"core/Lights/directionalLight\";\r\nimport { PointLight } from \"core/Lights/pointLight\";\r\nimport { SpotLight } from \"core/Lights/spotLight\";\r\n\r\ninterface IGLTFMaterialsCommonExtensionValues {\r\n    ambient?: number[] | string;\r\n    diffuse?: number[] | string;\r\n    emission?: number[] | string;\r\n    specular?: number[] | string;\r\n    shininess?: number;\r\n    transparency?: number;\r\n}\r\n\r\ninterface IGLTFMaterialsCommonExtension {\r\n    technique: string;\r\n    transparent?: number;\r\n    doubleSided?: boolean;\r\n    values: IGLTFMaterialsCommonExtensionValues;\r\n}\r\n\r\ninterface IGLTFRuntimeCommonExtension {\r\n    lights: { [key: string]: IGLTFLightCommonExtension };\r\n}\r\n\r\ninterface IGLTFLightCommonExtension {\r\n    name: string;\r\n    type: string;\r\n\r\n    ambient?: IGLTFAmbientLightCommonExtension;\r\n    point?: IGLTFPointLightCommonExtension;\r\n    directional?: IGLTFDirectionalLightCommonExtension;\r\n    spot?: IGLTFSpotLightCommonExtension;\r\n}\r\n\r\ninterface IGLTFPointLightCommonExtension {\r\n    color: number[];\r\n    constantAttenuation: number;\r\n    linearAttenuation: number;\r\n    quadraticAttenuation: number;\r\n}\r\n\r\ninterface IGLTFAmbientLightCommonExtension {\r\n    color: number[];\r\n}\r\n\r\ninterface IGLTFDirectionalLightCommonExtension {\r\n    color: number[];\r\n}\r\n\r\ninterface IGLTFSpotLightCommonExtension {\r\n    color: number[];\r\n    constantAttenuation: number;\r\n    fallOffAngle: number;\r\n    fallOffExponent: number;\r\n    linearAttenuation: number;\r\n    quadraticAttenuation: number;\r\n}\r\n\r\n/**\r\n * @internal\r\n * @deprecated\r\n */\r\nexport class GLTFMaterialsCommonExtension extends GLTFLoaderExtension {\r\n    constructor() {\r\n        super(\"KHR_materials_common\");\r\n    }\r\n\r\n    public loadRuntimeExtensionsAsync(gltfRuntime: IGLTFRuntime): boolean {\r\n        if (!gltfRuntime.extensions) {\r\n            return false;\r\n        }\r\n\r\n        const extension: IGLTFRuntimeCommonExtension = gltfRuntime.extensions[this.name];\r\n        if (!extension) {\r\n            return false;\r\n        }\r\n\r\n        // Create lights\r\n        const lights = extension.lights;\r\n        if (lights) {\r\n            for (const thing in lights) {\r\n                const light: IGLTFLightCommonExtension = lights[thing];\r\n\r\n                switch (light.type) {\r\n                    case \"ambient\": {\r\n                        const ambientLight = new HemisphericLight(light.name, new Vector3(0, 1, 0), gltfRuntime.scene);\r\n                        const ambient = light.ambient;\r\n                        if (ambient) {\r\n                            ambientLight.diffuse = Color3.FromArray(ambient.color || [1, 1, 1]);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case \"point\": {\r\n                        const pointLight = new PointLight(light.name, new Vector3(10, 10, 10), gltfRuntime.scene);\r\n                        const point = light.point;\r\n                        if (point) {\r\n                            pointLight.diffuse = Color3.FromArray(point.color || [1, 1, 1]);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case \"directional\": {\r\n                        const dirLight = new DirectionalLight(light.name, new Vector3(0, -1, 0), gltfRuntime.scene);\r\n                        const directional = light.directional;\r\n                        if (directional) {\r\n                            dirLight.diffuse = Color3.FromArray(directional.color || [1, 1, 1]);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case \"spot\": {\r\n                        const spot = light.spot;\r\n                        if (spot) {\r\n                            const spotLight = new SpotLight(\r\n                                light.name,\r\n                                new Vector3(0, 10, 0),\r\n                                new Vector3(0, -1, 0),\r\n                                spot.fallOffAngle || Math.PI,\r\n                                spot.fallOffExponent || 0.0,\r\n                                gltfRuntime.scene\r\n                            );\r\n                            spotLight.diffuse = Color3.FromArray(spot.color || [1, 1, 1]);\r\n                        }\r\n                        break;\r\n                    }\r\n                    default:\r\n                        Tools.Warn('GLTF Material Common extension: light type \"' + light.type + \"” not supported\");\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public loadMaterialAsync(gltfRuntime: IGLTFRuntime, id: string, onSuccess: (material: Material) => void, onError: (message: string) => void): boolean {\r\n        const material: IGLTFMaterial = gltfRuntime.materials[id];\r\n        if (!material || !material.extensions) {\r\n            return false;\r\n        }\r\n\r\n        const extension: IGLTFMaterialsCommonExtension = material.extensions[this.name];\r\n        if (!extension) {\r\n            return false;\r\n        }\r\n\r\n        const standardMaterial = new StandardMaterial(id, gltfRuntime.scene);\r\n        standardMaterial.sideOrientation = Material.CounterClockWiseSideOrientation;\r\n\r\n        if (extension.technique === \"CONSTANT\") {\r\n            standardMaterial.disableLighting = true;\r\n        }\r\n\r\n        standardMaterial.backFaceCulling = extension.doubleSided === undefined ? false : !extension.doubleSided;\r\n        standardMaterial.alpha = extension.values.transparency === undefined ? 1.0 : extension.values.transparency;\r\n        standardMaterial.specularPower = extension.values.shininess === undefined ? 0.0 : extension.values.shininess;\r\n\r\n        // Ambient\r\n        if (typeof extension.values.ambient === \"string\") {\r\n            this._loadTexture(gltfRuntime, extension.values.ambient, standardMaterial, \"ambientTexture\", onError);\r\n        } else {\r\n            standardMaterial.ambientColor = Color3.FromArray(extension.values.ambient || [0, 0, 0]);\r\n        }\r\n\r\n        // Diffuse\r\n        if (typeof extension.values.diffuse === \"string\") {\r\n            this._loadTexture(gltfRuntime, extension.values.diffuse, standardMaterial, \"diffuseTexture\", onError);\r\n        } else {\r\n            standardMaterial.diffuseColor = Color3.FromArray(extension.values.diffuse || [0, 0, 0]);\r\n        }\r\n\r\n        // Emission\r\n        if (typeof extension.values.emission === \"string\") {\r\n            this._loadTexture(gltfRuntime, extension.values.emission, standardMaterial, \"emissiveTexture\", onError);\r\n        } else {\r\n            standardMaterial.emissiveColor = Color3.FromArray(extension.values.emission || [0, 0, 0]);\r\n        }\r\n\r\n        // Specular\r\n        if (typeof extension.values.specular === \"string\") {\r\n            this._loadTexture(gltfRuntime, extension.values.specular, standardMaterial, \"specularTexture\", onError);\r\n        } else {\r\n            standardMaterial.specularColor = Color3.FromArray(extension.values.specular || [0, 0, 0]);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _loadTexture(gltfRuntime: IGLTFRuntime, id: string, material: StandardMaterial, propertyPath: string, onError: (message: string) => void): void {\r\n        // Create buffer from texture url\r\n        GLTFLoaderBase.LoadTextureBufferAsync(\r\n            gltfRuntime,\r\n            id,\r\n            (buffer) => {\r\n                // Create texture from buffer\r\n                GLTFLoaderBase.CreateTextureAsync(gltfRuntime, id, buffer, (texture) => ((<any>material)[propertyPath] = texture));\r\n            },\r\n            onError\r\n        );\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(new GLTFMaterialsCommonExtension());\r\n"]}