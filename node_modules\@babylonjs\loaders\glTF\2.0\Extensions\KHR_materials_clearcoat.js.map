{"version": 3, "file": "KHR_materials_clearcoat.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_clearcoat.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,yBAAyB,CAAC;AAEvC;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,uBAAuB;IAkBhC;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAyB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACvH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YAChG,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,6BAA6B,CAAC,OAAe,EAAE,UAAkC,EAAE,eAAyB;QAChH,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3C,eAAe,CAAC,SAAS,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAC9D,eAAe,CAAC,SAAS,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAE3D,IAAI,UAAU,CAAC,eAAe,IAAI,SAAS,EAAE;YACzC,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAAC;SACpE;aAAM;YACH,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;SAC3C;QAED,IAAI,UAAU,CAAC,gBAAgB,EAAE;YAC7B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,mBAAmB,EAAE,UAAU,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,EAAE;gBACtG,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,wBAAwB,CAAC;gBAC/D,eAAe,CAAC,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;YAChD,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,wBAAwB,IAAI,SAAS,EAAE;YAClD,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,CAAC,wBAAwB,CAAC;SAC7E;aAAM;YACH,eAAe,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC;SAC3C;QAED,IAAI,UAAU,CAAC,yBAAyB,EAAE;YACrC,UAAU,CAAC,yBAA0C,CAAC,YAAY,GAAG,IAAI,CAAC;YAC3E,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,4BAA4B,EAAE,UAAU,CAAC,yBAAyB,EAAE,CAAC,OAAO,EAAE,EAAE;gBACxH,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,wBAAwB,CAAC;gBAC/D,eAAe,CAAC,SAAS,CAAC,gBAAgB,GAAG,OAAO,CAAC;YACzD,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,sBAAsB,EAAE;YAClC,UAAU,CAAC,sBAAuC,CAAC,YAAY,GAAG,IAAI,CAAC;YACxE,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,yBAAyB,EAAE,UAAU,CAAC,sBAAsB,EAAE,CAAC,OAAO,EAAE,EAAE;gBAClH,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,qBAAqB,CAAC;gBAC5D,eAAe,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC;YACpD,CAAC,CAAC,CACL,CAAC;YAEF,eAAe,CAAC,gBAAgB,GAAG,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;YACpF,eAAe,CAAC,gBAAgB,GAAG,eAAe,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;YACnF,IAAI,UAAU,CAAC,sBAAsB,CAAC,KAAK,IAAI,SAAS,EAAE;gBACtD,eAAe,CAAC,SAAS,CAAC,WAAY,CAAC,KAAK,GAAG,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC;aAC1F;SACJ;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsClearcoat } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_clearcoat\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_clearcoat/README.md)\r\n * [Playground Sample](https://www.babylonjs-playground.com/frame.html#7F7PN6#8)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_clearcoat implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 190;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsClearcoat>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadClearCoatPropertiesAsync(extensionContext, extension, babylonMaterial));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadClearCoatPropertiesAsync(context: string, properties: IKHRMaterialsClearcoat, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        babylonMaterial.clearCoat.isEnabled = true;\r\n        babylonMaterial.clearCoat.useRoughnessFromMainTexture = false;\r\n        babylonMaterial.clearCoat.remapF0OnInterfaceChange = false;\r\n\r\n        if (properties.clearcoatFactor != undefined) {\r\n            babylonMaterial.clearCoat.intensity = properties.clearcoatFactor;\r\n        } else {\r\n            babylonMaterial.clearCoat.intensity = 0;\r\n        }\r\n\r\n        if (properties.clearcoatTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/clearcoatTexture`, properties.clearcoatTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (ClearCoat Intensity)`;\r\n                    babylonMaterial.clearCoat.texture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.clearcoatRoughnessFactor != undefined) {\r\n            babylonMaterial.clearCoat.roughness = properties.clearcoatRoughnessFactor;\r\n        } else {\r\n            babylonMaterial.clearCoat.roughness = 0;\r\n        }\r\n\r\n        if (properties.clearcoatRoughnessTexture) {\r\n            (properties.clearcoatRoughnessTexture as ITextureInfo).nonColorData = true;\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/clearcoatRoughnessTexture`, properties.clearcoatRoughnessTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (ClearCoat Roughness)`;\r\n                    babylonMaterial.clearCoat.textureRoughness = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.clearcoatNormalTexture) {\r\n            (properties.clearcoatNormalTexture as ITextureInfo).nonColorData = true;\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/clearcoatNormalTexture`, properties.clearcoatNormalTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (ClearCoat Normal)`;\r\n                    babylonMaterial.clearCoat.bumpTexture = texture;\r\n                })\r\n            );\r\n\r\n            babylonMaterial.invertNormalMapX = !babylonMaterial.getScene().useRightHandedSystem;\r\n            babylonMaterial.invertNormalMapY = babylonMaterial.getScene().useRightHandedSystem;\r\n            if (properties.clearcoatNormalTexture.scale != undefined) {\r\n                babylonMaterial.clearCoat.bumpTexture!.level = properties.clearcoatNormalTexture.scale;\r\n            }\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_clearcoat(loader));\r\n"]}