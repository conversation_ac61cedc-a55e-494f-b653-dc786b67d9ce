{"version": 3, "file": "gridMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/grid/gridMaterial.ts"], "names": [], "mappings": ";AAAA,yDAAyD;AACzD,OAAO,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,2CAA6B;AAEnJ,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,6CAA+B;AAC1D,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,aAAa,EAAE,mDAAqC;AAC7D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAKnD,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAEpD,OAAO,iBAAiB,CAAC;AACzB,OAAO,eAAe,CAAC;AAEvB,MAAM,mBAAoB,SAAQ,eAAe;IAa7C;QACI,KAAK,EAAE,CAAC;QAbL,YAAO,GAAG,KAAK,CAAC;QAChB,gBAAW,GAAG,KAAK,CAAC;QACpB,QAAG,GAAG,KAAK,CAAC;QACZ,qBAAgB,GAAG,KAAK,CAAC;QACzB,aAAQ,GAAG,KAAK,CAAC;QACjB,QAAG,GAAG,KAAK,CAAC;QACZ,QAAG,GAAG,KAAK,CAAC;QACZ,cAAS,GAAG,KAAK,CAAC;QAClB,mBAAc,GAAG,KAAK,CAAC;QACvB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,YAAa,SAAQ,YAAY;IA8D1C;;;;OAIG;IACH,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAnEvB;;WAEG;QAEI,cAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAElC;;WAEG;QAEI,cAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAEjC;;WAEG;QAEI,cAAS,GAAG,GAAG,CAAC;QAEvB;;WAEG;QAEI,eAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAEnC;;WAEG;QAEI,uBAAkB,GAAG,EAAE,CAAC;QAE/B;;WAEG;QAEI,wBAAmB,GAAG,IAAI,CAAC;QAElC;;WAEG;QAEI,YAAO,GAAG,GAAG,CAAC;QAErB;;WAEG;QAEI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QAEI,eAAU,GAAG,KAAK,CAAC;QAOlB,iBAAY,GAAY,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAS7H,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1F,CAAC;IAEM,wBAAwB,CAAC,IAAkB;QAC9C,OAAO,IAAI,CAAC,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7D,CAAC;IAEM,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAC;SACvD;QAED,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE;YAC5C,OAAO,CAAC,WAAW,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;YAC3C,OAAO,CAAC,iBAAiB,EAAE,CAAC;SAC/B;QAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACnD,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAC;YACrD,OAAO,CAAC,iBAAiB,EAAE,CAAC;SAC/B;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,UAAU,EAAE;YACtC,OAAO,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;YACrC,OAAO,CAAC,iBAAiB,EAAE,CAAC;SAC/B;QAED,WAAW;QACX,IAAI,OAAO,CAAC,iBAAiB,EAAE;YAC3B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAI,KAAK,CAAC,eAAe,EAAE;gBACvB,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;oBAC7D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE;wBACjC,OAAO,KAAK,CAAC;qBAChB;yBAAM;wBACH,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;qBAC1B;iBACJ;aACJ;SACJ;QAED,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEjG,kDAAkD;QAClD,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC;QAE1G,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,aAAa;YACb,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YACxE,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YAErE,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;aACrC;YACD,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;aACtC;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,UAAU;YACV,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,CAAC,SAAS,CACb,KAAK;iBACA,SAAS,EAAE;iBACX,YAAY,CACT,MAAM,EACN,OAAO,EACP;gBACI,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,MAAM;gBACN,eAAe;gBACf,eAAe;gBACf,YAAY;aACf,EACD,CAAC,gBAAgB,CAAC,EAClB,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,OAAO,CACf,EACL,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAwB,OAAO,CAAC,eAAe,CAAC;QAC7D,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,WAAW;QACX,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE;YAC7C,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAExE,WAAW;QACX,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAE7D,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC/C,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEhE,IAAI,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC,qBAAqB,EAAE;gBAC7D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACtE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACjH,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,CAAC,CAAC;aAC1F;SACJ;QACD,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,kBAA4B;QACvC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACtC,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,sBAAsB,CAAC;QACxD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACzG,CAAC;CACJ;AA3QG;IADC,iBAAiB,EAAE;+CACc;AAMlC;IADC,iBAAiB,EAAE;+CACa;AAMjC;IADC,SAAS,EAAE;+CACW;AAMvB;IADC,kBAAkB,EAAE;gDACc;AAMnC;IADC,SAAS,EAAE;wDACmB;AAM/B;IADC,SAAS,EAAE;yDACsB;AAMlC;IADC,SAAS,EAAE;6CACS;AAMrB;IADC,SAAS,EAAE;sDACoB;AAMhC;IADC,SAAS,EAAE;gDACc;AAG1B;IADC,kBAAkB,CAAC,gBAAgB,CAAC;qDACA;AAErC;IADC,gBAAgB,CAAC,kCAAkC,CAAC;oDAClB;AAwNvC,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serializeAsTexture, serialize, expandToProperty, serializeAsColor3, SerializationHelper, serializeAsVector3 } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Vector4, Vector3 } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { MaterialFlags } from \"core/Materials/materialFlags\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nimport \"./grid.fragment\";\r\nimport \"./grid.vertex\";\r\n\r\nclass GridMaterialDefines extends MaterialDefines {\r\n    public OPACITY = false;\r\n    public TRANSPARENT = false;\r\n    public FOG = false;\r\n    public PREMULTIPLYALPHA = false;\r\n    public MAX_LINE = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public INSTANCES = false;\r\n    public THIN_INSTANCES = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\n/**\r\n * The grid materials allows you to wrap any shape with a grid.\r\n * Colors are customizable.\r\n */\r\nexport class GridMaterial extends PushMaterial {\r\n    /**\r\n     * Main color of the grid (e.g. between lines)\r\n     */\r\n    @serializeAsColor3()\r\n    public mainColor = Color3.Black();\r\n\r\n    /**\r\n     * Color of the grid lines.\r\n     */\r\n    @serializeAsColor3()\r\n    public lineColor = Color3.Teal();\r\n\r\n    /**\r\n     * The scale of the grid compared to unit.\r\n     */\r\n    @serialize()\r\n    public gridRatio = 1.0;\r\n\r\n    /**\r\n     * Allows setting an offset for the grid lines.\r\n     */\r\n    @serializeAsVector3()\r\n    public gridOffset = Vector3.Zero();\r\n\r\n    /**\r\n     * The frequency of thicker lines.\r\n     */\r\n    @serialize()\r\n    public majorUnitFrequency = 10;\r\n\r\n    /**\r\n     * The visibility of minor units in the grid.\r\n     */\r\n    @serialize()\r\n    public minorUnitVisibility = 0.33;\r\n\r\n    /**\r\n     * The grid opacity outside of the lines.\r\n     */\r\n    @serialize()\r\n    public opacity = 1.0;\r\n\r\n    /**\r\n     * Determine RBG output is premultiplied by alpha value.\r\n     */\r\n    @serialize()\r\n    public preMultiplyAlpha = false;\r\n\r\n    /**\r\n     * Determines if the max line value will be used instead of the sum wherever grid lines intersect.\r\n     */\r\n    @serialize()\r\n    public useMaxLine = false;\r\n\r\n    @serializeAsTexture(\"opacityTexture\")\r\n    private _opacityTexture: BaseTexture;\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public opacityTexture: BaseTexture;\r\n\r\n    private _gridControl: Vector4 = new Vector4(this.gridRatio, this.majorUnitFrequency, this.minorUnitVisibility, this.opacity);\r\n\r\n    /**\r\n     * constructor\r\n     * @param name The name given to the material in order to identify it afterwards.\r\n     * @param scene The scene the material is used in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the grid requires alpha blending.\r\n     */\r\n    public needAlphaBlending(): boolean {\r\n        return this.opacity < 1.0 || (this._opacityTexture && this._opacityTexture.isReady());\r\n    }\r\n\r\n    public needAlphaBlendingForMesh(mesh: AbstractMesh): boolean {\r\n        return mesh.visibility < 1.0 || this.needAlphaBlending();\r\n    }\r\n\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new GridMaterialDefines();\r\n        }\r\n\r\n        const defines = <GridMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        if (defines.TRANSPARENT !== this.opacity < 1.0) {\r\n            defines.TRANSPARENT = !defines.TRANSPARENT;\r\n            defines.markAsUnprocessed();\r\n        }\r\n\r\n        if (defines.PREMULTIPLYALPHA != this.preMultiplyAlpha) {\r\n            defines.PREMULTIPLYALPHA = !defines.PREMULTIPLYALPHA;\r\n            defines.markAsUnprocessed();\r\n        }\r\n\r\n        if (defines.MAX_LINE !== this.useMaxLine) {\r\n            defines.MAX_LINE = !defines.MAX_LINE;\r\n            defines.markAsUnprocessed();\r\n        }\r\n\r\n        // Textures\r\n        if (defines._areTexturesDirty) {\r\n            defines._needUVs = false;\r\n            if (scene.texturesEnabled) {\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    if (!this._opacityTexture.isReady()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needUVs = true;\r\n                        defines.OPACITY = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, false, this.fogEnabled, false, defines);\r\n\r\n        // Values that need to be evaluated on every frame\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, scene.getEngine(), this, defines, !!useInstances);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n            scene.resetCachedMaterial();\r\n\r\n            // Attributes\r\n            MaterialHelper.PrepareDefinesForAttributes(mesh, defines, false, false);\r\n            const attribs = [VertexBuffer.PositionKind, VertexBuffer.NormalKind];\r\n\r\n            if (defines.UV1) {\r\n                attribs.push(VertexBuffer.UVKind);\r\n            }\r\n            if (defines.UV2) {\r\n                attribs.push(VertexBuffer.UV2Kind);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            // Defines\r\n            const join = defines.toString();\r\n            subMesh.setEffect(\r\n                scene\r\n                    .getEngine()\r\n                    .createEffect(\r\n                        \"grid\",\r\n                        attribs,\r\n                        [\r\n                            \"projection\",\r\n                            \"mainColor\",\r\n                            \"lineColor\",\r\n                            \"gridControl\",\r\n                            \"gridOffset\",\r\n                            \"vFogInfos\",\r\n                            \"vFogColor\",\r\n                            \"world\",\r\n                            \"view\",\r\n                            \"opacityMatrix\",\r\n                            \"vOpacityInfos\",\r\n                            \"visibility\",\r\n                        ],\r\n                        [\"opacitySampler\"],\r\n                        join,\r\n                        undefined,\r\n                        this.onCompiled,\r\n                        this.onError\r\n                    ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <GridMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        this._activeEffect.setFloat(\"visibility\", mesh.visibility);\r\n\r\n        // Matrices\r\n        if (!defines.INSTANCES || defines.THIN_INSTANCE) {\r\n            this.bindOnlyWorldMatrix(world);\r\n        }\r\n        this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        this._activeEffect.setMatrix(\"projection\", scene.getProjectionMatrix());\r\n\r\n        // Uniforms\r\n        if (this._mustRebind(scene, effect)) {\r\n            this._activeEffect.setColor3(\"mainColor\", this.mainColor);\r\n            this._activeEffect.setColor3(\"lineColor\", this.lineColor);\r\n\r\n            this._activeEffect.setVector3(\"gridOffset\", this.gridOffset);\r\n\r\n            this._gridControl.x = this.gridRatio;\r\n            this._gridControl.y = Math.round(this.majorUnitFrequency);\r\n            this._gridControl.z = this.minorUnitVisibility;\r\n            this._gridControl.w = this.opacity;\r\n            this._activeEffect.setVector4(\"gridControl\", this._gridControl);\r\n\r\n            if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                this._activeEffect.setTexture(\"opacitySampler\", this._opacityTexture);\r\n                this._activeEffect.setFloat2(\"vOpacityInfos\", this._opacityTexture.coordinatesIndex, this._opacityTexture.level);\r\n                this._activeEffect.setMatrix(\"opacityMatrix\", this._opacityTexture.getTextureMatrix());\r\n            }\r\n        }\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    /**\r\n     * Dispose the material and its associated resources.\r\n     * @param forceDisposeEffect will also dispose the used effect when true\r\n     */\r\n    public dispose(forceDisposeEffect?: boolean): void {\r\n        super.dispose(forceDisposeEffect);\r\n    }\r\n\r\n    public clone(name: string): GridMaterial {\r\n        return SerializationHelper.Clone(() => new GridMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.GridMaterial\";\r\n        return serializationObject;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"GridMaterial\";\r\n    }\r\n\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): GridMaterial {\r\n        return SerializationHelper.Parse(() => new GridMaterial(source.name, scene), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.GridMaterial\", GridMaterial);\r\n"]}