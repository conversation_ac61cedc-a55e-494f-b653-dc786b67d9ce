{"version": 3, "file": "mtlFileLoader.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/OBJ/mtlFileLoader.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AAInE;;GAEG;AACH,MAAM,OAAO,aAAa;IAA1B;QAMI;;WAEG;QACI,cAAS,GAAuB,EAAE,CAAC;IA+M9C,CAAC;IA7MG;;;;;;;;;;OAUG;IACI,QAAQ,CAAC,KAAY,EAAE,IAA0B,EAAE,OAAe,EAAE,cAAwC;QAC/G,IAAI,IAAI,YAAY,WAAW,EAAE;YAC7B,OAAO;SACV;QAED,+BAA+B;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,kCAAkC;QAClC,MAAM,iBAAiB,GAAG,KAAK,CAAC;QAChC,uBAAuB;QACvB,IAAI,KAAe,CAAC;QACpB,cAAc;QACd,IAAI,QAAQ,GAA+B,IAAI,CAAC;QAEhD,mBAAmB;QACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAE7B,wBAAwB;YACxB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7C,SAAS;aACZ;YAED,mCAAmC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACnD,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;YAExB,gCAAgC;YAChC,MAAM,KAAK,GAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAErE,+CAA+C;YAC/C,IAAI,GAAG,KAAK,QAAQ,EAAE;gBAClB,oCAAoC;gBACpC,6DAA6D;gBAC7D,IAAI,QAAQ,EAAE;oBACV,kDAAkD;oBAClD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACjC;gBACD,wBAAwB;gBACxB,yDAAyD;gBAEzD,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,cAAc,CAAC;gBAChD,QAAQ,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAC9C,QAAQ,CAAC,gBAAgB,GAAG,cAAc,CAAC;gBAC3C,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;aACxC;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,EAAE;gBACjC,2DAA2D;gBAE3D,kBAAkB;gBAClB,KAAK,GAAa,KAAK,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpE,iBAAiB;gBACjB,kCAAkC;gBAClC,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACnD;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,EAAE;gBACjC,sDAAsD;gBAEtD,iBAAiB;gBACjB,KAAK,GAAa,KAAK,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpE,iBAAiB;gBACjB,kCAAkC;gBAClC,QAAQ,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACnD;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,EAAE;gBACjC,qFAAqF;gBAErF,iBAAiB;gBACjB,KAAK,GAAa,KAAK,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACpE,iBAAiB;gBACjB,iCAAiC;gBACjC,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,EAAE;gBACjC,kCAAkC;gBAClC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC1D,QAAQ,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,GAAG,KAAK,IAAI,IAAI,QAAQ,EAAE;gBACjC,mBAAmB;gBACnB,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;aAC9C;iBAAM,IAAI,GAAG,KAAK,GAAG,IAAI,QAAQ,EAAE;gBAChC,+DAA+D;gBAC/D,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAEnC,SAAS;gBACT,qEAAqE;aACxE;iBAAM,IAAI,GAAG,KAAK,QAAQ,IAAI,QAAQ,EAAE;gBACrC,0CAA0C;gBAC1C,2CAA2C;gBAC3C,QAAQ,CAAC,cAAc,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC9E;iBAAM,IAAI,GAAG,KAAK,QAAQ,IAAI,QAAQ,EAAE;gBACrC,0CAA0C;gBAC1C,QAAQ,CAAC,cAAc,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC9E;iBAAM,IAAI,GAAG,KAAK,QAAQ,IAAI,QAAQ,EAAE;gBACrC,2CAA2C;gBAC3C,2CAA2C;gBAC3C,QAAQ,CAAC,eAAe,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC/E;iBAAM,IAAI,GAAG,KAAK,QAAQ,EAAE;gBACzB,UAAU;gBACV,8BAA8B;gBAC9B,2CAA2C;gBAC3C,EAAE;gBACF,0BAA0B;gBAC1B,EAAE;gBACF,eAAe;aAClB;iBAAM,IAAI,GAAG,KAAK,UAAU,IAAI,QAAQ,EAAE;gBACvC,kBAAkB;gBAClB,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBAC9C,MAAM,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAClD,IAAI,cAAc,GAAqB,IAAI,CAAC;gBAE5C,IAAI,mBAAmB,IAAI,CAAC,EAAE;oBAC1B,cAAc,GAAG,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;oBACjD,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;iBACnD;gBAED,QAAQ,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;gBACnF,IAAI,QAAQ,CAAC,WAAW,IAAI,cAAc,KAAK,IAAI,EAAE;oBACjD,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;iBAC3D;aACJ;iBAAM,IAAI,GAAG,KAAK,OAAO,IAAI,QAAQ,EAAE;gBACpC,+BAA+B;gBAC/B,QAAQ,CAAC,cAAc,GAAG,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAE3E,0BAA0B;aAC7B;iBAAM,IAAI,GAAG,KAAK,OAAO,EAAE;gBACxB,cAAc;gBACd,IAAI,KAAK,KAAK,GAAG,EAAE;oBACf,oBAAoB;iBACvB;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,yBAAyB;iBAC5B;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,cAAc;iBACjB;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,gCAAgC;iBACnC;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,kDAAkD;iBACrD;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,yCAAyC;iBAC5C;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,uEAAuE;iBAC1E;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,sEAAsE;iBACzE;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,iCAAiC;iBACpC;qBAAM,IAAI,KAAK,KAAK,GAAG,EAAE;oBACtB,mDAAmD;iBACtD;qBAAM,IAAI,KAAK,KAAK,IAAI,EAAE;oBACvB,uCAAuC;iBAC1C;aACJ;iBAAM;gBACH,qFAAqF;aACxF;SACJ;QACD,+CAA+C;QAC/C,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACK,MAAM,CAAC,WAAW,CAAC,OAAe,EAAE,KAAa,EAAE,KAAY;QACnE,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,IAAI,GAAG,GAAG,OAAO,CAAC;QAClB,wBAAwB;QACxB,IAAI,OAAO,KAAK,OAAO,EAAE;YACrB,IAAI,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;gBACtB,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;aAC1C;YAED,IAAI,aAAa,GAAG,CAAC,CAAC,EAAE;gBACpB,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;aAC1C;iBAAM;gBACH,GAAG,IAAI,KAAK,CAAC;aAChB;SACJ;QACD,uBAAuB;aAClB;YACD,GAAG,IAAI,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAC1E,CAAC;;AAtND;;GAEG;AACW,8BAAgB,GAAG,IAAI,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\n\r\nimport type { Scene } from \"core/scene\";\r\nimport type { AssetContainer } from \"core/assetContainer\";\r\n/**\r\n * Class reading and parsing the MTL file bundled with the obj file.\r\n */\r\nexport class MTLFileLoader {\r\n    /**\r\n     * Invert Y-Axis of referenced textures on load\r\n     */\r\n    public static INVERT_TEXTURE_Y = true;\r\n\r\n    /**\r\n     * All material loaded from the mtl will be set here\r\n     */\r\n    public materials: StandardMaterial[] = [];\r\n\r\n    /**\r\n     * This function will read the mtl file and create each material described inside\r\n     * This function could be improve by adding :\r\n     * -some component missing (Ni, Tf...)\r\n     * -including the specific options available\r\n     *\r\n     * @param scene defines the scene the material will be created in\r\n     * @param data defines the mtl data to parse\r\n     * @param rootUrl defines the rooturl to use in order to load relative dependencies\r\n     * @param assetContainer defines the asset container to store the material in (can be null)\r\n     */\r\n    public parseMTL(scene: Scene, data: string | ArrayBuffer, rootUrl: string, assetContainer: Nullable<AssetContainer>): void {\r\n        if (data instanceof ArrayBuffer) {\r\n            return;\r\n        }\r\n\r\n        //Split the lines from the file\r\n        const lines = data.split(\"\\n\");\r\n        // whitespace char ie: [ \\t\\r\\n\\f]\r\n        const delimiter_pattern = /\\s+/;\r\n        //Array with RGB colors\r\n        let color: number[];\r\n        //New material\r\n        let material: Nullable<StandardMaterial> = null;\r\n\r\n        //Look at each line\r\n        for (let i = 0; i < lines.length; i++) {\r\n            const line = lines[i].trim();\r\n\r\n            // Blank line or comment\r\n            if (line.length === 0 || line.charAt(0) === \"#\") {\r\n                continue;\r\n            }\r\n\r\n            //Get the first parameter (keyword)\r\n            const pos = line.indexOf(\" \");\r\n            let key = pos >= 0 ? line.substring(0, pos) : line;\r\n            key = key.toLowerCase();\r\n\r\n            //Get the data following the key\r\n            const value: string = pos >= 0 ? line.substring(pos + 1).trim() : \"\";\r\n\r\n            //This mtl keyword will create the new material\r\n            if (key === \"newmtl\") {\r\n                //Check if it is the first material.\r\n                // Materials specifications are described after this keyword.\r\n                if (material) {\r\n                    //Add the previous material in the material array.\r\n                    this.materials.push(material);\r\n                }\r\n                //Create a new material.\r\n                // value is the name of the material read in the mtl file\r\n\r\n                scene._blockEntityCollection = !!assetContainer;\r\n                material = new StandardMaterial(value, scene);\r\n                material._parentContainer = assetContainer;\r\n                scene._blockEntityCollection = false;\r\n            } else if (key === \"kd\" && material) {\r\n                // Diffuse color (color under white light) using RGB values\r\n\r\n                //value  = \"r g b\"\r\n                color = <number[]>value.split(delimiter_pattern, 3).map(parseFloat);\r\n                //color = [r,g,b]\r\n                //Set tghe color into the material\r\n                material.diffuseColor = Color3.FromArray(color);\r\n            } else if (key === \"ka\" && material) {\r\n                // Ambient color (color under shadow) using RGB values\r\n\r\n                //value = \"r g b\"\r\n                color = <number[]>value.split(delimiter_pattern, 3).map(parseFloat);\r\n                //color = [r,g,b]\r\n                //Set tghe color into the material\r\n                material.ambientColor = Color3.FromArray(color);\r\n            } else if (key === \"ks\" && material) {\r\n                // Specular color (color when light is reflected from shiny surface) using RGB values\r\n\r\n                //value = \"r g b\"\r\n                color = <number[]>value.split(delimiter_pattern, 3).map(parseFloat);\r\n                //color = [r,g,b]\r\n                //Set the color into the material\r\n                material.specularColor = Color3.FromArray(color);\r\n            } else if (key === \"ke\" && material) {\r\n                // Emissive color using RGB values\r\n                color = value.split(delimiter_pattern, 3).map(parseFloat);\r\n                material.emissiveColor = Color3.FromArray(color);\r\n            } else if (key === \"ns\" && material) {\r\n                //value = \"Integer\"\r\n                material.specularPower = parseFloat(value);\r\n            } else if (key === \"d\" && material) {\r\n                //d is dissolve for current material. It mean alpha for BABYLON\r\n                material.alpha = parseFloat(value);\r\n\r\n                //Texture\r\n                //This part can be improved by adding the possible options of texture\r\n            } else if (key === \"map_ka\" && material) {\r\n                // ambient texture map with a loaded image\r\n                //We must first get the folder of the image\r\n                material.ambientTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\r\n            } else if (key === \"map_kd\" && material) {\r\n                // Diffuse texture map with a loaded image\r\n                material.diffuseTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\r\n            } else if (key === \"map_ks\" && material) {\r\n                // Specular texture map with a loaded image\r\n                //We must first get the folder of the image\r\n                material.specularTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\r\n            } else if (key === \"map_ns\") {\r\n                //Specular\r\n                //Specular highlight component\r\n                //We must first get the folder of the image\r\n                //\r\n                //Not supported by BABYLON\r\n                //\r\n                //    continue;\r\n            } else if (key === \"map_bump\" && material) {\r\n                //The bump texture\r\n                const values = value.split(delimiter_pattern);\r\n                const bumpMultiplierIndex = values.indexOf(\"-bm\");\r\n                let bumpMultiplier: Nullable<string> = null;\r\n\r\n                if (bumpMultiplierIndex >= 0) {\r\n                    bumpMultiplier = values[bumpMultiplierIndex + 1];\r\n                    values.splice(bumpMultiplierIndex, 2); // remove\r\n                }\r\n\r\n                material.bumpTexture = MTLFileLoader._GetTexture(rootUrl, values.join(\" \"), scene);\r\n                if (material.bumpTexture && bumpMultiplier !== null) {\r\n                    material.bumpTexture.level = parseFloat(bumpMultiplier);\r\n                }\r\n            } else if (key === \"map_d\" && material) {\r\n                // The dissolve of the material\r\n                material.opacityTexture = MTLFileLoader._GetTexture(rootUrl, value, scene);\r\n\r\n                //Options for illumination\r\n            } else if (key === \"illum\") {\r\n                //Illumination\r\n                if (value === \"0\") {\r\n                    //That mean Kd == Kd\r\n                } else if (value === \"1\") {\r\n                    //Color on and Ambient on\r\n                } else if (value === \"2\") {\r\n                    //Highlight on\r\n                } else if (value === \"3\") {\r\n                    //Reflection on and Ray trace on\r\n                } else if (value === \"4\") {\r\n                    //Transparency: Glass on, Reflection: Ray trace on\r\n                } else if (value === \"5\") {\r\n                    //Reflection: Fresnel on and Ray trace on\r\n                } else if (value === \"6\") {\r\n                    //Transparency: Refraction on, Reflection: Fresnel off and Ray trace on\r\n                } else if (value === \"7\") {\r\n                    //Transparency: Refraction on, Reflection: Fresnel on and Ray trace on\r\n                } else if (value === \"8\") {\r\n                    //Reflection on and Ray trace off\r\n                } else if (value === \"9\") {\r\n                    //Transparency: Glass on, Reflection: Ray trace off\r\n                } else if (value === \"10\") {\r\n                    //Casts shadows onto invisible surfaces\r\n                }\r\n            } else {\r\n                // console.log(\"Unhandled expression at line : \" + i +'\\n' + \"with value : \" + line);\r\n            }\r\n        }\r\n        //At the end of the file, add the last material\r\n        if (material) {\r\n            this.materials.push(material);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the texture for the material.\r\n     *\r\n     * If the material is imported from input file,\r\n     * We sanitize the url to ensure it takes the texture from aside the material.\r\n     *\r\n     * @param rootUrl The root url to load from\r\n     * @param value The value stored in the mtl\r\n     * @param scene\r\n     * @returns The Texture\r\n     */\r\n    private static _GetTexture(rootUrl: string, value: string, scene: Scene): Nullable<Texture> {\r\n        if (!value) {\r\n            return null;\r\n        }\r\n\r\n        let url = rootUrl;\r\n        // Load from input file.\r\n        if (rootUrl === \"file:\") {\r\n            let lastDelimiter = value.lastIndexOf(\"\\\\\");\r\n            if (lastDelimiter === -1) {\r\n                lastDelimiter = value.lastIndexOf(\"/\");\r\n            }\r\n\r\n            if (lastDelimiter > -1) {\r\n                url += value.substr(lastDelimiter + 1);\r\n            } else {\r\n                url += value;\r\n            }\r\n        }\r\n        // Not from input file.\r\n        else {\r\n            url += value;\r\n        }\r\n\r\n        return new Texture(url, scene, false, MTLFileLoader.INVERT_TEXTURE_Y);\r\n    }\r\n}\r\n"]}