{"version": 3, "file": "glTFLoaderInterfaces.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/2.0/glTFLoaderInterfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { AnimationGroup } from \"core/Animations/animationGroup\";\r\nimport type { Skeleton } from \"core/Bones/skeleton\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\nimport type { <PERSON>uffer, VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { Light } from \"core/Lights/light\";\r\n\r\nimport type * as GLTF2 from \"babylonjs-gltf2interface\";\r\n\r\n/**\r\n * Loader interface with an index field.\r\n */\r\nexport interface IArrayItem {\r\n    /**\r\n     * The index of this item in the array.\r\n     */\r\n    index: number;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IAccessor extends GLTF2.IAccessor, IArrayItem {\r\n    /** @internal */\r\n    _data?: Promise<ArrayBufferView>;\r\n\r\n    /** @internal */\r\n    _babylonVertexBuffer?: { [kind: string]: Promise<VertexBuffer> };\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IAnimationChannel extends GLTF2.IAnimationChannel, IArrayItem {}\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface _IAnimationSamplerData {\r\n    /** @internal */\r\n    input: Float32Array;\r\n\r\n    /** @internal */\r\n    interpolation: GLTF2.AnimationSamplerInterpolation;\r\n\r\n    /** @internal */\r\n    output: Float32Array;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IAnimationSampler extends GLTF2.IAnimationSampler, IArrayItem {\r\n    /** @internal */\r\n    _data?: Promise<_IAnimationSamplerData>;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IAnimation extends GLTF2.IAnimation, IArrayItem {\r\n    /** @internal */\r\n    channels: IAnimationChannel[];\r\n\r\n    /** @internal */\r\n    samplers: IAnimationSampler[];\r\n\r\n    /** @internal */\r\n    _babylonAnimationGroup?: AnimationGroup;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IBuffer extends GLTF2.IBuffer, IArrayItem {\r\n    /** @internal */\r\n    _data?: Promise<ArrayBufferView>;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IBufferView extends GLTF2.IBufferView, IArrayItem {\r\n    /** @internal */\r\n    _data?: Promise<ArrayBufferView>;\r\n\r\n    /** @internal */\r\n    _babylonBuffer?: Promise<Buffer>;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface ICamera extends GLTF2.ICamera, IArrayItem {\r\n    /** @internal */\r\n    _babylonCamera?: Camera;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IImage extends GLTF2.IImage, IArrayItem {\r\n    /** @internal */\r\n    _data?: Promise<ArrayBufferView>;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMaterialNormalTextureInfo extends GLTF2.IMaterialNormalTextureInfo, ITextureInfo {}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMaterialOcclusionTextureInfo extends GLTF2.IMaterialOcclusionTextureInfo, ITextureInfo {}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMaterialPbrMetallicRoughness extends GLTF2.IMaterialPbrMetallicRoughness {\r\n    /** @internal */\r\n    baseColorTexture?: ITextureInfo;\r\n\r\n    /** @internal */\r\n    metallicRoughnessTexture?: ITextureInfo;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMaterial extends GLTF2.IMaterial, IArrayItem {\r\n    /** @internal */\r\n    pbrMetallicRoughness?: IMaterialPbrMetallicRoughness;\r\n\r\n    /** @internal */\r\n    normalTexture?: IMaterialNormalTextureInfo;\r\n\r\n    /** @internal */\r\n    occlusionTexture?: IMaterialOcclusionTextureInfo;\r\n\r\n    /** @internal */\r\n    emissiveTexture?: ITextureInfo;\r\n\r\n    /** @internal */\r\n    _data?: {\r\n        [babylonDrawMode: number]: {\r\n            babylonMaterial: Material;\r\n            babylonMeshes: AbstractMesh[];\r\n            promise: Promise<void>;\r\n        };\r\n    };\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMesh extends GLTF2.IMesh, IArrayItem {\r\n    /** @internal */\r\n    primitives: IMeshPrimitive[];\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IMeshPrimitive extends GLTF2.IMeshPrimitive, IArrayItem {\r\n    /** @internal */\r\n    _instanceData?: {\r\n        babylonSourceMesh: Mesh;\r\n        promise: Promise<any>;\r\n    };\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface INode extends GLTF2.INode, IArrayItem {\r\n    /** @internal */\r\n    parent?: INode;\r\n\r\n    /** @internal */\r\n    _babylonTransformNode?: TransformNode;\r\n\r\n    /** @internal */\r\n    _babylonTransformNodeForSkin?: TransformNode;\r\n\r\n    /** @internal */\r\n    _primitiveBabylonMeshes?: AbstractMesh[];\r\n\r\n    /** @internal */\r\n    _numMorphTargets?: number;\r\n}\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface _ISamplerData {\r\n    /** @internal */\r\n    noMipMaps: boolean;\r\n\r\n    /** @internal */\r\n    samplingMode: number;\r\n\r\n    /** @internal */\r\n    wrapU: number;\r\n\r\n    /** @internal */\r\n    wrapV: number;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface ISampler extends GLTF2.ISampler, IArrayItem {\r\n    /** @internal */\r\n    _data?: _ISamplerData;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IScene extends GLTF2.IScene, IArrayItem {}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface ISkin extends GLTF2.ISkin, IArrayItem {\r\n    /** @internal */\r\n    _data?: {\r\n        babylonSkeleton: Skeleton;\r\n        promise: Promise<void>;\r\n    };\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface ITexture extends GLTF2.ITexture, IArrayItem {\r\n    /** @internal */\r\n    _textureInfo: ITextureInfo;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface ITextureInfo extends GLTF2.ITextureInfo {\r\n    /** false or undefined if the texture holds color data (true if data are roughness, normal, ...) */\r\n    nonColorData?: boolean;\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\nexport interface IGLTF extends GLTF2.IGLTF {\r\n    /** @internal */\r\n    accessors?: IAccessor[];\r\n\r\n    /** @internal */\r\n    animations?: IAnimation[];\r\n\r\n    /** @internal */\r\n    buffers?: IBuffer[];\r\n\r\n    /** @internal */\r\n    bufferViews?: IBufferView[];\r\n\r\n    /** @internal */\r\n    cameras?: ICamera[];\r\n\r\n    /** @internal */\r\n    images?: IImage[];\r\n\r\n    /** @internal */\r\n    materials?: IMaterial[];\r\n\r\n    /** @internal */\r\n    meshes?: IMesh[];\r\n\r\n    /** @internal */\r\n    nodes?: INode[];\r\n\r\n    /** @internal */\r\n    samplers?: ISampler[];\r\n\r\n    /** @internal */\r\n    scenes?: IScene[];\r\n\r\n    /** @internal */\r\n    skins?: ISkin[];\r\n\r\n    /** @internal */\r\n    textures?: ITexture[];\r\n}\r\n\r\n/**\r\n * Loader interface with additional members.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface IKHRLightsPunctual_Light extends GLTF2.IKHRLightsPunctual_Light, IArrayItem {\r\n    /** @hidden */\r\n    _babylonLight?: Light;\r\n}\r\n"]}