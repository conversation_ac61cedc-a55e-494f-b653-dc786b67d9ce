{"version": 3, "file": "mix.fragment.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/mix/mix.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,mEAAqD;AACrD,4EAA8D;AAC9D,uEAAyD;AACzD,2EAA6D;AAC7D,4EAA8D;AAC9D,gFAAkE;AAClE,0EAA4D;AAC5D,qEAAuD;AACvD,gEAAkD;AAClD,iEAAmD;AACnD,+DAAiD;AACjD,gFAAkE;AAElE,MAAM,IAAI,GAAG,gBAAgB,CAAC;AAC9B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqHd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/helperFunctions\";\nimport \"core/Shaders/ShadersInclude/lightFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightUboDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/shadowsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragment\";\nimport \"core/Shaders/ShadersInclude/depthPrePass\";\nimport \"core/Shaders/ShadersInclude/lightFragment\";\nimport \"core/Shaders/ShadersInclude/fogFragment\";\nimport \"core/Shaders/ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"mixPixelShader\";\nconst shader = `precision highp float;\runiform vec4 vEyePosition;\runiform vec4 vDiffuseColor;\r#ifdef SPECULARTERM\nuniform vec4 vSpecularColor;\r#endif\nvarying vec3 vPositionW;\r#ifdef NORMAL\nvarying vec3 vNormalW;\r#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\r#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#ifdef DIFFUSE\nvarying vec2 vTextureUV;\runiform sampler2D mixMap1Sampler;\runiform vec2 vTextureInfos;\r#ifdef MIXMAP2\nuniform sampler2D mixMap2Sampler;\r#endif\nuniform sampler2D diffuse1Sampler;\runiform sampler2D diffuse2Sampler;\runiform sampler2D diffuse3Sampler;\runiform sampler2D diffuse4Sampler;\runiform vec2 diffuse1Infos;\runiform vec2 diffuse2Infos;\runiform vec2 diffuse3Infos;\runiform vec2 diffuse4Infos;\r#ifdef MIXMAP2\nuniform sampler2D diffuse5Sampler;\runiform sampler2D diffuse6Sampler;\runiform sampler2D diffuse7Sampler;\runiform sampler2D diffuse8Sampler;\runiform vec2 diffuse5Infos;\runiform vec2 diffuse6Infos;\runiform vec2 diffuse7Infos;\runiform vec2 diffuse8Infos;\r#endif\n#endif\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);\rvec4 finalMixColor=vec4(1.,1.,1.,1.);\rvec3 diffuseColor=vDiffuseColor.rgb;\r#ifdef MIXMAP2\nvec4 mixColor2=vec4(1.,1.,1.,1.);\r#endif\n#ifdef SPECULARTERM\nfloat glossiness=vSpecularColor.a;\rvec3 specularColor=vSpecularColor.rgb;\r#else\nfloat glossiness=0.;\r#endif\nfloat alpha=vDiffuseColor.a;\r#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\r#else\nvec3 normalW=vec3(1.0,1.0,1.0);\r#endif\n#ifdef DIFFUSE\nvec4 mixColor=texture2D(mixMap1Sampler,vTextureUV);\r#include<depthPrePass>\nmixColor.rgb*=vTextureInfos.y;\rvec4 diffuse1Color=texture2D(diffuse1Sampler,vTextureUV*diffuse1Infos);\rvec4 diffuse2Color=texture2D(diffuse2Sampler,vTextureUV*diffuse2Infos);\rvec4 diffuse3Color=texture2D(diffuse3Sampler,vTextureUV*diffuse3Infos);\rvec4 diffuse4Color=texture2D(diffuse4Sampler,vTextureUV*diffuse4Infos);\rdiffuse1Color.rgb*=mixColor.r;\rdiffuse2Color.rgb=mix(diffuse1Color.rgb,diffuse2Color.rgb,mixColor.g);\rdiffuse3Color.rgb=mix(diffuse2Color.rgb,diffuse3Color.rgb,mixColor.b);\rfinalMixColor.rgb=mix(diffuse3Color.rgb,diffuse4Color.rgb,1.0-mixColor.a);\r#ifdef MIXMAP2\nmixColor=texture2D(mixMap2Sampler,vTextureUV);\rmixColor.rgb*=vTextureInfos.y;\rvec4 diffuse5Color=texture2D(diffuse5Sampler,vTextureUV*diffuse5Infos);\rvec4 diffuse6Color=texture2D(diffuse6Sampler,vTextureUV*diffuse6Infos);\rvec4 diffuse7Color=texture2D(diffuse7Sampler,vTextureUV*diffuse7Infos);\rvec4 diffuse8Color=texture2D(diffuse8Sampler,vTextureUV*diffuse8Infos);\rdiffuse5Color.rgb=mix(finalMixColor.rgb,diffuse5Color.rgb,mixColor.r);\rdiffuse6Color.rgb=mix(diffuse5Color.rgb,diffuse6Color.rgb,mixColor.g);\rdiffuse7Color.rgb=mix(diffuse6Color.rgb,diffuse7Color.rgb,mixColor.b);\rfinalMixColor.rgb=mix(diffuse7Color.rgb,diffuse8Color.rgb,1.0-mixColor.a);\r#endif\n#endif\n#ifdef VERTEXCOLOR\nfinalMixColor.rgb*=vColor.rgb;\r#endif\nvec3 diffuseBase=vec3(0.,0.,0.);\rlightingInfo info;\rfloat shadow=1.;\r#ifdef SPECULARTERM\nvec3 specularBase=vec3(0.,0.,0.);\r#endif\n#include<lightFragment>[0..maxSimultaneousLights]\n#if defined(VERTEXALPHA) || defined(INSTANCESCOLOR) && defined(INSTANCES)\nalpha*=vColor.a;\r#endif\n#ifdef SPECULARTERM\nvec3 finalSpecular=specularBase*specularColor;\r#else\nvec3 finalSpecular=vec3(0.0);\r#endif\nvec3 finalDiffuse=clamp(diffuseBase*diffuseColor*finalMixColor.rgb,0.0,1.0);\rvec4 color=vec4(finalDiffuse+finalSpecular,alpha);\r#include<fogFragment>\ngl_FragColor=color;\r#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\r`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const mixPixelShader = { name, shader };\n"]}