{"version": 3, "file": "cell.vertex.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/cell/cell.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,oEAAsD;AACtD,mFAAqE;AACrE,wEAA0D;AAC1D,8EAAgE;AAChE,wEAA0D;AAC1D,4EAA8D;AAC9D,uEAAyD;AACzD,mEAAqD;AACrD,+DAAiD;AACjD,wEAA0D;AAC1D,mEAAqD;AACrD,6DAA+C;AAC/C,iEAAmD;AACnD,qEAAuD;AAEvD,MAAM,IAAI,GAAG,kBAAkB,CAAC;AAChC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA0Ed,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,gBAAgB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/bonesDeclaration\";\nimport \"core/Shaders/ShadersInclude/bakedVertexAnimationDeclaration\";\nimport \"core/Shaders/ShadersInclude/instancesDeclaration\";\nimport \"core/Shaders/ShadersInclude/clipPlaneVertexDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogVertexDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightUboDeclaration\";\nimport \"core/Shaders/ShadersInclude/instancesVertex\";\nimport \"core/Shaders/ShadersInclude/bonesVertex\";\nimport \"core/Shaders/ShadersInclude/bakedVertexAnimation\";\nimport \"core/Shaders/ShadersInclude/clipPlaneVertex\";\nimport \"core/Shaders/ShadersInclude/fogVertex\";\nimport \"core/Shaders/ShadersInclude/shadowsVertex\";\nimport \"core/Shaders/ShadersInclude/vertexColorMixing\";\n\nconst name = \"cellVertexShader\";\nconst shader = `precision highp float;\rattribute vec3 position;\r#ifdef NORMAL\nattribute vec3 normal;\r#endif\n#ifdef UV1\nattribute vec2 uv;\r#endif\n#ifdef UV2\nattribute vec2 uv2;\r#endif\n#ifdef VERTEXCOLOR\nattribute vec4 color;\r#endif\n#include<bonesDeclaration>\n#include<bakedVertexAnimationDeclaration>\n#include<instancesDeclaration>\nuniform mat4 view;\runiform mat4 viewProjection;\r#ifdef DIFFUSE\nvarying vec2 vDiffuseUV;\runiform mat4 diffuseMatrix;\runiform vec2 vDiffuseInfos;\r#endif\n#ifdef POINTSIZE\nuniform float pointSize;\r#endif\nvarying vec3 vPositionW;\r#ifdef NORMAL\nvarying vec3 vNormalW;\r#endif\n#ifdef VERTEXCOLOR\nvarying vec4 vColor;\r#endif\n#include<clipPlaneVertexDeclaration>\n#include<fogVertexDeclaration>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\n#include<bonesVertex>\n#include<bakedVertexAnimation>\nvec4 worldPos=finalWorld*vec4(position,1.0);\rgl_Position=viewProjection*worldPos;\rvPositionW=vec3(worldPos);\r#ifdef NORMAL\nvNormalW=normalize(vec3(finalWorld*vec4(normal,0.0)));\r#endif\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\r#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\r#endif\n#ifdef DIFFUSE\nif (vDiffuseInfos.x==0.)\r{\rvDiffuseUV=vec2(diffuseMatrix*vec4(uv,1.0,0.0));\r}\relse\r{\rvDiffuseUV=vec2(diffuseMatrix*vec4(uv2,1.0,0.0));\r}\r#endif\n#include<clipPlaneVertex>\n#include<fogVertex>\n#include<shadowsVertex>[0..maxSimultaneousLights]\n#include<vertexColorMixing>\n#if defined(POINTSIZE) && !defined(WEBGPU)\ngl_PointSize=pointSize;\r#endif\n#define CUSTOM_VERTEX_MAIN_END\n}\r`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const cellVertexShader = { name, shader };\n"]}