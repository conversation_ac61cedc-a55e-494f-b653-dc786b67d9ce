{"version": 3, "file": "KHR_draco_mesh_compression.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_draco_mesh_compression.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,+DAAiD;AAE5E,OAAO,EAAE,YAAY,EAAE,0CAA4B;AACnD,OAAO,EAAE,QAAQ,EAAE,2CAA6B;AAOhD,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAEtD,MAAM,IAAI,GAAG,4BAA4B,CAAC;AAM1C;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,0BAA0B;IAkBnC;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAkBxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC3F,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC5B,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAe,EAAE,SAAyB,EAAE,WAAiB;QACrF,OAAO,UAAU,CAAC,kBAAkB,CAAqC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACpI,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,EAAE;gBAC7B,IAAI,SAAS,CAAC,IAAI,6CAAqC,IAAI,SAAS,CAAC,IAAI,wCAAgC,EAAE;oBACvG,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,sBAAsB,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;iBACrE;gBAED,+BAA+B;gBAC/B,IAAI,SAAS,CAAC,IAAI,6CAAqC,EAAE;oBACrD,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,UAAU,SAAS,CAAC,IAAI,6BAA6B,CAAC,CAAC;iBACpF;aACJ;YAED,MAAM,UAAU,GAEZ,EAAE,CAAC;YACP,MAAM,QAAQ,GAEV,EAAE,CAAC;YACP,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;gBACjD,MAAM,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,QAAQ,KAAK,SAAS,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;oBACpE,OAAO;iBACV;gBAED,UAAU,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;gBAC5B,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,eAAe,IAAI,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzH,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,aAAa,2CAAgC,EAAE;oBAC/E,IAAI,OAAO,GAAG,CAAC,CAAC;oBAChB,QAAQ,QAAQ,CAAC,aAAa,EAAE;wBAC5B;4BACI,OAAO,GAAG,KAAK,CAAC;4BAChB,MAAM;wBACV;4BACI,OAAO,GAAG,KAAK,CAAC;4BAChB,MAAM;wBACV;4BACI,OAAO,GAAG,OAAO,CAAC;4BAClB,MAAM;wBACV;4BACI,OAAO,GAAG,OAAO,CAAC;4BAClB,MAAM;qBACb;oBACD,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;iBAC5B;gBAED,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,EAAE,CAAC;gBACtD,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC7C,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACrC;YACL,CAAC,CAAC;YAEF,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;YACrD,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;YACjD,aAAa,CAAC,SAAS,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;YACnD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;YACjD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,YAAY,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC;YAClD,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC5D,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,mBAAmB,CAAC,CAAC;YAC7D,aAAa,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;YAEjD,MAAM,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,UAAU,CAAqB,CAAC;YAC5H,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE;gBACnC,UAAU,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC9H,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,OAAO,CAAC;oBAC3E,OAAO,gBAAgB;yBAClB,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;yBAC3C,IAAI,CAAC,CAAC,iBAAiB,EAAE,EAAE;wBACxB,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAClF,iBAAiB,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;wBACnD,OAAO,eAAe,CAAC;oBAC3B,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBACb,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC;aACN;YAED,OAAO,UAAU,CAAC,qBAAqB,CAAC;QAC5C,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import { DracoCompression } from \"core/Meshes/Compression/dracoCompression\";\r\nimport type { Nullable } from \"core/types\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Geometry } from \"core/Meshes/geometry\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\n\r\nimport { MeshPrimitiveMode, AccessorComponentType } from \"babylonjs-gltf2interface\";\r\nimport type { IKHRDracoMeshCompression } from \"babylonjs-gltf2interface\";\r\nimport type { IMeshPrimitive, IBufferView } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\n\r\nconst NAME = \"KHR_draco_mesh_compression\";\r\n\r\ninterface IBufferViewDraco extends IBufferView {\r\n    _dracoBabylonGeometry?: Promise<Geometry>;\r\n}\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_draco_mesh_compression/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_draco_mesh_compression implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * The draco compression used to decode vertex data or DracoCompression.Default if not defined\r\n     */\r\n    public dracoCompression?: DracoCompression;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = DracoCompression.DecoderAvailable && this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        delete this.dracoCompression;\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadVertexDataAsync(context: string, primitive: IMeshPrimitive, babylonMesh: Mesh): Nullable<Promise<Geometry>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRDracoMeshCompression, Geometry>(context, primitive, this.name, (extensionContext, extension) => {\r\n            if (primitive.mode != undefined) {\r\n                if (primitive.mode !== MeshPrimitiveMode.TRIANGLE_STRIP && primitive.mode !== MeshPrimitiveMode.TRIANGLES) {\r\n                    throw new Error(`${context}: Unsupported mode ${primitive.mode}`);\r\n                }\r\n\r\n                // TODO: handle triangle strips\r\n                if (primitive.mode === MeshPrimitiveMode.TRIANGLE_STRIP) {\r\n                    throw new Error(`${context}: Mode ${primitive.mode} is not currently supported`);\r\n                }\r\n            }\r\n\r\n            const attributes: {\r\n                [kind: string]: number;\r\n            } = {};\r\n            const dividers: {\r\n                [kind: string]: number;\r\n            } = {};\r\n            const loadAttribute = (name: string, kind: string) => {\r\n                const uniqueId = extension.attributes[name];\r\n                if (uniqueId === undefined || primitive.attributes[name] === undefined) {\r\n                    return;\r\n                }\r\n\r\n                attributes[kind] = uniqueId;\r\n                const accessor = ArrayItem.Get(`${context}/attributes/${name}`, this._loader.gltf.accessors, primitive.attributes[name]);\r\n                if (accessor.normalized && accessor.componentType !== AccessorComponentType.FLOAT) {\r\n                    let divider = 1;\r\n                    switch (accessor.componentType) {\r\n                        case AccessorComponentType.BYTE:\r\n                            divider = 127.0;\r\n                            break;\r\n                        case AccessorComponentType.UNSIGNED_BYTE:\r\n                            divider = 255.0;\r\n                            break;\r\n                        case AccessorComponentType.SHORT:\r\n                            divider = 32767.0;\r\n                            break;\r\n                        case AccessorComponentType.UNSIGNED_SHORT:\r\n                            divider = 65535.0;\r\n                            break;\r\n                    }\r\n                    dividers[kind] = divider;\r\n                }\r\n\r\n                babylonMesh._delayInfo = babylonMesh._delayInfo || [];\r\n                if (babylonMesh._delayInfo.indexOf(kind) === -1) {\r\n                    babylonMesh._delayInfo.push(kind);\r\n                }\r\n            };\r\n\r\n            loadAttribute(\"POSITION\", VertexBuffer.PositionKind);\r\n            loadAttribute(\"NORMAL\", VertexBuffer.NormalKind);\r\n            loadAttribute(\"TANGENT\", VertexBuffer.TangentKind);\r\n            loadAttribute(\"TEXCOORD_0\", VertexBuffer.UVKind);\r\n            loadAttribute(\"TEXCOORD_1\", VertexBuffer.UV2Kind);\r\n            loadAttribute(\"TEXCOORD_2\", VertexBuffer.UV3Kind);\r\n            loadAttribute(\"TEXCOORD_3\", VertexBuffer.UV4Kind);\r\n            loadAttribute(\"TEXCOORD_4\", VertexBuffer.UV5Kind);\r\n            loadAttribute(\"TEXCOORD_5\", VertexBuffer.UV6Kind);\r\n            loadAttribute(\"JOINTS_0\", VertexBuffer.MatricesIndicesKind);\r\n            loadAttribute(\"WEIGHTS_0\", VertexBuffer.MatricesWeightsKind);\r\n            loadAttribute(\"COLOR_0\", VertexBuffer.ColorKind);\r\n\r\n            const bufferView = ArrayItem.Get(extensionContext, this._loader.gltf.bufferViews, extension.bufferView) as IBufferViewDraco;\r\n            if (!bufferView._dracoBabylonGeometry) {\r\n                bufferView._dracoBabylonGeometry = this._loader.loadBufferViewAsync(`/bufferViews/${bufferView.index}`, bufferView).then((data) => {\r\n                    const dracoCompression = this.dracoCompression || DracoCompression.Default;\r\n                    return dracoCompression\r\n                        .decodeMeshAsync(data, attributes, dividers)\r\n                        .then((babylonVertexData) => {\r\n                            const babylonGeometry = new Geometry(babylonMesh.name, this._loader.babylonScene);\r\n                            babylonVertexData.applyToGeometry(babylonGeometry);\r\n                            return babylonGeometry;\r\n                        })\r\n                        .catch((error) => {\r\n                            throw new Error(`${context}: ${error.message}`);\r\n                        });\r\n                });\r\n            }\r\n\r\n            return bufferView._dracoBabylonGeometry;\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_draco_mesh_compression(loader));\r\n"]}