{"version": 3, "file": "grid.fragment.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/grid/grid.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,0EAA4D;AAC5D,+DAAiD;AACjD,gFAAkE;AAElE,MAAM,IAAI,GAAG,iBAAiB,CAAC;AAC/B,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwFd,CAAC;AACF,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,eAAe,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/fogFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogFragment\";\nimport \"core/Shaders/ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"gridPixelShader\";\nconst shader = `#extension GL_OES_standard_derivatives : enable\n#define SQRT2 1.41421356\n#define PI 3.14159\nprecision highp float;\runiform float visibility;\runiform vec3 mainColor;\runiform vec3 lineColor;\runiform vec4 gridControl;\runiform vec3 gridOffset;\rvarying vec3 vPosition;\rvarying vec3 vNormal;\r#include<fogFragmentDeclaration>\n#ifdef OPACITY\nvarying vec2 vOpacityUV;\runiform sampler2D opacitySampler;\runiform vec2 vOpacityInfos;\r#endif\nfloat getDynamicVisibility(float position) {\rfloat majorGridFrequency=gridControl.y;\rif (floor(position+0.5)==floor(position/majorGridFrequency+0.5)*majorGridFrequency)\r{\rreturn 1.0;\r} \rreturn gridControl.z;\r}\rfloat getAnisotropicAttenuation(float differentialLength) {\rconst float maxNumberOfLines=10.0;\rreturn clamp(1.0/(differentialLength+1.0)-1.0/maxNumberOfLines,0.0,1.0);\r}\rfloat isPointOnLine(float position,float differentialLength) {\rfloat fractionPartOfPosition=position-floor(position+0.5); \rfractionPartOfPosition/=differentialLength; \rfractionPartOfPosition=clamp(fractionPartOfPosition,-1.,1.);\rfloat result=0.5+0.5*cos(fractionPartOfPosition*PI); \rreturn result; \r}\rfloat contributionOnAxis(float position) {\rfloat differentialLength=length(vec2(dFdx(position),dFdy(position)));\rdifferentialLength*=SQRT2; \rfloat result=isPointOnLine(position,differentialLength);\rfloat dynamicVisibility=getDynamicVisibility(position);\rresult*=dynamicVisibility;\rfloat anisotropicAttenuation=getAnisotropicAttenuation(differentialLength);\rresult*=anisotropicAttenuation;\rreturn result;\r}\rfloat normalImpactOnAxis(float x) {\rfloat normalImpact=clamp(1.0-3.0*abs(x*x*x),0.0,1.0);\rreturn normalImpact;\r}\r#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_FRAGMENT_MAIN_BEGIN\nfloat gridRatio=gridControl.x;\rvec3 gridPos=(vPosition+gridOffset.xyz)/gridRatio;\rfloat x=contributionOnAxis(gridPos.x);\rfloat y=contributionOnAxis(gridPos.y);\rfloat z=contributionOnAxis(gridPos.z);\rvec3 normal=normalize(vNormal);\rx*=normalImpactOnAxis(normal.x);\ry*=normalImpactOnAxis(normal.y);\rz*=normalImpactOnAxis(normal.z);\r#ifdef MAX_LINE \nfloat grid=clamp(max(max(x,y),z),0.,1.);\r#else\nfloat grid=clamp(x+y+z,0.,1.);\r#endif\nvec3 color=mix(mainColor,lineColor,grid);\r#ifdef FOG\n#include<fogFragment>\n#endif\nfloat opacity=1.0;\r#ifdef TRANSPARENT\nopacity=clamp(grid,0.08,gridControl.w*grid);\r#endif \n#ifdef OPACITY\nopacity*=texture2D(opacitySampler,vOpacityUV).a;\r#endif \ngl_FragColor=vec4(color.rgb,opacity*visibility);\r#ifdef TRANSPARENT\n#ifdef PREMULTIPLYALPHA\ngl_FragColor.rgb*=opacity;\r#endif\n#else \n#endif\n#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}\r`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const gridPixelShader = { name, shader };\n"]}