{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/index.ts"], "names": [], "mappings": "AAAA,cAAc,0BAA0B,CAAC;AACzC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,2BAA2B,CAAC;AAC1C,cAAc,oBAAoB,CAAC;AACnC,cAAc,8BAA8B,CAAC;AAC7C,cAAc,uBAAuB,CAAC;AACtC,cAAc,uCAAuC,CAAC;AACtD,cAAc,uBAAuB,CAAC;AACtC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,6BAA6B,CAAC;AAC5C,cAAc,mCAAmC,CAAC;AAClD,cAAc,uBAAuB,CAAC;AACtC,cAAc,0BAA0B,CAAC;AACzC,cAAc,qBAAqB,CAAC;AACpC,cAAc,0BAA0B,CAAC;AACzC,cAAc,8BAA8B,CAAC;AAC7C,cAAc,8BAA8B,CAAC;AAC7C,cAAc,wBAAwB,CAAC;AACvC,cAAc,yBAAyB,CAAC;AACxC,cAAc,sBAAsB,CAAC;AACrC,cAAc,yBAAyB,CAAC;AACxC,cAAc,mBAAmB,CAAC;AAClC,cAAc,yBAAyB,CAAC;AACxC,cAAc,sBAAsB,CAAC;AACrC,cAAc,YAAY,CAAC;AAC3B,cAAc,sBAAsB,CAAC;AACrC,cAAc,oBAAoB,CAAC;AACnC,cAAc,oBAAoB,CAAC", "sourcesContent": ["export * from \"./EXT_lights_image_based\";\r\nexport * from \"./EXT_mesh_gpu_instancing\";\r\nexport * from \"./EXT_meshopt_compression\";\r\nexport * from \"./EXT_texture_webp\";\r\nexport * from \"./KHR_draco_mesh_compression\";\r\nexport * from \"./KHR_lights_punctual\";\r\nexport * from \"./KHR_materials_pbrSpecularGlossiness\";\r\nexport * from \"./KHR_materials_unlit\";\r\nexport * from \"./KHR_materials_clearcoat\";\r\nexport * from \"./KHR_materials_iridescence\";\r\nexport * from \"./KHR_materials_emissive_strength\";\r\nexport * from \"./KHR_materials_sheen\";\r\nexport * from \"./KHR_materials_specular\";\r\nexport * from \"./KHR_materials_ior\";\r\nexport * from \"./KHR_materials_variants\";\r\nexport * from \"./KHR_materials_transmission\";\r\nexport * from \"./KHR_materials_translucency\";\r\nexport * from \"./KHR_materials_volume\";\r\nexport * from \"./KHR_mesh_quantization\";\r\nexport * from \"./KHR_texture_basisu\";\r\nexport * from \"./KHR_texture_transform\";\r\nexport * from \"./KHR_xmp_json_ld\";\r\nexport * from \"./KHR_animation_pointer\";\r\nexport * from \"./MSFT_audio_emitter\";\r\nexport * from \"./MSFT_lod\";\r\nexport * from \"./MSFT_minecraftMesh\";\r\nexport * from \"./MSFT_sRGBFactors\";\r\nexport * from \"./ExtrasAsMetadata\";\r\n"]}