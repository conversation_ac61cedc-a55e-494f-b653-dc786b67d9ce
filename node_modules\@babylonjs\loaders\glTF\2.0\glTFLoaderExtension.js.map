{"version": 3, "file": "glTFLoaderExtension.js", "sourceRoot": "", "sources": ["../../../../../lts/loaders/generated/glTF/2.0/glTFLoaderExtension.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { Animation } from \"core/Animations/animation\";\r\nimport type { AnimationGroup } from \"core/Animations/animationGroup\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { Geometry } from \"core/Meshes/geometry\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { IDisposable } from \"core/scene\";\r\nimport type {\r\n    IScene,\r\n    INode,\r\n    IMesh,\r\n    ISkin,\r\n    ICamera,\r\n    IMeshPrimitive,\r\n    IMaterial,\r\n    ITextureInfo,\r\n    IAnimation,\r\n    ITexture,\r\n    IBufferView,\r\n    IBuffer,\r\n    IAnimationChannel,\r\n} from \"./glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension as IGLTFBaseLoaderExtension } from \"../glTFFileLoader\";\r\nimport type { IProperty } from \"babylonjs-gltf2interface\";\r\nimport type { IAnimatable } from \"core/Animations/animatable.interface\";\r\n\r\n/**\r\n * Interface for a glTF loader extension.\r\n */\r\nexport interface IGLTFLoaderExtension extends IGLTFBaseLoaderExtension, IDisposable {\r\n    /**\r\n     * Called after the loader state changes to LOADING.\r\n     */\r\n    onLoading?(): void;\r\n\r\n    /**\r\n     * Called after the loader state changes to READY.\r\n     */\r\n    onReady?(): void;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading scenes.\r\n     * @param context The context when loading the asset\r\n     * @param scene The glTF scene property\r\n     * @returns A promise that resolves when the load is complete or null if not handled\r\n     */\r\n    loadSceneAsync?(context: string, scene: IScene): Nullable<Promise<void>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading nodes.\r\n     * @param context The context when loading the asset\r\n     * @param node The glTF node property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon transform node when the load is complete or null if not handled\r\n     */\r\n    loadNodeAsync?(context: string, node: INode, assign: (babylonMesh: TransformNode) => void): Nullable<Promise<TransformNode>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading cameras.\r\n     * @param context The context when loading the asset\r\n     * @param camera The glTF camera property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon camera when the load is complete or null if not handled\r\n     */\r\n    loadCameraAsync?(context: string, camera: ICamera, assign: (babylonCamera: Camera) => void): Nullable<Promise<Camera>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading vertex data for mesh primitives.\r\n     * @param context The context when loading the asset\r\n     * @param primitive The glTF mesh primitive property\r\n     * @returns A promise that resolves with the loaded geometry when the load is complete or null if not handled\r\n     */\r\n    _loadVertexDataAsync?(context: string, primitive: IMeshPrimitive, babylonMesh: Mesh): Nullable<Promise<Geometry>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading data for mesh primitives.\r\n     * @param context The context when loading the asset\r\n     * @param name The mesh name when loading the asset\r\n     * @param node The glTF node when loading the asset\r\n     * @param mesh The glTF mesh when loading the asset\r\n     * @param primitive The glTF mesh primitive property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded mesh when the load is complete or null if not handled\r\n     */\r\n    _loadMeshPrimitiveAsync?(\r\n        context: string,\r\n        name: string,\r\n        node: INode,\r\n        mesh: IMesh,\r\n        primitive: IMeshPrimitive,\r\n        assign: (babylonMesh: AbstractMesh) => void\r\n    ): Nullable<Promise<AbstractMesh>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading materials. Load material creates the material and then loads material properties.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon material when the load is complete or null if not handled\r\n     */\r\n    _loadMaterialAsync?(\r\n        context: string,\r\n        material: IMaterial,\r\n        babylonMesh: Nullable<Mesh>,\r\n        babylonDrawMode: number,\r\n        assign: (babylonMaterial: Material) => void\r\n    ): Nullable<Promise<Material>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when creating materials.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonDrawMode The draw mode for the Babylon material\r\n     * @returns The Babylon material or null if not handled\r\n     */\r\n    createMaterial?(context: string, material: IMaterial, babylonDrawMode: number): Nullable<Material>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading material properties.\r\n     * @param context The context when loading the asset\r\n     * @param material The glTF material property\r\n     * @param babylonMaterial The Babylon material\r\n     * @returns A promise that resolves when the load is complete or null if not handled\r\n     */\r\n    loadMaterialPropertiesAsync?(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading texture infos.\r\n     * @param context The context when loading the asset\r\n     * @param textureInfo The glTF texture info property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon texture when the load is complete or null if not handled\r\n     */\r\n    loadTextureInfoAsync?(context: string, textureInfo: ITextureInfo, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading textures.\r\n     * @param context The context when loading the asset\r\n     * @param texture The glTF texture property\r\n     * @param assign A function called synchronously after parsing the glTF properties\r\n     * @returns A promise that resolves with the loaded Babylon texture when the load is complete or null if not handled\r\n     */\r\n    _loadTextureAsync?(context: string, texture: ITexture, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading animations.\r\n     * @param context The context when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @returns A promise that resolves with the loaded Babylon animation group when the load is complete or null if not handled\r\n     */\r\n    loadAnimationAsync?(context: string, animation: IAnimation): Nullable<Promise<AnimationGroup>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behvaior when loading animation channels.\r\n     * @param context The context when loading the asset\r\n     * @param animationContext The context of the animation when loading the asset\r\n     * @param animation The glTF animation property\r\n     * @param channel The glTF animation channel property\r\n     * @param onLoad Called for each animation loaded\r\n     * @returns A void promise that resolves when the load is complete or null if not handled\r\n     */\r\n    _loadAnimationChannelAsync?(\r\n        context: string,\r\n        animationContext: string,\r\n        animation: IAnimation,\r\n        channel: IAnimationChannel,\r\n        onLoad: (babylonAnimatable: IAnimatable, babylonAnimation: Animation) => void\r\n    ): Nullable<Promise<void>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading skins.\r\n     * @param context The context when loading the asset\r\n     * @param node The glTF node property\r\n     * @param skin The glTF skin property\r\n     * @returns A promise that resolves when the load is complete or null if not handled\r\n     */\r\n    _loadSkinAsync?(context: string, node: INode, skin: ISkin): Nullable<Promise<void>>;\r\n\r\n    /**\r\n     * @internal\r\n     * Define this method to modify the default behavior when loading uris.\r\n     * @param context The context when loading the asset\r\n     * @param property The glTF property associated with the uri\r\n     * @param uri The uri to load\r\n     * @returns A promise that resolves with the loaded data when the load is complete or null if not handled\r\n     */\r\n    _loadUriAsync?(context: string, property: IProperty, uri: string): Nullable<Promise<ArrayBufferView>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading buffer views.\r\n     * @param context The context when loading the asset\r\n     * @param bufferView The glTF buffer view property\r\n     * @returns A promise that resolves with the loaded data when the load is complete or null if not handled\r\n     */\r\n    loadBufferViewAsync?(context: string, bufferView: IBufferView): Nullable<Promise<ArrayBufferView>>;\r\n\r\n    /**\r\n     * Define this method to modify the default behavior when loading buffers.\r\n     * @param context The context when loading the asset\r\n     * @param buffer The glTF buffer property\r\n     * @param byteOffset The byte offset to load\r\n     * @param byteLength The byte length to load\r\n     * @returns A promise that resolves with the loaded data when the load is complete or null if not handled\r\n     */\r\n    loadBufferAsync?(context: string, buffer: IBuffer, byteOffset: number, byteLength: number): Nullable<Promise<ArrayBufferView>>;\r\n}\r\n"]}