{"version": 3, "file": "KHR_materials_iridescence.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_iridescence.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,2BAA2B,CAAC;AAEzC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,yBAAyB;IAkBlC;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAA2B,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACzH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YAClG,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,+BAA+B,CAAC,OAAe,EAAE,UAAoC,EAAE,eAAyB;;QACpH,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,eAAe,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;QAE7C,eAAe,CAAC,WAAW,CAAC,SAAS,GAAG,MAAA,UAAU,CAAC,iBAAiB,mCAAI,CAAC,CAAC;QAC1E,eAAe,CAAC,WAAW,CAAC,iBAAiB,GAAG,MAAA,MAAA,UAAU,CAAC,cAAc,mCAAK,UAAkB,CAAC,cAAc,mCAAI,GAAG,CAAC;QACvH,eAAe,CAAC,WAAW,CAAC,gBAAgB,GAAG,MAAA,UAAU,CAAC,2BAA2B,mCAAI,GAAG,CAAC;QAC7F,eAAe,CAAC,WAAW,CAAC,gBAAgB,GAAG,MAAA,UAAU,CAAC,2BAA2B,mCAAI,GAAG,CAAC;QAE7F,IAAI,UAAU,CAAC,kBAAkB,EAAE;YAC/B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,qBAAqB,EAAE,UAAU,CAAC,kBAAkB,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC1G,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,0BAA0B,CAAC;gBACjE,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;YAClD,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,2BAA2B,EAAE;YACxC,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,8BAA8B,EAAE,UAAU,CAAC,2BAA2B,EAAE,CAAC,OAAO,EAAE,EAAE;gBAC5H,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,0BAA0B,CAAC;gBACjE,eAAe,CAAC,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC;YAC3D,CAAC,CAAC,CACL,CAAC;SACL;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsIridescence } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_iridescence\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_iridescence/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_iridescence implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 195;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsIridescence>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadIridescencePropertiesAsync(extensionContext, extension, babylonMaterial));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadIridescencePropertiesAsync(context: string, properties: IKHRMaterialsIridescence, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        babylonMaterial.iridescence.isEnabled = true;\r\n\r\n        babylonMaterial.iridescence.intensity = properties.iridescenceFactor ?? 0;\r\n        babylonMaterial.iridescence.indexOfRefraction = properties.iridescenceIor ?? (properties as any).iridescenceIOR ?? 1.3;\r\n        babylonMaterial.iridescence.minimumThickness = properties.iridescenceThicknessMinimum ?? 100;\r\n        babylonMaterial.iridescence.maximumThickness = properties.iridescenceThicknessMaximum ?? 400;\r\n\r\n        if (properties.iridescenceTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/iridescenceTexture`, properties.iridescenceTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Iridescence Intensity)`;\r\n                    babylonMaterial.iridescence.texture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.iridescenceThicknessTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/iridescenceThicknessTexture`, properties.iridescenceThicknessTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Iridescence Thickness)`;\r\n                    babylonMaterial.iridescence.thicknessTexture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_iridescence(loader));\r\n"]}