{"version": 3, "file": "KHR_materials_sheen.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_sheen.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAG/C,MAAM,IAAI,GAAG,qBAAqB,CAAC;AAEnC;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,mBAAmB;IAkB5B;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAqB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACnH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB,CAAC,OAAe,EAAE,UAA8B,EAAE,eAAyB;QACxG,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAE3C,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvC,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QAEpC,IAAI,UAAU,CAAC,gBAAgB,IAAI,SAAS,EAAE;YAC1C,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;SAC/E;aAAM;YACH,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;SAChD;QAED,IAAI,UAAU,CAAC,iBAAiB,EAAE;YAC9B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,oBAAoB,EAAE,UAAU,CAAC,iBAAiB,EAAE,CAAC,OAAO,EAAE,EAAE;gBACxG,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,gBAAgB,CAAC;gBACvD,eAAe,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5C,CAAC,CAAC,CACL,CAAC;SACL;QAED,IAAI,UAAU,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC/C,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,oBAAoB,CAAC;SACrE;aAAM;YACH,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;SACvC;QAED,IAAI,UAAU,CAAC,qBAAqB,EAAE;YACjC,UAAU,CAAC,qBAAsC,CAAC,YAAY,GAAG,IAAI,CAAC;YACvE,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,wBAAwB,EAAE,UAAU,CAAC,qBAAqB,EAAE,CAAC,OAAO,EAAE,EAAE;gBAChH,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,oBAAoB,CAAC;gBAC3D,eAAe,CAAC,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC;YACrD,CAAC,CAAC,CACL,CAAC;SACL;QAED,eAAe,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC3C,eAAe,CAAC,KAAK,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAE1D,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { IKHRMaterialsSheen } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_sheen\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_sheen/README.md)\r\n * [Playground Sample](https://www.babylonjs-playground.com/frame.html#BNIZX6#4)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_sheen implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 190;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsSheen>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadSheenPropertiesAsync(extensionContext, extension, babylonMaterial));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadSheenPropertiesAsync(context: string, properties: IKHRMaterialsSheen, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n\r\n        babylonMaterial.sheen.isEnabled = true;\r\n        babylonMaterial.sheen.intensity = 1;\r\n\r\n        if (properties.sheenColorFactor != undefined) {\r\n            babylonMaterial.sheen.color = Color3.FromArray(properties.sheenColorFactor);\r\n        } else {\r\n            babylonMaterial.sheen.color = Color3.Black();\r\n        }\r\n\r\n        if (properties.sheenColorTexture) {\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/sheenColorTexture`, properties.sheenColorTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Sheen Color)`;\r\n                    babylonMaterial.sheen.texture = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        if (properties.sheenRoughnessFactor !== undefined) {\r\n            babylonMaterial.sheen.roughness = properties.sheenRoughnessFactor;\r\n        } else {\r\n            babylonMaterial.sheen.roughness = 0;\r\n        }\r\n\r\n        if (properties.sheenRoughnessTexture) {\r\n            (properties.sheenRoughnessTexture as ITextureInfo).nonColorData = true;\r\n            promises.push(\r\n                this._loader.loadTextureInfoAsync(`${context}/sheenRoughnessTexture`, properties.sheenRoughnessTexture, (texture) => {\r\n                    texture.name = `${babylonMaterial.name} (Sheen Roughness)`;\r\n                    babylonMaterial.sheen.textureRoughness = texture;\r\n                })\r\n            );\r\n        }\r\n\r\n        babylonMaterial.sheen.albedoScaling = true;\r\n        babylonMaterial.sheen.useRoughnessFromMainTexture = false;\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_sheen(loader));\r\n"]}