{"version": 3, "file": "KHR_materials_unlit.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_unlit.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,MAAM,IAAI,GAAG,qBAAqB,CAAC;AAEnC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,mBAAmB;IAkB5B;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;YACpE,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,yBAAyB,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC7F,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;QAC3C,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC;QAE7B,MAAM,UAAU,GAAG,QAAQ,CAAC,oBAAoB,CAAC;QACjD,IAAI,UAAU,EAAE;YACZ,IAAI,UAAU,CAAC,eAAe,EAAE;gBAC5B,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;gBAC3E,eAAe,CAAC,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;aACzD;iBAAM;gBACH,eAAe,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;aAChD;YAED,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC7B,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,mBAAmB,EAAE,UAAU,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE,EAAE;oBACtG,OAAO,CAAC,IAAI,GAAG,GAAG,eAAe,CAAC,IAAI,eAAe,CAAC;oBACtD,eAAe,CAAC,aAAa,GAAG,OAAO,CAAC;gBAC5C,CAAC,CAAC,CACL,CAAC;aACL;SACJ;QAED,IAAI,QAAQ,CAAC,WAAW,EAAE;YACtB,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;YACxC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QAE7E,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IAChD,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\n\r\nimport type { IMaterial } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\n\r\nconst NAME = \"KHR_materials_unlit\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_unlit/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_unlit implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 210;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync(context, material, this.name, () => {\r\n            return this._loadUnlitPropertiesAsync(context, material, babylonMaterial);\r\n        });\r\n    }\r\n\r\n    private _loadUnlitPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        const promises = new Array<Promise<any>>();\r\n        babylonMaterial.unlit = true;\r\n\r\n        const properties = material.pbrMetallicRoughness;\r\n        if (properties) {\r\n            if (properties.baseColorFactor) {\r\n                babylonMaterial.albedoColor = Color3.FromArray(properties.baseColorFactor);\r\n                babylonMaterial.alpha = properties.baseColorFactor[3];\r\n            } else {\r\n                babylonMaterial.albedoColor = Color3.White();\r\n            }\r\n\r\n            if (properties.baseColorTexture) {\r\n                promises.push(\r\n                    this._loader.loadTextureInfoAsync(`${context}/baseColorTexture`, properties.baseColorTexture, (texture) => {\r\n                        texture.name = `${babylonMaterial.name} (Base Color)`;\r\n                        babylonMaterial.albedoTexture = texture;\r\n                    })\r\n                );\r\n            }\r\n        }\r\n\r\n        if (material.doubleSided) {\r\n            babylonMaterial.backFaceCulling = false;\r\n            babylonMaterial.twoSidedLighting = true;\r\n        }\r\n\r\n        this._loader.loadMaterialAlphaProperties(context, material, babylonMaterial);\r\n\r\n        return Promise.all(promises).then(() => {});\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_unlit(loader));\r\n"]}