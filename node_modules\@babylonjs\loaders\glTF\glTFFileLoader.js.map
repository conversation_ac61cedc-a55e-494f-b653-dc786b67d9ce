{"version": 3, "file": "glTFFileLoader.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/glTF/glTFFileLoader.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,UAAU,EAAE,2CAA6B;AAClD,OAAO,EAAE,KAAK,EAAE,sCAAwB;AAaxC,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,OAAO,EAAE,cAAc,EAAE,0CAA4B;AAIrD,OAAO,EAAE,MAAM,EAAE,uCAAyB;AAE1C,OAAO,EAAE,UAAU,EAAE,2CAA6B;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,uBAAuB,EAAE,0CAA4B;AAC9D,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,sCAAwB;AAU3D,SAAS,SAAS,CAAC,WAAwB,EAAE,UAAkB,EAAE,UAAkB;IAC/E,IAAI;QACA,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;KAC/E;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;KAC5B;AACL,CAAC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,8BAUX;AAVD,WAAY,8BAA8B;IACtC;;OAEG;IACH,mFAAI,CAAA;IAEJ;;OAEG;IACH,+GAAkB,CAAA;AACtB,CAAC,EAVW,8BAA8B,KAA9B,8BAA8B,QAUzC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,4BAeX;AAfD,WAAY,4BAA4B;IACpC;;OAEG;IACH,+EAAI,CAAA;IAEJ;;OAEG;IACH,iFAAK,CAAA;IAEL;;OAEG;IACH,6EAAG,CAAA;AACP,CAAC,EAfW,4BAA4B,KAA5B,4BAA4B,QAevC;AAsCD;;GAEG;AACH,MAAM,CAAN,IAAY,eAeX;AAfD,WAAY,eAAe;IACvB;;OAEG;IACH,2DAAO,CAAA;IAEP;;OAEG;IACH,uDAAK,CAAA;IAEL;;OAEG;IACH,6DAAQ,CAAA;AACZ,CAAC,EAfW,eAAe,KAAf,eAAe,QAe1B;AAgBD;;GAEG;AACH,MAAM,OAAO,cAAc;IAA3B;QAOI,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB;;WAEG;QACI,uBAAkB,GAAG,IAAI,UAAU,EAAmB,CAAC;QAiC9D,aAAa;QACb,aAAa;QACb,aAAa;QAEb;;WAEG;QACI,yBAAoB,GAAG,8BAA8B,CAAC,IAAI,CAAC;QAElE;;WAEG;QACI,uBAAkB,GAAG,4BAA4B,CAAC,KAAK,CAAC;QAE/D;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,iBAAY,GAAG,KAAK,CAAC;QAE5B;;WAEG;QACI,4BAAuB,GAAG,KAAK,CAAC;QAEvC;;;;WAIG;QACI,2BAAsB,GAAG,KAAK,CAAC;QAEtC;;;;WAIG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;WAEG;QACI,6BAAwB,GAAG,KAAK,CAAC;QAExC;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,sBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG;QACI,kBAAa,GAAG,KAAK,CAAC;QAE7B;;WAEG;QACI,mBAAc,GAAG,IAAI,CAAC;QAE7B;;WAEG;QACI,cAAS,GAAG,EAAE,CAAC;QAEtB;;;WAGG;QACI,kCAA6B,GAAG,KAAK,CAAC;QAE7C;;;WAGG;QACI,uBAAkB,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAElE;;;WAGG;QACa,2BAAsB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAexE;;;;;WAKG;QACa,2BAAsB,GAAG,IAAI,UAAU,EAAuD,CAAC;QAE/G;;WAEG;QACa,8BAAyB,GAAG,IAAI,UAAU,EAAe,CAAC;QAc1E;;WAEG;QACa,+BAA0B,GAAG,IAAI,UAAU,EAAY,CAAC;QAcxE;;WAEG;QACa,6BAAwB,GAAG,IAAI,UAAU,EAAU,CAAC;QAcpE;;;;WAIG;QACa,yBAAoB,GAAG,IAAI,UAAU,EAAQ,CAAC;QAgB9D;;WAEG;QACa,sBAAiB,GAAG,IAAI,UAAU,EAAO,CAAC;QAc1D;;WAEG;QACa,wBAAmB,GAAG,IAAI,UAAU,EAAQ,CAAC;QAc7D;;;WAGG;QACa,gCAA2B,GAAG,IAAI,UAAU,EAAwB,CAAC;QA0DrF;;WAEG;QACI,aAAQ,GAAG,KAAK,CAAC;QAExB;;WAEG;QACa,0BAAqB,GAAG,IAAI,UAAU,EAAgC,CAAC;QAc/E,YAAO,GAA0B,IAAI,CAAC;QACtC,WAAM,GAA8B,IAAI,CAAC;QAEzC,cAAS,GAAG,IAAI,KAAK,EAAoB,CAAC;QAIlD;;WAEG;QACI,SAAI,GAAG,MAAM,CAAC;QAErB,gBAAgB;QACT,eAAU,GAAiC;YAC9C,OAAO,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;YAC5B,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC7B,CAAC;QAgRF;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAA6B,CAAC;QAiU5E,oBAAe,GAAG,CAAC,CAAC;QACpB,oBAAe,GAAG,KAAK,CAAC;QAEhC,gBAAgB;QACT,SAAI,GAAG,IAAI,CAAC,YAAY,CAAC;QAsBxB,gCAA2B,GAAG,KAAK,CAAC;QAE5C,gBAAgB;QACT,6BAAwB,GAAG,IAAI,CAAC,gCAAgC,CAAC;QAExE,gBAAgB;QACT,2BAAsB,GAAG,IAAI,CAAC,8BAA8B,CAAC;IAaxE,CAAC;IAj+BG;;OAEG;IACH,IAAW,QAAQ,CAAC,QAA+C;QAC/D,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SAC1D;QACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnE,CAAC;IAuHD;;;OAGG;IACH,IAAW,YAAY,CAAC,QAAsC;QAC1D,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAiBD;;OAEG;IACH,IAAW,eAAe,CAAC,QAAwC;QAC/D,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;SACxE;QACD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjF,CAAC;IASD;;OAEG;IACH,IAAW,gBAAgB,CAAC,QAAsC;QAC9D,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;SAC1E;QACD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnF,CAAC;IASD;;OAEG;IACH,IAAW,cAAc,CAAC,QAAkC;QACxD,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;SACtE;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAWD;;;;OAIG;IACH,IAAW,UAAU,CAAC,QAAoB;QACtC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACvE,CAAC;IASD;;OAEG;IACH,IAAW,OAAO,CAAC,QAA+B;QAC9C,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IASD;;OAEG;IACH,IAAW,SAAS,CAAC,QAAoB;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAUD;;OAEG;IACH,IAAW,iBAAiB,CAAC,QAAmD;QAC5E,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SAC5E;QACD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAc;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE;YAChC,OAAO;SACV;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;SAChC;aAAM;YACH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,0BAA0B;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,IAAW,0BAA0B,CAAC,KAAc;QAChD,IAAI,IAAI,CAAC,2BAA2B,KAAK,KAAK,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAEzC,IAAI,IAAI,CAAC,2BAA2B,EAAE;YAClC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,+BAA+B,CAAC;YACrE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,6BAA6B,CAAC;SACpE;aAAM;YACH,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,gCAAgC,CAAC;YACtE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,8BAA8B,CAAC;SACrE;IACL,CAAC;IAcD;;OAEG;IACH,IAAW,WAAW,CAAC,QAAyD;QAC5E,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SAChE;QACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzE,CAAC;IAoBD;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QAED,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,OAAO,CAAC,KAAK,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAE9B,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAExD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QAEzC,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,QAAQ,CACX,KAAY,EACZ,SAAwB,EACxB,SAAoD,EACpD,UAAoD,EACpD,cAAwB,EACxB,OAAmE;QAEnE,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;QAEpC,MAAM,OAAO,GAAI,SAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,SAAmB,CAAC,CAAC;QAC9F,MAAM,QAAQ,GAAI,SAAkB,CAAC,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,SAAmB,CAAC,CAAC;QAEpF,IAAI,cAAc,EAAE;YAChB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAC;iBACnF;gBAED,MAAM,WAAW,GAAiB;oBAC9B,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC;oBACf,oBAAoB,EAAE,IAAI,UAAU,EAAgB;iBACvD,CAAC;gBAEF,MAAM,UAAU,GAAG;oBACf,SAAS,EAAE,CAAC,UAAkB,EAAE,UAAkB,EAAE,EAAE;wBAClD,OAAO,IAAI,OAAO,CAAkB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;4BACpD,IAAI,CAAC,SAAS,CACV,KAAK,EACL,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;gCACL,OAAO,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,CAAC;4BACjD,CAAC,EACD,IAAI,EACJ,CAAC,KAAK,EAAE,EAAE;gCACN,MAAM,CAAC,KAAK,CAAC,CAAC;4BAClB,CAAC,EACD,CAAC,UAAU,EAAE,EAAE;gCACX,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,UAAU,IAAI,UAAU,GAAG,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;4BAC/F,CAAC,CACJ,CAAC;wBACN,CAAC,CAAC,CAAC;oBACP,CAAC;oBACD,UAAU,EAAE,CAAC;iBAChB,CAAC;gBAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CACpD,CAAC,UAAU,EAAE,EAAE;oBACX,WAAW,CAAC,oBAAoB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;oBAC9D,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC1B,CAAC,EACD,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7D,CAAC;gBAEF,OAAO,WAAW,CAAC;aACtB;YAED,OAAO,IAAI,CAAC,SAAS,CACjB,KAAK,EACL,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;gBACL,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAmB,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;gBAC9D,IAAI,CAAC,kBAAkB,CACnB,IAAI,UAAU,CAAC;oBACX,SAAS,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,IAAmB,EAAE,UAAU,EAAE,UAAU,CAAC;oBAC7F,UAAU,EAAG,IAAoB,CAAC,UAAU;iBAC/C,CAAC,CACL,CAAC,IAAI,CACF,CAAC,UAAU,EAAE,EAAE;oBACX,SAAS,CAAC,UAAU,CAAC,CAAC;gBAC1B,CAAC,EACD,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7D,CAAC;YACN,CAAC,EACD,IAAI,EACJ,OAAO,CACV,CAAC;SACL;QAED,OAAO,IAAI,CAAC,SAAS,CACjB,KAAK,EACL,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;YACL,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC/C,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAc,CAAC,EAAE,CAAC,CAAC;QACzD,CAAC,EACD,cAAc,EACd,OAAO,CACV,CAAC;IACN,CAAC;IAED;;OAEG;IACI,eAAe,CAClB,WAAgB,EAChB,KAAY,EACZ,IAAS,EACT,OAAe,EACf,UAAuD,EACvD,QAAiB;QAEjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,WAAW,QAAQ,IAAI,EAAE,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QACvG,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,KAAY,EAAE,IAAS,EAAE,OAAe,EAAE,UAAuD,EAAE,QAAiB;QACjI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,WAAW,QAAQ,IAAI,EAAE,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,uBAAuB,CAAC,KAAY,EAAE,IAAS,EAAE,OAAe,EAAE,UAAuD,EAAE,QAAiB;QAC/I,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/B,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,WAAW,QAAQ,IAAI,EAAE,EAAE,CAAC,CAAC;YACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAErC,+BAA+B;YAC/B,MAAM,SAAS,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;YAE5C,0DAA0D;YAC1D,MAAM,SAAS,GAAoB,EAAE,CAAC;YACtC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC7C,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,MAAM,QAAQ,GAAuB,EAAE,CAAC;YACxC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC3C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;YACH,MAAM,OAAO,GAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAA8B,EAAE,CAAC;YAC1D,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBACzB,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;iBACrD;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7G,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;gBACpE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC5D,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;gBAC9E,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;gBAClE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC,eAAe,CAAC,CAAC;gBAC9E,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAC3D,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACzD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC5D,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;gBAC5E,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACvD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;gBAC/E,OAAO,SAAS,CAAC;YACrB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,IAAY;QAC7B,OAAO,CACH,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,cAAc,CAAC,mBAAmB,CAAC,IAAI,8EAA8E;YACtJ,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC,mBAAmB,CAAC;YACrE,IAAI,CAAC,UAAU,CAAC,uCAAuC,GAAG,cAAc,CAAC,mBAAmB,CAAC;YAC7F,IAAI,CAAC,UAAU,CAAC,gCAAgC,GAAG,cAAc,CAAC,mBAAmB,CAAC,CACzF,CAAC;IACN,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,KAAY,EAAE,IAAY;QACxC,IACI,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,cAAc,CAAC,mBAAmB,CAAC,IAAI,8EAA8E;YACjJ,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,cAAc,CAAC,mBAAmB,CAAC;YAChE,IAAI,CAAC,UAAU,CAAC,kCAAkC,GAAG,cAAc,CAAC,mBAAmB,CAAC;YACxF,IAAI,CAAC,UAAU,CAAC,2BAA2B,GAAG,cAAc,CAAC,mBAAmB,CAAC,EACnF;YACE,MAAM,WAAW,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAElD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC,kBAAkB,CAC1B,IAAI,UAAU,CAAC;gBACX,SAAS,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;gBACrF,UAAU,EAAE,WAAW,CAAC,UAAU;aACrC,CAAC,CACL,CAAC;SACL;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC;IAUD,gBAAgB;IACT,YAAY;QACf,OAAO,IAAI,cAAc,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAOD;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACnC,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACtC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,KAAsB;QACnC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACvB,OAAO;SACV;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,SAAS,CACZ,KAAY,EACZ,SAAwB,EACxB,SAA+C,EAC/C,cAAwB,EACxB,OAAwC,EACxC,QAAwC;QAExC,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAC3B,SAAS,EACT,SAAS,EACT,CAAC,KAAK,EAAE,EAAE;YACN,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,EACD,IAAI,EACJ,cAAc,EACd,OAAO,EACP,QAAQ,CACS,CAAC;QACtB,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,KAAoB,EAAE,OAAyB;QAC/D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,OAAO;SACV;QAED,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CAAC;QACnD,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QAE7B,IAAI,gBAAgB,GAAG,IAAI,CAAC;QAC5B,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE;YAClC,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC1G,OAAO;aACV;YAED,gBAAgB,GAAG,gBAAgB,IAAI,OAAO,CAAC,iBAAiB,CAAC;YACjE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC;YAC1B,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;SAC3B;QAED,IAAI,CAAC,iBAAiB,CAAC;YACnB,gBAAgB,EAAE,gBAAgB;YAClC,MAAM,EAAE,MAAM;YACd,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACtC,CAAC,CAAC;IACP,CAAC;IAEO,SAAS,CAAC,KAAY,EAAE,IAA0B,EAAE,OAAO,GAAG,EAAE,EAAE,QAAQ,GAAG,EAAE;QACnF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;QAC/C,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;YAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAyB,CAAC,CAAC;QAC1I,CAAC,CAAC,CAAC,IAAI,CACH,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;YAC7C,KAAK,CAAC,IAAI,CAAC,uBAAuB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC,CACJ,CAAC;IACN,CAAC;IAEO,UAAU,CAAC,UAA2B;QAC1C,MAAM,KAAK,GAAS,UAAU,CAAC,IAAK,CAAC,KAAK,IAAI,EAAE,CAAC;QAEjD,IAAI,CAAC,IAAI,CAAC,kBAAkB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7C,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;QAC5E,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,oBAAoB,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QAEpE,MAAM,OAAO,GAAG,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE;YACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;SACxD;QAED,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE;YAChC,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAClE,IAAI,CAAC,UAAU,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;aACnE;YAED,IAAI,cAAc,CAAC,eAAe,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;gBACxE,MAAM,IAAI,KAAK,CAAC,gCAAgC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;aACxE;SACJ;QAED,MAAM,aAAa,GAA+D;YAC9E,CAAC,EAAE,cAAc,CAAC,kBAAkB;YACpC,CAAC,EAAE,cAAc,CAAC,kBAAkB;SACvC,CAAC;QAEF,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;SAC5D;QAED,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEO,UAAU,CAAC,IAAY;QAC3B,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;QAC1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,kBAAkB,CAAC,UAAsB;QAC7C,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,CAAC;QAE/C,4DAA4D;QAC5D,OAAO,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACtC,MAAM,MAAM,GAAG;gBACX,KAAK,EAAE,UAAU;aACpB,CAAC;YAEF,MAAM,KAAK,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,KAAK,KAAK,MAAM,CAAC,KAAK,EAAE;gBACxB,MAAM,IAAI,YAAY,CAAC,oBAAoB,GAAG,KAAK,EAAE,UAAU,CAAC,8BAA8B,CAAC,CAAC;aACnG;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;YAExC,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB,OAAO,EAAE,CAAC,CAAC;aAC3C;YAED,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,KAAK,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnE,MAAM,CAAC,IAAI,CAAC,uDAAuD,MAAM,OAAO,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;aACnH;YAED,IAAI,QAAkC,CAAC;YACvC,QAAQ,OAAO,EAAE;gBACb,KAAK,CAAC,CAAC,CAAC;oBACJ,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBACzD,MAAM;iBACT;gBACD,KAAK,CAAC,CAAC,CAAC;oBACJ,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;oBACzD,MAAM;iBACT;gBACD,OAAO,CAAC,CAAC;oBACL,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,OAAO,CAAC,CAAC;iBACtD;aACJ;YAED,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;YAE7C,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB,CAAC,UAAsB,EAAE,MAAc;QAC/D,MAAM,aAAa,GAAG;YAClB,IAAI,EAAE,CAAC;SACV,CAAC;QAEF,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9C,MAAM,aAAa,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QAE9C,IAAI,aAAa,KAAK,aAAa,CAAC,IAAI,EAAE;YACtC,MAAM,IAAI,KAAK,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;SAClE;QAED,MAAM,UAAU,GAAG,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC;QAElD,MAAM,IAAI,GAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACzG,IAAI,UAAU,KAAK,CAAC,EAAE;YAClB,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC;YAC9C,IAAI,CAAC,GAAG,GAAG;gBACP,SAAS,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,EAAE,UAAU,CAAC;gBAC5G,UAAU,EAAE,UAAU;aACzB,CAAC;SACL;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAEO,oBAAoB,CAAC,UAAsB,EAAE,MAAc;QAC/D,MAAM,WAAW,GAAG;YAChB,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,UAAU;SAClB,CAAC;QAEF,8BAA8B;QAC9B,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QAC5C,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;QAC5C,IAAI,WAAW,KAAK,WAAW,CAAC,IAAI,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACrD;QAED,qCAAqC;QACrC,IAAI,UAAU,CAAC,UAAU,GAAG,WAAW,KAAK,MAAM,EAAE;YAChD,OAAO,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC/C,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;YACpF,CAAC,CAAC,CAAC;SACN;QAED,iEAAiE;QACjE,OAAO,UAAU,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACnD,MAAM,IAAI,GAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;YAEvG,MAAM,SAAS,GAAG,GAA6B,EAAE;gBAC7C,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC;gBAE5C,QAAQ,WAAW,EAAE;oBACjB,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC;wBACnB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;qBAC5C;oBACD,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC;wBAClB,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC;wBAC9C,IAAI,CAAC,GAAG,GAAG;4BACP,SAAS,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,EAAE,UAAU,CAAC;4BAC5G,UAAU,EAAE,WAAW;yBAC1B,CAAC;wBACF,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;wBAClC,MAAM;qBACT;oBACD,OAAO,CAAC,CAAC;wBACL,kCAAkC;wBAClC,UAAU,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;wBAClC,MAAM;qBACT;iBACJ;gBAED,IAAI,UAAU,CAAC,UAAU,KAAK,MAAM,EAAE;oBAClC,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAClD;gBAED,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjC,CAAC,CAAC;YAEF,OAAO,SAAS,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,OAAe;QACxC,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,OAAO,EAAE;YAC1C,OAAO;gBACH,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;aACX,CAAC;SACL;QAED,MAAM,KAAK,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,OAAO;YACH,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC5B,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,CAAmC,EAAE,CAAmC;QACnG,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;YACnB,OAAO,CAAC,CAAC;SACZ;QACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;YACnB,OAAO,CAAC,CAAC,CAAC;SACb;QACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;YACnB,OAAO,CAAC,CAAC;SACZ;QACD,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;YACnB,OAAO,CAAC,CAAC,CAAC;SACb;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IASD;;OAEG;IACI,QAAQ,CAAC,OAAe;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IACT,SAAS;QACZ,EAAE,IAAI,CAAC,eAAe,CAAC;IAC3B,CAAC;IAEO,WAAW,CAAC,OAAe;QAC/B,MAAM,MAAM,GAAG,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC7E,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC,CAAC;IACtC,CAAC;IAEO,YAAY,CAAC,OAAe,IAAS,CAAC;IAUtC,+BAA+B,CAAC,WAAmB;QACvD,KAAK,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;IAEO,gCAAgC,CAAC,WAAmB,IAAS,CAAC;IAE9D,6BAA6B,CAAC,WAAmB;QACrD,KAAK,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IAEO,8BAA8B,CAAC,WAAmB,IAAS,CAAC;;AAt9BpE,aAAa;AACb,aAAa;AACb,aAAa;AAEb;;;;;GAKG;AACW,iCAAkB,GAAG,IAAI,CAAC;AAExC;;;;GAIG;AACW,qCAAsB,GAAG,KAAK,CAAC;AA0T9B,kCAAmB,GAAG,QAAQ,CAAC,CAAC,8CAA8C;AA8lBrE,yBAAU,GAAG,kCAAkC,CAAC;AAgD5E,IAAI,WAAW,EAAE;IACb,WAAW,CAAC,cAAc,CAAC,IAAI,cAAc,EAAE,CAAC,CAAC;CACpD", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type * as GLTF2 from \"babylonjs-gltf2interface\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { Observer } from \"core/Misc/observable\";\r\nimport { Observable } from \"core/Misc/observable\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type {\r\n    ISceneLoaderPluginFactory,\r\n    ISceneLoaderPlugin,\r\n    ISceneLoaderPluginAsync,\r\n    ISceneLoaderProgressEvent,\r\n    ISceneLoaderPluginExtensions,\r\n    ISceneLoaderAsyncResult,\r\n} from \"core/Loading/sceneLoader\";\r\nimport { SceneLoader } from \"core/Loading/sceneLoader\";\r\nimport { AssetContainer } from \"core/assetContainer\";\r\nimport type { Scene, IDisposable } from \"core/scene\";\r\nimport type { WebRequest } from \"core/Misc/webRequest\";\r\nimport type { IFileRequest } from \"core/Misc/fileRequest\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport type { IDataBuffer } from \"core/Misc/dataReader\";\r\nimport { DataReader } from \"core/Misc/dataReader\";\r\nimport { GLTFValidation } from \"./glTFValidation\";\r\nimport type { LoadFileError } from \"core/Misc/fileTools\";\r\nimport { DecodeBase64UrlToBinary } from \"core/Misc/fileTools\";\r\nimport { RuntimeError, ErrorCodes } from \"core/Misc/error\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\nimport type { MorphTargetManager } from \"core/Morph/morphTargetManager\";\r\n\r\ninterface IFileRequestInfo extends IFileRequest {\r\n    _lengthComputable?: boolean;\r\n    _loaded?: number;\r\n    _total?: number;\r\n}\r\n\r\nfunction readAsync(arrayBuffer: ArrayBuffer, byteOffset: number, byteLength: number): Promise<Uint8Array> {\r\n    try {\r\n        return Promise.resolve(new Uint8Array(arrayBuffer, byteOffset, byteLength));\r\n    } catch (e) {\r\n        return Promise.reject(e);\r\n    }\r\n}\r\n\r\n/**\r\n * Mode that determines the coordinate system to use.\r\n */\r\nexport enum GLTFLoaderCoordinateSystemMode {\r\n    /**\r\n     * Automatically convert the glTF right-handed data to the appropriate system based on the current coordinate system mode of the scene.\r\n     */\r\n    AUTO,\r\n\r\n    /**\r\n     * Sets the useRightHandedSystem flag on the scene.\r\n     */\r\n    FORCE_RIGHT_HANDED,\r\n}\r\n\r\n/**\r\n * Mode that determines what animations will start.\r\n */\r\nexport enum GLTFLoaderAnimationStartMode {\r\n    /**\r\n     * No animation will start.\r\n     */\r\n    NONE,\r\n\r\n    /**\r\n     * The first animation will start.\r\n     */\r\n    FIRST,\r\n\r\n    /**\r\n     * All animations will start.\r\n     */\r\n    ALL,\r\n}\r\n\r\n/**\r\n * Interface that contains the data for the glTF asset.\r\n */\r\nexport interface IGLTFLoaderData {\r\n    /**\r\n     * The object that represents the glTF JSON.\r\n     */\r\n    json: Object;\r\n\r\n    /**\r\n     * The BIN chunk of a binary glTF.\r\n     */\r\n    bin: Nullable<IDataBuffer>;\r\n}\r\n\r\n/**\r\n * Interface for extending the loader.\r\n */\r\nexport interface IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    readonly name: string;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    enabled: boolean;\r\n\r\n    /**\r\n     * Defines the order of this extension.\r\n     * The loader sorts the extensions using these values when loading.\r\n     */\r\n    order?: number;\r\n}\r\n\r\n/**\r\n * Loader state.\r\n */\r\nexport enum GLTFLoaderState {\r\n    /**\r\n     * The asset is loading.\r\n     */\r\n    LOADING,\r\n\r\n    /**\r\n     * The asset is ready for rendering.\r\n     */\r\n    READY,\r\n\r\n    /**\r\n     * The asset is completely loaded.\r\n     */\r\n    COMPLETE,\r\n}\r\n\r\n/** @internal */\r\nexport interface IGLTFLoader extends IDisposable {\r\n    importMeshAsync: (\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        container: Nullable<AssetContainer>,\r\n        data: IGLTFLoaderData,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        fileName?: string\r\n    ) => Promise<ISceneLoaderAsyncResult>;\r\n    loadAsync: (scene: Scene, data: IGLTFLoaderData, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string) => Promise<void>;\r\n}\r\n\r\n/**\r\n * File loader for loading glTF files into a scene.\r\n */\r\nexport class GLTFFileLoader implements IDisposable, ISceneLoaderPluginAsync, ISceneLoaderPluginFactory {\r\n    /** @internal */\r\n    public static _CreateGLTF1Loader: (parent: GLTFFileLoader) => IGLTFLoader;\r\n\r\n    /** @internal */\r\n    public static _CreateGLTF2Loader: (parent: GLTFFileLoader) => IGLTFLoader;\r\n\r\n    // --------------\r\n    // Common options\r\n    // --------------\r\n\r\n    /**\r\n     * Raised when the asset has been parsed\r\n     */\r\n    public onParsedObservable = new Observable<IGLTFLoaderData>();\r\n\r\n    private _onParsedObserver: Nullable<Observer<IGLTFLoaderData>>;\r\n\r\n    /**\r\n     * Raised when the asset has been parsed\r\n     */\r\n    public set onParsed(callback: (loaderData: IGLTFLoaderData) => void) {\r\n        if (this._onParsedObserver) {\r\n            this.onParsedObservable.remove(this._onParsedObserver);\r\n        }\r\n        this._onParsedObserver = this.onParsedObservable.add(callback);\r\n    }\r\n\r\n    // ----------\r\n    // V1 options\r\n    // ----------\r\n\r\n    /**\r\n     * Set this property to false to disable incremental loading which delays the loader from calling the success callback until after loading the meshes and shaders.\r\n     * Textures always loads asynchronously. For example, the success callback can compute the bounding information of the loaded meshes when incremental loading is disabled.\r\n     * Defaults to true.\r\n     * @internal\r\n     */\r\n    public static IncrementalLoading = true;\r\n\r\n    /**\r\n     * Set this property to true in order to work with homogeneous coordinates, available with some converters and exporters.\r\n     * Defaults to false. See https://en.wikipedia.org/wiki/Homogeneous_coordinates.\r\n     * @internal\r\n     */\r\n    public static HomogeneousCoordinates = false;\r\n\r\n    // ----------\r\n    // V2 options\r\n    // ----------\r\n\r\n    /**\r\n     * The coordinate system mode. Defaults to AUTO.\r\n     */\r\n    public coordinateSystemMode = GLTFLoaderCoordinateSystemMode.AUTO;\r\n\r\n    /**\r\n     * The animation start mode. Defaults to FIRST.\r\n     */\r\n    public animationStartMode = GLTFLoaderAnimationStartMode.FIRST;\r\n\r\n    /**\r\n     * Defines if the loader should compile materials before raising the success callback. Defaults to false.\r\n     */\r\n    public compileMaterials = false;\r\n\r\n    /**\r\n     * Defines if the loader should also compile materials with clip planes. Defaults to false.\r\n     */\r\n    public useClipPlane = false;\r\n\r\n    /**\r\n     * Defines if the loader should compile shadow generators before raising the success callback. Defaults to false.\r\n     */\r\n    public compileShadowGenerators = false;\r\n\r\n    /**\r\n     * Defines if the Alpha blended materials are only applied as coverage.\r\n     * If false, (default) The luminance of each pixel will reduce its opacity to simulate the behaviour of most physical materials.\r\n     * If true, no extra effects are applied to transparent pixels.\r\n     */\r\n    public transparencyAsCoverage = false;\r\n\r\n    /**\r\n     * Defines if the loader should use range requests when load binary glTF files from HTTP.\r\n     * Enabling will disable offline support and glTF validator.\r\n     * Defaults to false.\r\n     */\r\n    public useRangeRequests = false;\r\n\r\n    /**\r\n     * Defines if the loader should create instances when multiple glTF nodes point to the same glTF mesh. Defaults to true.\r\n     */\r\n    public createInstances = true;\r\n\r\n    /**\r\n     * Defines if the loader should always compute the bounding boxes of meshes and not use the min/max values from the position accessor. Defaults to false.\r\n     */\r\n    public alwaysComputeBoundingBox = false;\r\n\r\n    /**\r\n     * If true, load all materials defined in the file, even if not used by any mesh. Defaults to false.\r\n     */\r\n    public loadAllMaterials = false;\r\n\r\n    /**\r\n     * If true, load only the materials defined in the file. Defaults to false.\r\n     */\r\n    public loadOnlyMaterials = false;\r\n\r\n    /**\r\n     * If true, do not load any materials defined in the file. Defaults to false.\r\n     */\r\n    public skipMaterials = false;\r\n\r\n    /**\r\n     * If true, load the color (gamma encoded) textures into sRGB buffers (if supported by the GPU), which will yield more accurate results when sampling the texture. Defaults to true.\r\n     */\r\n    public useSRGBBuffers = true;\r\n\r\n    /**\r\n     * When loading glTF animations, which are defined in seconds, target them to this FPS. Defaults to 60.\r\n     */\r\n    public targetFps = 60;\r\n\r\n    /**\r\n     * Defines if the loader should always compute the nearest common ancestor of the skeleton joints instead of using `skin.skeleton`. Defaults to false.\r\n     * Set this to true if loading assets with invalid `skin.skeleton` values.\r\n     */\r\n    public alwaysComputeSkeletonRootNode = false;\r\n\r\n    /**\r\n     * Function called before loading a url referenced by the asset.\r\n     * @param url\r\n     */\r\n    public preprocessUrlAsync = (url: string) => Promise.resolve(url);\r\n\r\n    /**\r\n     * Observable raised when the loader creates a mesh after parsing the glTF properties of the mesh.\r\n     * Note that the observable is raised as soon as the mesh object is created, meaning some data may not have been setup yet for this mesh (vertex data, morph targets, material, ...)\r\n     */\r\n    public readonly onMeshLoadedObservable = new Observable<AbstractMesh>();\r\n\r\n    private _onMeshLoadedObserver: Nullable<Observer<AbstractMesh>>;\r\n\r\n    /**\r\n     * Callback raised when the loader creates a mesh after parsing the glTF properties of the mesh.\r\n     * Note that the callback is called as soon as the mesh object is created, meaning some data may not have been setup yet for this mesh (vertex data, morph targets, material, ...)\r\n     */\r\n    public set onMeshLoaded(callback: (mesh: AbstractMesh) => void) {\r\n        if (this._onMeshLoadedObserver) {\r\n            this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver);\r\n        }\r\n        this._onMeshLoadedObserver = this.onMeshLoadedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Callback raised when the loader creates a skin after parsing the glTF properties of the skin node.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/glTF/glTFSkinning#ignoring-the-transform-of-the-skinned-mesh\r\n     * @param node - the transform node that corresponds to the original glTF skin node used for animations\r\n     * @param skinnedNode - the transform node that is the skinned mesh itself or the parent of the skinned meshes\r\n     */\r\n    public readonly onSkinLoadedObservable = new Observable<{ node: TransformNode; skinnedNode: TransformNode }>();\r\n\r\n    /**\r\n     * Observable raised when the loader creates a texture after parsing the glTF properties of the texture.\r\n     */\r\n    public readonly onTextureLoadedObservable = new Observable<BaseTexture>();\r\n\r\n    private _onTextureLoadedObserver: Nullable<Observer<BaseTexture>>;\r\n\r\n    /**\r\n     * Callback raised when the loader creates a texture after parsing the glTF properties of the texture.\r\n     */\r\n    public set onTextureLoaded(callback: (texture: BaseTexture) => void) {\r\n        if (this._onTextureLoadedObserver) {\r\n            this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver);\r\n        }\r\n        this._onTextureLoadedObserver = this.onTextureLoadedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised when the loader creates a material after parsing the glTF properties of the material.\r\n     */\r\n    public readonly onMaterialLoadedObservable = new Observable<Material>();\r\n\r\n    private _onMaterialLoadedObserver: Nullable<Observer<Material>>;\r\n\r\n    /**\r\n     * Callback raised when the loader creates a material after parsing the glTF properties of the material.\r\n     */\r\n    public set onMaterialLoaded(callback: (material: Material) => void) {\r\n        if (this._onMaterialLoadedObserver) {\r\n            this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver);\r\n        }\r\n        this._onMaterialLoadedObserver = this.onMaterialLoadedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised when the loader creates a camera after parsing the glTF properties of the camera.\r\n     */\r\n    public readonly onCameraLoadedObservable = new Observable<Camera>();\r\n\r\n    private _onCameraLoadedObserver: Nullable<Observer<Camera>>;\r\n\r\n    /**\r\n     * Callback raised when the loader creates a camera after parsing the glTF properties of the camera.\r\n     */\r\n    public set onCameraLoaded(callback: (camera: Camera) => void) {\r\n        if (this._onCameraLoadedObserver) {\r\n            this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver);\r\n        }\r\n        this._onCameraLoadedObserver = this.onCameraLoadedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised when the asset is completely loaded, immediately before the loader is disposed.\r\n     * For assets with LODs, raised when all of the LODs are complete.\r\n     * For assets without LODs, raised when the model is complete, immediately after the loader resolves the returned promise.\r\n     */\r\n    public readonly onCompleteObservable = new Observable<void>();\r\n\r\n    private _onCompleteObserver: Nullable<Observer<void>>;\r\n\r\n    /**\r\n     * Callback raised when the asset is completely loaded, immediately before the loader is disposed.\r\n     * For assets with LODs, raised when all of the LODs are complete.\r\n     * For assets without LODs, raised when the model is complete, immediately after the loader resolves the returned promise.\r\n     */\r\n    public set onComplete(callback: () => void) {\r\n        if (this._onCompleteObserver) {\r\n            this.onCompleteObservable.remove(this._onCompleteObserver);\r\n        }\r\n        this._onCompleteObserver = this.onCompleteObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised when an error occurs.\r\n     */\r\n    public readonly onErrorObservable = new Observable<any>();\r\n\r\n    private _onErrorObserver: Nullable<Observer<any>>;\r\n\r\n    /**\r\n     * Callback raised when an error occurs.\r\n     */\r\n    public set onError(callback: (reason: any) => void) {\r\n        if (this._onErrorObserver) {\r\n            this.onErrorObservable.remove(this._onErrorObserver);\r\n        }\r\n        this._onErrorObserver = this.onErrorObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised after the loader is disposed.\r\n     */\r\n    public readonly onDisposeObservable = new Observable<void>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<void>>;\r\n\r\n    /**\r\n     * Callback raised after the loader is disposed.\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Observable raised after a loader extension is created.\r\n     * Set additional options for a loader extension in this event.\r\n     */\r\n    public readonly onExtensionLoadedObservable = new Observable<IGLTFLoaderExtension>();\r\n\r\n    private _onExtensionLoadedObserver: Nullable<Observer<IGLTFLoaderExtension>>;\r\n\r\n    /**\r\n     * Callback raised after a loader extension is created.\r\n     */\r\n    public set onExtensionLoaded(callback: (extension: IGLTFLoaderExtension) => void) {\r\n        if (this._onExtensionLoadedObserver) {\r\n            this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver);\r\n        }\r\n        this._onExtensionLoadedObserver = this.onExtensionLoadedObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Defines if the loader logging is enabled.\r\n     */\r\n    public get loggingEnabled(): boolean {\r\n        return this._loggingEnabled;\r\n    }\r\n\r\n    public set loggingEnabled(value: boolean) {\r\n        if (this._loggingEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._loggingEnabled = value;\r\n\r\n        if (this._loggingEnabled) {\r\n            this._log = this._logEnabled;\r\n        } else {\r\n            this._log = this._logDisabled;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines if the loader should capture performance counters.\r\n     */\r\n    public get capturePerformanceCounters(): boolean {\r\n        return this._capturePerformanceCounters;\r\n    }\r\n\r\n    public set capturePerformanceCounters(value: boolean) {\r\n        if (this._capturePerformanceCounters === value) {\r\n            return;\r\n        }\r\n\r\n        this._capturePerformanceCounters = value;\r\n\r\n        if (this._capturePerformanceCounters) {\r\n            this._startPerformanceCounter = this._startPerformanceCounterEnabled;\r\n            this._endPerformanceCounter = this._endPerformanceCounterEnabled;\r\n        } else {\r\n            this._startPerformanceCounter = this._startPerformanceCounterDisabled;\r\n            this._endPerformanceCounter = this._endPerformanceCounterDisabled;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines if the loader should validate the asset.\r\n     */\r\n    public validate = false;\r\n\r\n    /**\r\n     * Observable raised after validation when validate is set to true. The event data is the result of the validation.\r\n     */\r\n    public readonly onValidatedObservable = new Observable<GLTF2.IGLTFValidationResults>();\r\n\r\n    private _onValidatedObserver: Nullable<Observer<GLTF2.IGLTFValidationResults>>;\r\n\r\n    /**\r\n     * Callback raised after a loader extension is created.\r\n     */\r\n    public set onValidated(callback: (results: GLTF2.IGLTFValidationResults) => void) {\r\n        if (this._onValidatedObserver) {\r\n            this.onValidatedObservable.remove(this._onValidatedObserver);\r\n        }\r\n        this._onValidatedObserver = this.onValidatedObservable.add(callback);\r\n    }\r\n\r\n    private _loader: Nullable<IGLTFLoader> = null;\r\n    private _state: Nullable<GLTFLoaderState> = null;\r\n    private _progressCallback?: (event: ISceneLoaderProgressEvent) => void;\r\n    private _requests = new Array<IFileRequestInfo>();\r\n\r\n    private static _MagicBase64Encoded = \"Z2xURg\"; // \"glTF\" base64 encoded (without the quotes!)\r\n\r\n    /**\r\n     * Name of the loader (\"gltf\")\r\n     */\r\n    public name = \"gltf\";\r\n\r\n    /** @internal */\r\n    public extensions: ISceneLoaderPluginExtensions = {\r\n        \".gltf\": { isBinary: false },\r\n        \".glb\": { isBinary: true },\r\n    };\r\n\r\n    /**\r\n     * Disposes the loader, releases resources during load, and cancels any outstanding requests.\r\n     */\r\n    public dispose(): void {\r\n        if (this._loader) {\r\n            this._loader.dispose();\r\n            this._loader = null;\r\n        }\r\n\r\n        for (const request of this._requests) {\r\n            request.abort();\r\n        }\r\n\r\n        this._requests.length = 0;\r\n\r\n        delete this._progressCallback;\r\n\r\n        this.preprocessUrlAsync = (url) => Promise.resolve(url);\r\n\r\n        this.onMeshLoadedObservable.clear();\r\n        this.onSkinLoadedObservable.clear();\r\n        this.onTextureLoadedObservable.clear();\r\n        this.onMaterialLoadedObservable.clear();\r\n        this.onCameraLoadedObservable.clear();\r\n        this.onCompleteObservable.clear();\r\n        this.onExtensionLoadedObservable.clear();\r\n\r\n        this.onDisposeObservable.notifyObservers(undefined);\r\n        this.onDisposeObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadFile(\r\n        scene: Scene,\r\n        fileOrUrl: File | string,\r\n        onSuccess: (data: any, responseURL?: string) => void,\r\n        onProgress?: (ev: ISceneLoaderProgressEvent) => void,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void\r\n    ): IFileRequest {\r\n        this._progressCallback = onProgress;\r\n\r\n        const rootUrl = (fileOrUrl as File).name ? \"file:\" : Tools.GetFolderPath(fileOrUrl as string);\r\n        const fileName = (fileOrUrl as File).name || Tools.GetFilename(fileOrUrl as string);\r\n\r\n        if (useArrayBuffer) {\r\n            if (this.useRangeRequests) {\r\n                if (this.validate) {\r\n                    Logger.Warn(\"glTF validation is not supported when range requests are enabled\");\r\n                }\r\n\r\n                const fileRequest: IFileRequest = {\r\n                    abort: () => {},\r\n                    onCompleteObservable: new Observable<IFileRequest>(),\r\n                };\r\n\r\n                const dataBuffer = {\r\n                    readAsync: (byteOffset: number, byteLength: number) => {\r\n                        return new Promise<ArrayBufferView>((resolve, reject) => {\r\n                            this._loadFile(\r\n                                scene,\r\n                                fileOrUrl,\r\n                                (data) => {\r\n                                    resolve(new Uint8Array(data as ArrayBuffer));\r\n                                },\r\n                                true,\r\n                                (error) => {\r\n                                    reject(error);\r\n                                },\r\n                                (webRequest) => {\r\n                                    webRequest.setRequestHeader(\"Range\", `bytes=${byteOffset}-${byteOffset + byteLength - 1}`);\r\n                                }\r\n                            );\r\n                        });\r\n                    },\r\n                    byteLength: 0,\r\n                };\r\n\r\n                this._unpackBinaryAsync(new DataReader(dataBuffer)).then(\r\n                    (loaderData) => {\r\n                        fileRequest.onCompleteObservable.notifyObservers(fileRequest);\r\n                        onSuccess(loaderData);\r\n                    },\r\n                    onError ? (error) => onError(undefined, error) : undefined\r\n                );\r\n\r\n                return fileRequest;\r\n            }\r\n\r\n            return this._loadFile(\r\n                scene,\r\n                fileOrUrl,\r\n                (data) => {\r\n                    this._validate(scene, data as ArrayBuffer, rootUrl, fileName);\r\n                    this._unpackBinaryAsync(\r\n                        new DataReader({\r\n                            readAsync: (byteOffset, byteLength) => readAsync(data as ArrayBuffer, byteOffset, byteLength),\r\n                            byteLength: (data as ArrayBuffer).byteLength,\r\n                        })\r\n                    ).then(\r\n                        (loaderData) => {\r\n                            onSuccess(loaderData);\r\n                        },\r\n                        onError ? (error) => onError(undefined, error) : undefined\r\n                    );\r\n                },\r\n                true,\r\n                onError\r\n            );\r\n        }\r\n\r\n        return this._loadFile(\r\n            scene,\r\n            fileOrUrl,\r\n            (data) => {\r\n                this._validate(scene, data, rootUrl, fileName);\r\n                onSuccess({ json: this._parseJson(data as string) });\r\n            },\r\n            useArrayBuffer,\r\n            onError\r\n        );\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public importMeshAsync(\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        data: any,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        fileName?: string\r\n    ): Promise<ISceneLoaderAsyncResult> {\r\n        return Promise.resolve().then(() => {\r\n            this.onParsedObservable.notifyObservers(data);\r\n            this.onParsedObservable.clear();\r\n\r\n            this._log(`Loading ${fileName || \"\"}`);\r\n            this._loader = this._getLoader(data);\r\n            return this._loader.importMeshAsync(meshesNames, scene, null, data, rootUrl, onProgress, fileName);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadAsync(scene: Scene, data: any, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<void> {\r\n        return Promise.resolve().then(() => {\r\n            this.onParsedObservable.notifyObservers(data);\r\n            this.onParsedObservable.clear();\r\n\r\n            this._log(`Loading ${fileName || \"\"}`);\r\n            this._loader = this._getLoader(data);\r\n            return this._loader.loadAsync(scene, data, rootUrl, onProgress, fileName);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadAssetContainerAsync(scene: Scene, data: any, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<AssetContainer> {\r\n        return Promise.resolve().then(() => {\r\n            this.onParsedObservable.notifyObservers(data);\r\n            this.onParsedObservable.clear();\r\n\r\n            this._log(`Loading ${fileName || \"\"}`);\r\n            this._loader = this._getLoader(data);\r\n\r\n            // Prepare the asset container.\r\n            const container = new AssetContainer(scene);\r\n\r\n            // Get materials/textures when loading to add to container\r\n            const materials: Array<Material> = [];\r\n            this.onMaterialLoadedObservable.add((material) => {\r\n                materials.push(material);\r\n            });\r\n            const textures: Array<BaseTexture> = [];\r\n            this.onTextureLoadedObservable.add((texture) => {\r\n                textures.push(texture);\r\n            });\r\n            const cameras: Array<Camera> = [];\r\n            this.onCameraLoadedObservable.add((camera) => {\r\n                cameras.push(camera);\r\n            });\r\n\r\n            const morphTargetManagers: Array<MorphTargetManager> = [];\r\n            this.onMeshLoadedObservable.add((mesh) => {\r\n                if (mesh.morphTargetManager) {\r\n                    morphTargetManagers.push(mesh.morphTargetManager);\r\n                }\r\n            });\r\n\r\n            return this._loader.importMeshAsync(null, scene, container, data, rootUrl, onProgress, fileName).then((result) => {\r\n                Array.prototype.push.apply(container.geometries, result.geometries);\r\n                Array.prototype.push.apply(container.meshes, result.meshes);\r\n                Array.prototype.push.apply(container.particleSystems, result.particleSystems);\r\n                Array.prototype.push.apply(container.skeletons, result.skeletons);\r\n                Array.prototype.push.apply(container.animationGroups, result.animationGroups);\r\n                Array.prototype.push.apply(container.materials, materials);\r\n                Array.prototype.push.apply(container.textures, textures);\r\n                Array.prototype.push.apply(container.lights, result.lights);\r\n                Array.prototype.push.apply(container.transformNodes, result.transformNodes);\r\n                Array.prototype.push.apply(container.cameras, cameras);\r\n                Array.prototype.push.apply(container.morphTargetManagers, morphTargetManagers);\r\n                return container;\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public canDirectLoad(data: string): boolean {\r\n        return (\r\n            (data.indexOf(\"asset\") !== -1 && data.indexOf(\"version\") !== -1) ||\r\n            data.startsWith(\"data:base64,\" + GLTFFileLoader._MagicBase64Encoded) || // this is technically incorrect, but will continue to support for backcompat.\r\n            data.startsWith(\"data:;base64,\" + GLTFFileLoader._MagicBase64Encoded) ||\r\n            data.startsWith(\"data:application/octet-stream;base64,\" + GLTFFileLoader._MagicBase64Encoded) ||\r\n            data.startsWith(\"data:model/gltf-binary;base64,\" + GLTFFileLoader._MagicBase64Encoded)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public directLoad(scene: Scene, data: string): Promise<any> {\r\n        if (\r\n            data.startsWith(\"base64,\" + GLTFFileLoader._MagicBase64Encoded) || // this is technically incorrect, but will continue to support for backcompat.\r\n            data.startsWith(\";base64,\" + GLTFFileLoader._MagicBase64Encoded) ||\r\n            data.startsWith(\"application/octet-stream;base64,\" + GLTFFileLoader._MagicBase64Encoded) ||\r\n            data.startsWith(\"model/gltf-binary;base64,\" + GLTFFileLoader._MagicBase64Encoded)\r\n        ) {\r\n            const arrayBuffer = DecodeBase64UrlToBinary(data);\r\n\r\n            this._validate(scene, arrayBuffer);\r\n            return this._unpackBinaryAsync(\r\n                new DataReader({\r\n                    readAsync: (byteOffset, byteLength) => readAsync(arrayBuffer, byteOffset, byteLength),\r\n                    byteLength: arrayBuffer.byteLength,\r\n                })\r\n            );\r\n        }\r\n\r\n        this._validate(scene, data);\r\n        return Promise.resolve({ json: this._parseJson(data) });\r\n    }\r\n\r\n    /**\r\n     * The callback that allows custom handling of the root url based on the response url.\r\n     * @param rootUrl the original root url\r\n     * @param responseURL the response url if available\r\n     * @returns the new root url\r\n     */\r\n    public rewriteRootURL?(rootUrl: string, responseURL?: string): string;\r\n\r\n    /** @internal */\r\n    public createPlugin(): ISceneLoaderPlugin | ISceneLoaderPluginAsync {\r\n        return new GLTFFileLoader();\r\n    }\r\n\r\n    /**\r\n     * The loader state or null if the loader is not active.\r\n     */\r\n    public get loaderState(): Nullable<GLTFLoaderState> {\r\n        return this._state;\r\n    }\r\n\r\n    /**\r\n     * Observable raised when the loader state changes.\r\n     */\r\n    public onLoaderStateChangedObservable = new Observable<Nullable<GLTFLoaderState>>();\r\n\r\n    /**\r\n     * Returns a promise that resolves when the asset is completely loaded.\r\n     * @returns a promise that resolves when the asset is completely loaded.\r\n     */\r\n    public whenCompleteAsync(): Promise<void> {\r\n        return new Promise((resolve, reject) => {\r\n            this.onCompleteObservable.addOnce(() => {\r\n                resolve();\r\n            });\r\n            this.onErrorObservable.addOnce((reason) => {\r\n                reject(reason);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setState(state: GLTFLoaderState): void {\r\n        if (this._state === state) {\r\n            return;\r\n        }\r\n\r\n        this._state = state;\r\n        this.onLoaderStateChangedObservable.notifyObservers(this._state);\r\n        this._log(GLTFLoaderState[this._state]);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFile(\r\n        scene: Scene,\r\n        fileOrUrl: File | string,\r\n        onSuccess: (data: string | ArrayBuffer) => void,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest) => void,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): IFileRequest {\r\n        const request = scene._loadFile(\r\n            fileOrUrl,\r\n            onSuccess,\r\n            (event) => {\r\n                this._onProgress(event, request);\r\n            },\r\n            true,\r\n            useArrayBuffer,\r\n            onError,\r\n            onOpened\r\n        ) as IFileRequestInfo;\r\n        request.onCompleteObservable.add((request) => {\r\n            this._requests.splice(this._requests.indexOf(request), 1);\r\n        });\r\n        this._requests.push(request);\r\n        return request;\r\n    }\r\n\r\n    private _onProgress(event: ProgressEvent, request: IFileRequestInfo): void {\r\n        if (!this._progressCallback) {\r\n            return;\r\n        }\r\n\r\n        request._lengthComputable = event.lengthComputable;\r\n        request._loaded = event.loaded;\r\n        request._total = event.total;\r\n\r\n        let lengthComputable = true;\r\n        let loaded = 0;\r\n        let total = 0;\r\n        for (const request of this._requests) {\r\n            if (request._lengthComputable === undefined || request._loaded === undefined || request._total === undefined) {\r\n                return;\r\n            }\r\n\r\n            lengthComputable = lengthComputable && request._lengthComputable;\r\n            loaded += request._loaded;\r\n            total += request._total;\r\n        }\r\n\r\n        this._progressCallback({\r\n            lengthComputable: lengthComputable,\r\n            loaded: loaded,\r\n            total: lengthComputable ? total : 0,\r\n        });\r\n    }\r\n\r\n    private _validate(scene: Scene, data: string | ArrayBuffer, rootUrl = \"\", fileName = \"\"): void {\r\n        if (!this.validate) {\r\n            return;\r\n        }\r\n\r\n        this._startPerformanceCounter(\"Validate JSON\");\r\n        GLTFValidation.ValidateAsync(data, rootUrl, fileName, (uri) => {\r\n            return this.preprocessUrlAsync(rootUrl + uri).then((url) => scene._loadFileAsync(url, undefined, true, true) as Promise<ArrayBuffer>);\r\n        }).then(\r\n            (result) => {\r\n                this._endPerformanceCounter(\"Validate JSON\");\r\n                this.onValidatedObservable.notifyObservers(result);\r\n                this.onValidatedObservable.clear();\r\n            },\r\n            (reason) => {\r\n                this._endPerformanceCounter(\"Validate JSON\");\r\n                Tools.Warn(`Failed to validate: ${reason.message}`);\r\n                this.onValidatedObservable.clear();\r\n            }\r\n        );\r\n    }\r\n\r\n    private _getLoader(loaderData: IGLTFLoaderData): IGLTFLoader {\r\n        const asset = (<any>loaderData.json).asset || {};\r\n\r\n        this._log(`Asset version: ${asset.version}`);\r\n        asset.minVersion && this._log(`Asset minimum version: ${asset.minVersion}`);\r\n        asset.generator && this._log(`Asset generator: ${asset.generator}`);\r\n\r\n        const version = GLTFFileLoader._parseVersion(asset.version);\r\n        if (!version) {\r\n            throw new Error(\"Invalid version: \" + asset.version);\r\n        }\r\n\r\n        if (asset.minVersion !== undefined) {\r\n            const minVersion = GLTFFileLoader._parseVersion(asset.minVersion);\r\n            if (!minVersion) {\r\n                throw new Error(\"Invalid minimum version: \" + asset.minVersion);\r\n            }\r\n\r\n            if (GLTFFileLoader._compareVersion(minVersion, { major: 2, minor: 0 }) > 0) {\r\n                throw new Error(\"Incompatible minimum version: \" + asset.minVersion);\r\n            }\r\n        }\r\n\r\n        const createLoaders: { [key: number]: (parent: GLTFFileLoader) => IGLTFLoader } = {\r\n            1: GLTFFileLoader._CreateGLTF1Loader,\r\n            2: GLTFFileLoader._CreateGLTF2Loader,\r\n        };\r\n\r\n        const createLoader = createLoaders[version.major];\r\n        if (!createLoader) {\r\n            throw new Error(\"Unsupported version: \" + asset.version);\r\n        }\r\n\r\n        return createLoader(this);\r\n    }\r\n\r\n    private _parseJson(json: string): Object {\r\n        this._startPerformanceCounter(\"Parse JSON\");\r\n        this._log(`JSON length: ${json.length}`);\r\n        const parsed = JSON.parse(json);\r\n        this._endPerformanceCounter(\"Parse JSON\");\r\n        return parsed;\r\n    }\r\n\r\n    private _unpackBinaryAsync(dataReader: DataReader): Promise<IGLTFLoaderData> {\r\n        this._startPerformanceCounter(\"Unpack Binary\");\r\n\r\n        // Read magic + version + length + json length + json format\r\n        return dataReader.loadAsync(20).then(() => {\r\n            const Binary = {\r\n                Magic: 0x46546c67,\r\n            };\r\n\r\n            const magic = dataReader.readUint32();\r\n            if (magic !== Binary.Magic) {\r\n                throw new RuntimeError(\"Unexpected magic: \" + magic, ErrorCodes.GLTFLoaderUnexpectedMagicError);\r\n            }\r\n\r\n            const version = dataReader.readUint32();\r\n\r\n            if (this.loggingEnabled) {\r\n                this._log(`Binary version: ${version}`);\r\n            }\r\n\r\n            const length = dataReader.readUint32();\r\n            if (!this.useRangeRequests && length !== dataReader.buffer.byteLength) {\r\n                Logger.Warn(`Length in header does not match actual data length: ${length} != ${dataReader.buffer.byteLength}`);\r\n            }\r\n\r\n            let unpacked: Promise<IGLTFLoaderData>;\r\n            switch (version) {\r\n                case 1: {\r\n                    unpacked = this._unpackBinaryV1Async(dataReader, length);\r\n                    break;\r\n                }\r\n                case 2: {\r\n                    unpacked = this._unpackBinaryV2Async(dataReader, length);\r\n                    break;\r\n                }\r\n                default: {\r\n                    throw new Error(\"Unsupported version: \" + version);\r\n                }\r\n            }\r\n\r\n            this._endPerformanceCounter(\"Unpack Binary\");\r\n\r\n            return unpacked;\r\n        });\r\n    }\r\n\r\n    private _unpackBinaryV1Async(dataReader: DataReader, length: number): Promise<IGLTFLoaderData> {\r\n        const ContentFormat = {\r\n            JSON: 0,\r\n        };\r\n\r\n        const contentLength = dataReader.readUint32();\r\n        const contentFormat = dataReader.readUint32();\r\n\r\n        if (contentFormat !== ContentFormat.JSON) {\r\n            throw new Error(`Unexpected content format: ${contentFormat}`);\r\n        }\r\n\r\n        const bodyLength = length - dataReader.byteOffset;\r\n\r\n        const data: IGLTFLoaderData = { json: this._parseJson(dataReader.readString(contentLength)), bin: null };\r\n        if (bodyLength !== 0) {\r\n            const startByteOffset = dataReader.byteOffset;\r\n            data.bin = {\r\n                readAsync: (byteOffset, byteLength) => dataReader.buffer.readAsync(startByteOffset + byteOffset, byteLength),\r\n                byteLength: bodyLength,\r\n            };\r\n        }\r\n\r\n        return Promise.resolve(data);\r\n    }\r\n\r\n    private _unpackBinaryV2Async(dataReader: DataReader, length: number): Promise<IGLTFLoaderData> {\r\n        const ChunkFormat = {\r\n            JSON: 0x4e4f534a,\r\n            BIN: 0x004e4942,\r\n        };\r\n\r\n        // Read the JSON chunk header.\r\n        const chunkLength = dataReader.readUint32();\r\n        const chunkFormat = dataReader.readUint32();\r\n        if (chunkFormat !== ChunkFormat.JSON) {\r\n            throw new Error(\"First chunk format is not JSON\");\r\n        }\r\n\r\n        // Bail if there are no other chunks.\r\n        if (dataReader.byteOffset + chunkLength === length) {\r\n            return dataReader.loadAsync(chunkLength).then(() => {\r\n                return { json: this._parseJson(dataReader.readString(chunkLength)), bin: null };\r\n            });\r\n        }\r\n\r\n        // Read the JSON chunk and the length and type of the next chunk.\r\n        return dataReader.loadAsync(chunkLength + 8).then(() => {\r\n            const data: IGLTFLoaderData = { json: this._parseJson(dataReader.readString(chunkLength)), bin: null };\r\n\r\n            const readAsync = (): Promise<IGLTFLoaderData> => {\r\n                const chunkLength = dataReader.readUint32();\r\n                const chunkFormat = dataReader.readUint32();\r\n\r\n                switch (chunkFormat) {\r\n                    case ChunkFormat.JSON: {\r\n                        throw new Error(\"Unexpected JSON chunk\");\r\n                    }\r\n                    case ChunkFormat.BIN: {\r\n                        const startByteOffset = dataReader.byteOffset;\r\n                        data.bin = {\r\n                            readAsync: (byteOffset, byteLength) => dataReader.buffer.readAsync(startByteOffset + byteOffset, byteLength),\r\n                            byteLength: chunkLength,\r\n                        };\r\n                        dataReader.skipBytes(chunkLength);\r\n                        break;\r\n                    }\r\n                    default: {\r\n                        // ignore unrecognized chunkFormat\r\n                        dataReader.skipBytes(chunkLength);\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                if (dataReader.byteOffset !== length) {\r\n                    return dataReader.loadAsync(8).then(readAsync);\r\n                }\r\n\r\n                return Promise.resolve(data);\r\n            };\r\n\r\n            return readAsync();\r\n        });\r\n    }\r\n\r\n    private static _parseVersion(version: string): Nullable<{ major: number; minor: number }> {\r\n        if (version === \"1.0\" || version === \"1.0.1\") {\r\n            return {\r\n                major: 1,\r\n                minor: 0,\r\n            };\r\n        }\r\n\r\n        const match = (version + \"\").match(/^(\\d+)\\.(\\d+)/);\r\n        if (!match) {\r\n            return null;\r\n        }\r\n\r\n        return {\r\n            major: parseInt(match[1]),\r\n            minor: parseInt(match[2]),\r\n        };\r\n    }\r\n\r\n    private static _compareVersion(a: { major: number; minor: number }, b: { major: number; minor: number }): number {\r\n        if (a.major > b.major) {\r\n            return 1;\r\n        }\r\n        if (a.major < b.major) {\r\n            return -1;\r\n        }\r\n        if (a.minor > b.minor) {\r\n            return 1;\r\n        }\r\n        if (a.minor < b.minor) {\r\n            return -1;\r\n        }\r\n        return 0;\r\n    }\r\n\r\n    private static readonly _logSpaces = \"                                \";\r\n    private _logIndentLevel = 0;\r\n    private _loggingEnabled = false;\r\n\r\n    /** @internal */\r\n    public _log = this._logDisabled;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _logOpen(message: string): void {\r\n        this._log(message);\r\n        this._logIndentLevel++;\r\n    }\r\n\r\n    /** @internal */\r\n    public _logClose(): void {\r\n        --this._logIndentLevel;\r\n    }\r\n\r\n    private _logEnabled(message: string): void {\r\n        const spaces = GLTFFileLoader._logSpaces.substr(0, this._logIndentLevel * 2);\r\n        Logger.Log(`${spaces}${message}`);\r\n    }\r\n\r\n    private _logDisabled(message: string): void {}\r\n\r\n    private _capturePerformanceCounters = false;\r\n\r\n    /** @internal */\r\n    public _startPerformanceCounter = this._startPerformanceCounterDisabled;\r\n\r\n    /** @internal */\r\n    public _endPerformanceCounter = this._endPerformanceCounterDisabled;\r\n\r\n    private _startPerformanceCounterEnabled(counterName: string): void {\r\n        Tools.StartPerformanceCounter(counterName);\r\n    }\r\n\r\n    private _startPerformanceCounterDisabled(counterName: string): void {}\r\n\r\n    private _endPerformanceCounterEnabled(counterName: string): void {\r\n        Tools.EndPerformanceCounter(counterName);\r\n    }\r\n\r\n    private _endPerformanceCounterDisabled(counterName: string): void {}\r\n}\r\n\r\nif (SceneLoader) {\r\n    SceneLoader.RegisterPlugin(new GLTFFileLoader());\r\n}\r\n"]}