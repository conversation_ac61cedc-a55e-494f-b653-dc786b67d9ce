{"version": 3, "file": "KHR_materials_volume.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_volume.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAK7D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,sBAAsB,CAAC;AAEpC;;;GAGG;AACH,gEAAgE;AAChE,MAAM,OAAO,oBAAoB;IAkB7B;;OAEG;IACH,YAAY,MAAkB;QApB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAO5B;;WAEG;QACI,UAAK,GAAG,GAAG,CAAC;QAQf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,qHAAqH;YACrH,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;SACxC;IACL,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;SACxC;QACA,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB;QAC9F,OAAO,UAAU,CAAC,kBAAkB,CAAsB,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACpH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,+BAA+B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAChG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC,CAAC;YAC5F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC;YACvG,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B,CAAC,OAAe,EAAE,QAAmB,EAAE,eAAyB,EAAE,SAA8B;QAC9H,IAAI,CAAC,CAAC,eAAe,YAAY,WAAW,CAAC,EAAE;YAC3C,MAAM,IAAI,KAAK,CAAC,GAAG,OAAO,+BAA+B,CAAC,CAAC;SAC9D;QAED,+EAA+E;QAC/E,mGAAmG;QACnG,IAAI,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,mBAAmB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;YACtI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;QAED,+CAA+C;QAC/C,eAAe,CAAC,UAAU,CAAC,uBAAuB,GAAG,eAAe,CAAC,iBAAiB,CAAC;QACvF,MAAM,mBAAmB,GAAG,SAAS,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;QAC3H,eAAe,CAAC,UAAU,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QACrE,IAAI,SAAS,CAAC,gBAAgB,KAAK,SAAS,IAAI,SAAS,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE;YACpF,eAAe,CAAC,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;SACpJ;QAED,eAAe,CAAC,UAAU,CAAC,gBAAgB,GAAG,GAAG,CAAC;QAClD,eAAe,CAAC,UAAU,CAAC,gBAAgB,GAAG,SAAS,CAAC,eAAe,CAAC;QACxE,eAAe,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACtD,IAAI,SAAS,CAAC,gBAAgB,EAAE;YAC3B,SAAS,CAAC,gBAAiC,CAAC,YAAY,GAAG,IAAI,CAAC;YACjE,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,OAAO,mBAAmB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,OAAoB,EAAE,EAAE;gBAC9H,eAAe,CAAC,UAAU,CAAC,gBAAgB,GAAG,OAAO,CAAC;gBACtD,eAAe,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAC3D,CAAC,CAAC,CAAC;SACN;aAAM;YACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;SAC5B;IACL,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Material } from \"core/Materials/material\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { IMaterial, ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRMaterialsVolume } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_volume\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_volume/README.md)\r\n * @since 5.0.0\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_volume implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    /**\r\n     * Defines a number that determines the order the extensions are applied.\r\n     */\r\n    public order = 173;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n        if (this.enabled) {\r\n            // We need to disable instance usage because the attenuation factor depends on the node scale of each individual mesh\r\n            this._loader._disableInstancedMesh++;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        if (this.enabled) {\r\n            this._loader._disableInstancedMesh--;\r\n        }\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadMaterialPropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material): Nullable<Promise<void>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialsVolume>(context, material, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(this._loader.loadMaterialBasePropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loader.loadMaterialPropertiesAsync(context, material, babylonMaterial));\r\n            promises.push(this._loadVolumePropertiesAsync(extensionContext, material, babylonMaterial, extension));\r\n            return Promise.all(promises).then(() => {});\r\n        });\r\n    }\r\n\r\n    private _loadVolumePropertiesAsync(context: string, material: IMaterial, babylonMaterial: Material, extension: IKHRMaterialsVolume): Promise<void> {\r\n        if (!(babylonMaterial instanceof PBRMaterial)) {\r\n            throw new Error(`${context}: Material type not supported`);\r\n        }\r\n\r\n        // If transparency isn't enabled already, this extension shouldn't do anything.\r\n        // i.e. it requires either the KHR_materials_transmission or KHR_materials_translucency extensions.\r\n        if ((!babylonMaterial.subSurface.isRefractionEnabled && !babylonMaterial.subSurface.isTranslucencyEnabled) || !extension.thicknessFactor) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        // IOR in this extension only affects interior.\r\n        babylonMaterial.subSurface.volumeIndexOfRefraction = babylonMaterial.indexOfRefraction;\r\n        const attenuationDistance = extension.attenuationDistance !== undefined ? extension.attenuationDistance : Number.MAX_VALUE;\r\n        babylonMaterial.subSurface.tintColorAtDistance = attenuationDistance;\r\n        if (extension.attenuationColor !== undefined && extension.attenuationColor.length == 3) {\r\n            babylonMaterial.subSurface.tintColor.copyFromFloats(extension.attenuationColor[0], extension.attenuationColor[1], extension.attenuationColor[2]);\r\n        }\r\n\r\n        babylonMaterial.subSurface.minimumThickness = 0.0;\r\n        babylonMaterial.subSurface.maximumThickness = extension.thicknessFactor;\r\n        babylonMaterial.subSurface.useThicknessAsDepth = true;\r\n        if (extension.thicknessTexture) {\r\n            (extension.thicknessTexture as ITextureInfo).nonColorData = true;\r\n            return this._loader.loadTextureInfoAsync(`${context}/thicknessTexture`, extension.thicknessTexture).then((texture: BaseTexture) => {\r\n                babylonMaterial.subSurface.thicknessTexture = texture;\r\n                babylonMaterial.subSurface.useGltfStyleTextures = true;\r\n            });\r\n        } else {\r\n            return Promise.resolve();\r\n        }\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_volume(loader));\r\n"]}