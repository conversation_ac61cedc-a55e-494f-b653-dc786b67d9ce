{"version": 3, "file": "shadowOnly.fragment.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/shadowOnly/shadowOnly.fragment.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,mEAAqD;AACrD,4EAA8D;AAC9D,uEAAyD;AACzD,2EAA6D;AAC7D,4EAA8D;AAC9D,gFAAkE;AAClE,0EAA4D;AAC5D,qEAAuD;AACvD,iEAAmD;AACnD,+DAAiD;AACjD,gFAAkE;AAElE,MAAM,IAAI,GAAG,uBAAuB,CAAC;AACrC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,qBAAqB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/helperFunctions\";\nimport \"core/Shaders/ShadersInclude/lightFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightUboDeclaration\";\nimport \"core/Shaders/ShadersInclude/lightsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/shadowsFragmentFunctions\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogFragmentDeclaration\";\nimport \"core/Shaders/ShadersInclude/clipPlaneFragment\";\nimport \"core/Shaders/ShadersInclude/lightFragment\";\nimport \"core/Shaders/ShadersInclude/fogFragment\";\nimport \"core/Shaders/ShadersInclude/imageProcessingCompatibility\";\n\nconst name = \"shadowOnlyPixelShader\";\nconst shader = `precision highp float;\runiform vec4 vEyePosition;\runiform float alpha;\runiform vec3 shadowColor;\rvarying vec3 vPositionW;\r#ifdef NORMAL\nvarying vec3 vNormalW;\r#endif\n#include<helperFunctions>\n#include<__decl__lightFragment>[0..maxSimultaneousLights]\n#include<lightsFragmentFunctions>\n#include<shadowsFragmentFunctions>\n#include<clipPlaneFragmentDeclaration>\n#include<fogFragmentDeclaration>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_FRAGMENT_MAIN_BEGIN\n#include<clipPlaneFragment>\nvec3 viewDirectionW=normalize(vEyePosition.xyz-vPositionW);\r#ifdef NORMAL\nvec3 normalW=normalize(vNormalW);\r#else\nvec3 normalW=vec3(1.0,1.0,1.0);\r#endif\nvec3 diffuseBase=vec3(0.,0.,0.);\rlightingInfo info;\rfloat shadow=1.;\rfloat glossiness=0.;\r#include<lightFragment>[0..1]\nvec4 color=vec4(shadowColor,(1.0-clamp(shadow,0.,1.))*alpha);\r#include<fogFragment>\ngl_FragColor=color;\r#include<imageProcessingCompatibility>\n#define CUSTOM_FRAGMENT_MAIN_END\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const shadowOnlyPixelShader = { name, shader };\n"]}