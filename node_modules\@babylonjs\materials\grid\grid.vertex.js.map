{"version": 3, "file": "grid.vertex.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/grid/grid.vertex.ts"], "names": [], "mappings": "AAAA,eAAe;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,wEAA0D;AAC1D,wEAA0D;AAC1D,mEAAqD;AACrD,6DAA+C;AAE/C,MAAM,IAAI,GAAG,kBAAkB,CAAC;AAChC,MAAM,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+Cb,CAAC;AACH,aAAa;AACb,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACxC,gBAAgB;AAChB,MAAM,CAAC,MAAM,gBAAgB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\nimport \"core/Shaders/ShadersInclude/instancesDeclaration\";\nimport \"core/Shaders/ShadersInclude/fogVertexDeclaration\";\nimport \"core/Shaders/ShadersInclude/instancesVertex\";\nimport \"core/Shaders/ShadersInclude/fogVertex\";\n\nconst name = \"gridVertexShader\";\nconst shader = `precision highp float;\rattribute vec3 position;\rattribute vec3 normal;\r#ifdef UV1\nattribute vec2 uv;\r#endif\n#ifdef UV2\nattribute vec2 uv2;\r#endif\n#include<instancesDeclaration>\nuniform mat4 projection;\runiform mat4 view;\rvarying vec3 vPosition;\rvarying vec3 vNormal;\r#include<fogVertexDeclaration>\n#ifdef OPACITY\nvarying vec2 vOpacityUV;\runiform mat4 opacityMatrix;\runiform vec2 vOpacityInfos;\r#endif\n#define CUSTOM_VERTEX_DEFINITIONS\nvoid main(void) {\r#define CUSTOM_VERTEX_MAIN_BEGIN\n#include<instancesVertex>\nvec4 worldPos=finalWorld*vec4(position,1.0);\r#include<fogVertex>\nvec4 cameraSpacePosition=view*worldPos;\rgl_Position=projection*cameraSpacePosition;\r#ifdef OPACITY\n#ifndef UV1\nvec2 uv=vec2(0.,0.);\r#endif\n#ifndef UV2\nvec2 uv2=vec2(0.,0.);\r#endif\nif (vOpacityInfos.x==0.)\r{\rvOpacityUV=vec2(opacityMatrix*vec4(uv,1.0,0.0));\r}\relse\r{\rvOpacityUV=vec2(opacityMatrix*vec4(uv2,1.0,0.0));\r}\r#endif \nvPosition=position;\rvNormal=normal;\r#define CUSTOM_VERTEX_MAIN_END\n}`;\n// Sideeffect\nShaderStore.ShadersStore[name] = shader;\n/** @internal */\nexport const gridVertexShader = { name, shader };\n"]}