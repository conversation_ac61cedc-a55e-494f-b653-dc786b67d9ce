{"version": 3, "file": "stlFileLoader.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/STL/stlFileLoader.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,KAAK,EAAE,sCAAwB;AACxC,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAEnD,OAAO,EAAE,IAAI,EAAE,uCAAyB;AAExC,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,OAAO,EAAE,cAAc,EAAE,0CAA4B;AAGrD;;;GAGG;AACH,MAAM,OAAO,aAAa;IAA1B;QACI,gBAAgB;QACT,iBAAY,GAAG,yCAAyC,CAAC;QAEhE,gBAAgB;QACT,kBAAa,GAAG,0BAA0B,CAAC;QAClD,gBAAgB;QACT,kBAAa,GAAG,mJAAmJ,CAAC;QAC3K,gBAAgB;QACT,kBAAa,GAAG,mJAAmJ,CAAC;QAE3K;;WAEG;QACI,SAAI,GAAG,KAAK,CAAC;QAEpB;;;;WAIG;QACI,eAAU,GAAiC;YAC9C,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC7B,CAAC;IAkPN,CAAC;IAzOG;;;;;;;;OAQG;IACI,UAAU,CAAC,WAAgB,EAAE,KAAY,EAAE,IAAS,EAAE,OAAe,EAAE,MAAgC;QAC1G,IAAI,OAAO,CAAC;QAEZ,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;gBACtB,cAAc;gBACd,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACrC,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC5B;gBACD,OAAO,IAAI,CAAC;aACf;YAED,aAAa;YAEb,oBAAoB;YACpB,MAAM,YAAY,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,GAAG,GAAG,EAAE,CAAC;YACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE;gBACtC,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mCAAmC;aACnF;YACD,IAAI,GAAG,GAAG,CAAC;SACd;QAED,8DAA8D;QAE9D,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YAC7C,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,eAAe,IAAI,QAAQ,IAAI,eAAe,EAAE;gBAChD,KAAK,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBACzD,OAAO,KAAK,CAAC;aAChB;YAED,oBAAoB;YACpB,IAAI,WAAW,IAAI,QAAQ,EAAE;gBACzB,IAAI,WAAW,YAAY,KAAK,EAAE;oBAC9B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAChC,SAAS;qBACZ;iBACJ;qBAAM;oBACH,IAAI,QAAQ,KAAK,WAAW,EAAE;wBAC1B,SAAS;qBACZ;iBACJ;aACJ;YAED,qCAAqC;YACrC,QAAQ,GAAG,QAAQ,IAAI,SAAS,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAC5B;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,KAAY,EAAE,IAAS,EAAE,OAAe;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACjE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,KAAY,EAAE,IAAY,EAAE,OAAe;QACjE,MAAM,SAAS,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;QAC5C,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QAC9D,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACrC,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,SAAS,CAAC,IAAS;QACvB,+CAA+C;QAC/C,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QAElC,sEAAsE;QACtE,mCAAmC;QACnC,IAAI,MAAM,CAAC,UAAU,IAAI,EAAE,EAAE;YACzB,OAAO,KAAK,CAAC;SAChB;QAED,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAE1C,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,KAAK,MAAM,CAAC,UAAU,EAAE;YACvD,OAAO,IAAI,CAAC;SACf;QAED,8CAA8C;QAC9C,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACxC,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;YAC9B,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE;gBACrC,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,YAAY,CAAC,IAAU,EAAE,IAAiB;QAC9C,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,MAAM,UAAU,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC3C,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE;YACrC,MAAM,KAAK,GAAG,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;YAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACzB,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;gBAEnC,gDAAgD;gBAChD,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACzD,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;gBAE1B,IAAI,CAAC,aAAa,CAAC,6BAA6B,EAAE;oBAC9C,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBACjE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBAEjE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;oBAC9B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;iBACjC;qBAAM;oBACH,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBACjE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;oBAEjE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;oBAC9B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;iBACjC;gBAED,MAAM,IAAI,CAAC,CAAC;aACf;YAED,IAAI,aAAa,CAAC,6BAA6B,EAAE;gBAC7C,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;gBACrC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC;gBAC7C,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC;gBAC7C,YAAY,IAAI,CAAC,CAAC;aACrB;iBAAM;gBACH,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,EAAE,CAAC;gBACvC,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,EAAE,CAAC;gBACvC,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,EAAE,CAAC;aAC1C;SACJ;QAED,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,WAAW,CAAC,IAAU,EAAE,SAAiB;QAC7C,MAAM,SAAS,GAAG,EAAE,CAAC;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,6FAA6F;QAC7F,IAAI,OAAO,CAAC;QACZ,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE;YACnD,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YACzB,qBAAqB;YACrB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,aAAa,EAAE;gBAChB,SAAS;aACZ;YACD,MAAM,MAAM,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9F,IAAI,WAAW,CAAC;YAChB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACnD,IAAI,CAAC,aAAa,CAAC,6BAA6B,EAAE;oBAC9C,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjD;qBAAM;oBACH,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEvF,2DAA2D;oBAC3D,4BAA4B;oBAC5B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACjD;aACJ;YACD,IAAI,aAAa,CAAC,6BAA6B,EAAE;gBAC7C,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;gBAC/D,YAAY,IAAI,CAAC,CAAC;aACrB;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,YAAY,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;aAChE;YACD,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;;AA/OD;;;;GAIG;AACW,2CAA6B,GAAG,KAAK,CAAC;AA6OxD,IAAI,WAAW,EAAE;IACb,WAAW,CAAC,cAAc,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC;CACnD", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { Mesh } from \"core/Meshes/mesh\";\r\nimport type { ISceneLoaderPlugin, ISceneLoaderPluginExtensions } from \"core/Loading/sceneLoader\";\r\nimport { SceneLoader } from \"core/Loading/sceneLoader\";\r\nimport { AssetContainer } from \"core/assetContainer\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * STL file type loader.\r\n * This is a babylon scene loader plugin.\r\n */\r\nexport class STLFileLoader implements ISceneLoaderPlugin {\r\n    /** @internal */\r\n    public solidPattern = /solid (\\S*)([\\S\\s]*?)endsolid[ ]*(\\S*)/g;\r\n\r\n    /** @internal */\r\n    public facetsPattern = /facet([\\s\\S]*?)endfacet/g;\r\n    /** @internal */\r\n    public normalPattern = /normal[\\s]+([-+]?[0-9]+\\.?[0-9]*([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+/g;\r\n    /** @internal */\r\n    public vertexPattern = /vertex[\\s]+([-+]?[0-9]+\\.?[0-9]*([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+/g;\r\n\r\n    /**\r\n     * Defines the name of the plugin.\r\n     */\r\n    public name = \"stl\";\r\n\r\n    /**\r\n     * Defines the extensions the stl loader is able to load.\r\n     * force data to come in as an ArrayBuffer\r\n     * we'll convert to string if it looks like it's an ASCII .stl\r\n     */\r\n    public extensions: ISceneLoaderPluginExtensions = {\r\n        \".stl\": { isBinary: true },\r\n    };\r\n\r\n    /**\r\n     * Defines if Y and Z axes are swapped or not when loading an STL file.\r\n     * The default is false to maintain backward compatibility. When set to\r\n     * true, coordinates from the STL file are used without change.\r\n     */\r\n    public static DO_NOT_ALTER_FILE_COORDINATES = false;\r\n\r\n    /**\r\n     * Import meshes into a scene.\r\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param scene The scene to import into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param meshes The meshes array to import into\r\n     * @returns True if successful or false otherwise\r\n     */\r\n    public importMesh(meshesNames: any, scene: Scene, data: any, rootUrl: string, meshes: Nullable<AbstractMesh[]>): boolean {\r\n        let matches;\r\n\r\n        if (typeof data !== \"string\") {\r\n            if (this._isBinary(data)) {\r\n                // binary .stl\r\n                const babylonMesh = new Mesh(\"stlmesh\", scene);\r\n                this._parseBinary(babylonMesh, data);\r\n                if (meshes) {\r\n                    meshes.push(babylonMesh);\r\n                }\r\n                return true;\r\n            }\r\n\r\n            // ASCII .stl\r\n\r\n            // convert to string\r\n            const array_buffer = new Uint8Array(data);\r\n            let str = \"\";\r\n            for (let i = 0; i < data.byteLength; i++) {\r\n                str += String.fromCharCode(array_buffer[i]); // implicitly assumes little-endian\r\n            }\r\n            data = str;\r\n        }\r\n\r\n        //if arrived here, data is a string, containing the STLA data.\r\n\r\n        while ((matches = this.solidPattern.exec(data))) {\r\n            let meshName = matches[1];\r\n            const meshNameFromEnd = matches[3];\r\n            if (meshNameFromEnd && meshName != meshNameFromEnd) {\r\n                Tools.Error(\"Error in STL, solid name != endsolid name\");\r\n                return false;\r\n            }\r\n\r\n            // check meshesNames\r\n            if (meshesNames && meshName) {\r\n                if (meshesNames instanceof Array) {\r\n                    if (!meshesNames.indexOf(meshName)) {\r\n                        continue;\r\n                    }\r\n                } else {\r\n                    if (meshName !== meshesNames) {\r\n                        continue;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // stl mesh name can be empty as well\r\n            meshName = meshName || \"stlmesh\";\r\n\r\n            const babylonMesh = new Mesh(meshName, scene);\r\n            this._parseASCII(babylonMesh, matches[2]);\r\n            if (meshes) {\r\n                meshes.push(babylonMesh);\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Load into a scene.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @returns true if successful or false otherwise\r\n     */\r\n    public load(scene: Scene, data: any, rootUrl: string): boolean {\r\n        const result = this.importMesh(null, scene, data, rootUrl, null);\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @returns The loaded asset container\r\n     */\r\n    public loadAssetContainer(scene: Scene, data: string, rootUrl: string): AssetContainer {\r\n        const container = new AssetContainer(scene);\r\n        scene._blockEntityCollection = true;\r\n        this.importMesh(null, scene, data, rootUrl, container.meshes);\r\n        scene._blockEntityCollection = false;\r\n        return container;\r\n    }\r\n\r\n    private _isBinary(data: any) {\r\n        // check if file size is correct for binary stl\r\n        const reader = new DataView(data);\r\n\r\n        // A Binary STL header is 80 bytes, if the data size is not great than\r\n        // that then it's not a binary STL.\r\n        if (reader.byteLength <= 80) {\r\n            return false;\r\n        }\r\n\r\n        const faceSize = (32 / 8) * 3 + (32 / 8) * 3 * 3 + 16 / 8;\r\n        const nFaces = reader.getUint32(80, true);\r\n\r\n        if (80 + 32 / 8 + nFaces * faceSize === reader.byteLength) {\r\n            return true;\r\n        }\r\n\r\n        // US-ASCII begin with 's', 'o', 'l', 'i', 'd'\r\n        const ascii = [115, 111, 108, 105, 100];\r\n        for (let off = 0; off < 5; off++) {\r\n            if (reader.getUint8(off) !== ascii[off]) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _parseBinary(mesh: Mesh, data: ArrayBuffer) {\r\n        const reader = new DataView(data);\r\n        const faces = reader.getUint32(80, true);\r\n\r\n        const dataOffset = 84;\r\n        const faceLength = 12 * 4 + 2;\r\n\r\n        let offset = 0;\r\n\r\n        const positions = new Float32Array(faces * 3 * 3);\r\n        const normals = new Float32Array(faces * 3 * 3);\r\n        const indices = new Uint32Array(faces * 3);\r\n        let indicesCount = 0;\r\n\r\n        for (let face = 0; face < faces; face++) {\r\n            const start = dataOffset + face * faceLength;\r\n            const normalX = reader.getFloat32(start, true);\r\n            const normalY = reader.getFloat32(start + 4, true);\r\n            const normalZ = reader.getFloat32(start + 8, true);\r\n\r\n            for (let i = 1; i <= 3; i++) {\r\n                const vertexstart = start + i * 12;\r\n\r\n                // ordering is intentional to match ascii import\r\n                positions[offset] = reader.getFloat32(vertexstart, true);\r\n                normals[offset] = normalX;\r\n\r\n                if (!STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\r\n                    positions[offset + 2] = reader.getFloat32(vertexstart + 4, true);\r\n                    positions[offset + 1] = reader.getFloat32(vertexstart + 8, true);\r\n\r\n                    normals[offset + 2] = normalY;\r\n                    normals[offset + 1] = normalZ;\r\n                } else {\r\n                    positions[offset + 1] = reader.getFloat32(vertexstart + 4, true);\r\n                    positions[offset + 2] = reader.getFloat32(vertexstart + 8, true);\r\n\r\n                    normals[offset + 1] = normalY;\r\n                    normals[offset + 2] = normalZ;\r\n                }\r\n\r\n                offset += 3;\r\n            }\r\n\r\n            if (STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\r\n                indices[indicesCount] = indicesCount;\r\n                indices[indicesCount + 1] = indicesCount + 2;\r\n                indices[indicesCount + 2] = indicesCount + 1;\r\n                indicesCount += 3;\r\n            } else {\r\n                indices[indicesCount] = indicesCount++;\r\n                indices[indicesCount] = indicesCount++;\r\n                indices[indicesCount] = indicesCount++;\r\n            }\r\n        }\r\n\r\n        mesh.setVerticesData(VertexBuffer.PositionKind, positions);\r\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\r\n        mesh.setIndices(indices);\r\n        mesh.computeWorldMatrix(true);\r\n    }\r\n\r\n    private _parseASCII(mesh: Mesh, solidData: string) {\r\n        const positions = [];\r\n        const normals = [];\r\n        const indices = [];\r\n        let indicesCount = 0;\r\n\r\n        //load facets, ignoring loop as the standard doesn't define it can contain more than vertices\r\n        let matches;\r\n        while ((matches = this.facetsPattern.exec(solidData))) {\r\n            const facet = matches[1];\r\n            //one normal per face\r\n            const normalMatches = this.normalPattern.exec(facet);\r\n            this.normalPattern.lastIndex = 0;\r\n            if (!normalMatches) {\r\n                continue;\r\n            }\r\n            const normal = [Number(normalMatches[1]), Number(normalMatches[5]), Number(normalMatches[3])];\r\n\r\n            let vertexMatch;\r\n            while ((vertexMatch = this.vertexPattern.exec(facet))) {\r\n                if (!STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\r\n                    positions.push(Number(vertexMatch[1]), Number(vertexMatch[5]), Number(vertexMatch[3]));\r\n                    normals.push(normal[0], normal[1], normal[2]);\r\n                } else {\r\n                    positions.push(Number(vertexMatch[1]), Number(vertexMatch[3]), Number(vertexMatch[5]));\r\n\r\n                    // Flipping the second and third component because inverted\r\n                    // when normal was declared.\r\n                    normals.push(normal[0], normal[2], normal[1]);\r\n                }\r\n            }\r\n            if (STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\r\n                indices.push(indicesCount, indicesCount + 2, indicesCount + 1);\r\n                indicesCount += 3;\r\n            } else {\r\n                indices.push(indicesCount++, indicesCount++, indicesCount++);\r\n            }\r\n            this.vertexPattern.lastIndex = 0;\r\n        }\r\n\r\n        this.facetsPattern.lastIndex = 0;\r\n        mesh.setVerticesData(VertexBuffer.PositionKind, positions);\r\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\r\n        mesh.setIndices(indices);\r\n        mesh.computeWorldMatrix(true);\r\n    }\r\n}\r\n\r\nif (SceneLoader) {\r\n    SceneLoader.RegisterPlugin(new STLFileLoader());\r\n}\r\n"]}