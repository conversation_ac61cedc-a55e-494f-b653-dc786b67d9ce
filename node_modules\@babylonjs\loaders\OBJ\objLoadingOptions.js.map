{"version": 3, "file": "objLoadingOptions.js", "sourceRoot": "", "sources": ["../../../../lts/loaders/generated/OBJ/objLoadingOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Vector2 } from \"core/Maths/math.vector\";\r\n\r\n/**\r\n * Options for loading OBJ/MTL files\r\n */\r\nexport type OBJLoadingOptions = {\r\n    /**\r\n     * Defines if UVs are optimized by default during load.\r\n     */\r\n    optimizeWithUV: boolean;\r\n    /**\r\n     * Defines custom scaling of UV coordinates of loaded meshes.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    UVScaling: Vector2;\r\n    /**\r\n     * Invert model on y-axis (does a model scaling inversion)\r\n     */\r\n    invertY: boolean;\r\n    /**\r\n     * Invert Y-Axis of referenced textures on load\r\n     */\r\n    invertTextureY: boolean;\r\n    /**\r\n     * Include in meshes the vertex colors available in some OBJ files.  This is not part of OBJ standard.\r\n     */\r\n    importVertexColors: boolean;\r\n    /**\r\n     * Compute the normals for the model, even if normals are present in the file.\r\n     */\r\n    computeNormals: boolean;\r\n    /**\r\n     * Optimize the normals for the model. Lighting can be uneven if you use OptimizeWithUV = true because new vertices can be created for the same location if they pertain to different faces.\r\n     * Using OptimizehNormals = true will help smoothing the lighting by averaging the normals of those vertices.\r\n     */\r\n    optimizeNormals: boolean;\r\n    /**\r\n     * Skip loading the materials even if defined in the OBJ file (materials are ignored).\r\n     */\r\n    skipMaterials: boolean;\r\n    /**\r\n     * When a material fails to load OBJ loader will silently fail and onSuccess() callback will be triggered.\r\n     */\r\n    materialLoadingFailsSilently: boolean;\r\n};\r\n"]}