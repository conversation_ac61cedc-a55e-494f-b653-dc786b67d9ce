{"version": 3, "file": "KHR_materials_variants.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_materials_variants.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAGtD,OAAO,EAAE,IAAI,EAAE,uCAAyB;AAMxC,MAAM,IAAI,GAAG,wBAAwB,CAAC;AAYtC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,sBAAsB;IAe/B;;OAEG;IACH,YAAY,MAAkB;QAjB9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAexB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,oBAAoB,CAAC,QAAc;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,EAAE;YACpB,OAAO,EAAE,CAAC;SACb;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,QAAc;QACtC,OAAO,sBAAsB,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,aAAa,CAAC,QAAc,EAAE,WAA8B;QACtE,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,+DAA+D,IAAI,YAAY,CAAC,CAAC;SACpG;QAED,MAAM,MAAM,GAAG,CAAC,WAAmB,EAAQ,EAAE;YACzC,MAAM,OAAO,GAAG,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,OAAO,EAAE;gBACT,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;oBACzB,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;iBACxC;aACJ;QACL,CAAC,CAAC;QAEF,IAAI,WAAW,YAAY,KAAK,EAAE;YAC9B,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,CAAC;aAChB;SACJ;aAAM;YACH,MAAM,CAAC,WAAW,CAAC,CAAC;SACvB;QAED,iBAAiB,CAAC,YAAY,GAAG,WAAW,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,aAAa,CAAC,QAAc,EAAE,WAA8B;QAC/D,OAAO,sBAAsB,CAAC,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACvE,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,KAAK,CAAC,QAAc;QAC9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,sDAAsD,IAAI,YAAY,CAAC,CAAC;SAC3F;QAED,KAAK,MAAM,KAAK,IAAI,iBAAiB,CAAC,QAAQ,EAAE;YAC5C,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;SACxC;QAED,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,QAAc;QACvB,OAAO,sBAAsB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,sBAAsB,CAAC,QAAc;QAC/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,EAAE;YACpB,MAAM,IAAI,KAAK,CAAC,8EAA8E,IAAI,YAAY,CAAC,CAAC;SACnH;QAED,OAAO,iBAAiB,CAAC,YAAY,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAc;QACxC,OAAO,sBAAsB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;IACnE,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,QAAwB;;QACzD,OAAO,CAAA,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,iBAAiB,0CAAE,IAAI,0CAAG,IAAI,CAAC,KAAI,IAAI,CAAC;IAC7D,CAAC;IAED,gBAAgB;IACT,SAAS;QACZ,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;QAChD,IAAI,UAAU,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACrC,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAkC,CAAC;YACzE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC;SACvC;IACL,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC1B,OAAe,EACf,IAAY,EACZ,IAAW,EACX,IAAW,EACX,SAAyB,EACzB,MAA2C;QAE3C,OAAO,UAAU,CAAC,kBAAkB,CAA6C,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YAC5I,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAgB,CAAC;YAC3C,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,WAAW,EAAE,EAAE;gBACvF,MAAM,CAAC,WAAW,CAAC,CAAC;gBAEpB,IAAI,WAAW,YAAY,IAAI,EAAE;oBAC7B,MAAM,eAAe,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;oBAEzE,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;oBAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrF,MAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;oBACnD,MAAM,iBAAiB,GAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;oBAE9H,+BAA+B;oBAC/B,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAEvF,wEAAwE;oBACxE,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,YAAY,EAAE;wBACjF,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;wBACjD,MAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,aAAa,YAAY,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;wBACvI,QAAQ,CAAC,IAAI,CACT,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,eAAe,OAAO,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC,eAAe,EAAE,EAAE;4BAC3H,KAAK,IAAI,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,mBAAmB,EAAE;gCACpG,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;gCAC3D,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,eAAe,IAAI,aAAa,YAAY,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gCAC5G,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gCAC1F,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;oCAC1C,IAAI,EAAE,WAAW;oCACjB,QAAQ,EAAE,eAAe;iCAC5B,CAAC,CAAC;gCAEH,kDAAkD;gCAClD,WAAW,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAY,EAAE,EAAE;oCAChD,MAAM,OAAO,GAAG,MAAc,CAAC;oCAC/B,IAAI,QAAQ,GAAiC,IAAI,CAAC;oCAClD,IAAI,OAAO,GAAmB,OAAO,CAAC;oCAEtC,0BAA0B;oCAC1B,GAAG;wCACC,OAAO,GAAG,OAAQ,CAAC,MAAM,CAAC;wCAC1B,IAAI,CAAC,OAAO,EAAE;4CACV,OAAO;yCACV;wCACD,QAAQ,GAAG,sBAAsB,CAAC,qBAAqB,CAAC,OAAe,CAAC,CAAC;qCAC5E,QAAQ,QAAQ,KAAK,IAAI,EAAE;oCAE5B,2DAA2D;oCAC3D,IAAI,IAAI,IAAI,QAAQ,KAAK,sBAAsB,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;wCACzE,qBAAqB;wCACrB,OAAO,CAAC,iBAAiB,GAAG,EAAE,CAAC;wCAC/B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,EAAE;4CACtC,OAAO,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;yCAChE;wCAED,yBAAyB;wCACzB,OAAO,CAAC,iBAAiB,CAAC,IAAI,GAAG,EAAE,CAAC;wCACpC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE;4CAC3C,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yCAC1E;wCAED,4CAA4C;wCAC5C,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;wCAC1F,KAAK,MAAM,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;4CACtC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;gDAC/C,IAAI,EAAE,QAAQ,CAAC,IAAI;gDACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;6CAC9B,CAAC,CAAC;yCACN;wCACD,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE;4CACjC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;gDAC9D,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gDACxD,KAAK,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oDAC/C,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wDACpD,IAAI,EAAE,YAAY,CAAC,IAAI;wDACvB,QAAQ,EAAE,YAAY,CAAC,QAAQ;qDAClC,CAAC,CAAC;iDACN;6CACJ;yCACJ;wCAED,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qCACnD;oCAED,WAAW;oCACX,KAAK,MAAM,MAAM,IAAI,QAAS,CAAC,QAAQ,EAAE;wCACrC,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;4CAC7B,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;yCACzB;qCACJ;oCACD,KAAK,MAAM,MAAM,IAAI,QAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wCACnD,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE;4CAC7B,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;yCACzB;qCACJ;gCACL,CAAC,CAAC,CAAC;6BACN;wBACL,CAAC,CAAC,CACL,CAAC;qBACL;iBACJ;YACL,CAAC,CAAC,CACL,CAAC;YACF,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE,EAAE;gBAChD,OAAO,WAAW,CAAC;YACvB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader, ArrayItem } from \"../glTFLoader\";\r\n\r\nimport type { Material } from \"core/Materials/material\";\r\nimport { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Node } from \"core/node\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { INode, IMeshPrimitive, IMesh } from \"../glTFLoaderInterfaces\";\r\nimport type { IKHRMaterialVariants_Mapping, IKHRMaterialVariants_Variant, IKHRMaterialVariants_Variants } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_materials_variants\";\r\n\r\ninterface IVariantsMap {\r\n    [key: string]: Array<{ mesh: AbstractMesh; material: Nullable<Material> }>;\r\n}\r\n\r\ninterface IExtensionMetadata {\r\n    lastSelected: Nullable<string | Array<string>>;\r\n    original: Array<{ mesh: AbstractMesh; material: Nullable<Material> }>;\r\n    variants: IVariantsMap;\r\n}\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_materials_variants/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_materials_variants implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    private _variants?: Array<IKHRMaterialVariants_Variant>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of available variant names for this asset.\r\n     * @param rootMesh The glTF root mesh\r\n     * @returns the list of all the variant names for this model\r\n     */\r\n    public static GetAvailableVariants(rootMesh: Mesh): string[] {\r\n        const extensionMetadata = this._GetExtensionMetadata(rootMesh);\r\n        if (!extensionMetadata) {\r\n            return [];\r\n        }\r\n\r\n        return Object.keys(extensionMetadata.variants);\r\n    }\r\n\r\n    /**\r\n     * Gets the list of available variant names for this asset.\r\n     * @param rootMesh The glTF root mesh\r\n     * @returns the list of all the variant names for this model\r\n     */\r\n    public getAvailableVariants(rootMesh: Mesh): string[] {\r\n        return KHR_materials_variants.GetAvailableVariants(rootMesh);\r\n    }\r\n\r\n    /**\r\n     * Select a variant given a variant name or a list of variant names.\r\n     * @param rootMesh The glTF root mesh\r\n     * @param variantName The variant name(s) to select.\r\n     */\r\n    public static SelectVariant(rootMesh: Mesh, variantName: string | string[]): void {\r\n        const extensionMetadata = this._GetExtensionMetadata(rootMesh);\r\n        if (!extensionMetadata) {\r\n            throw new Error(`Cannot select variant on a glTF mesh that does not have the ${NAME} extension`);\r\n        }\r\n\r\n        const select = (variantName: string): void => {\r\n            const entries = extensionMetadata.variants[variantName];\r\n            if (entries) {\r\n                for (const entry of entries) {\r\n                    entry.mesh.material = entry.material;\r\n                }\r\n            }\r\n        };\r\n\r\n        if (variantName instanceof Array) {\r\n            for (const name of variantName) {\r\n                select(name);\r\n            }\r\n        } else {\r\n            select(variantName);\r\n        }\r\n\r\n        extensionMetadata.lastSelected = variantName;\r\n    }\r\n\r\n    /**\r\n     * Select a variant given a variant name or a list of variant names.\r\n     * @param rootMesh The glTF root mesh\r\n     * @param variantName The variant name(s) to select.\r\n     */\r\n    public selectVariant(rootMesh: Mesh, variantName: string | string[]): void {\r\n        return KHR_materials_variants.SelectVariant(rootMesh, variantName);\r\n    }\r\n\r\n    /**\r\n     * Reset back to the original before selecting a variant.\r\n     * @param rootMesh The glTF root mesh\r\n     */\r\n    public static Reset(rootMesh: Mesh): void {\r\n        const extensionMetadata = this._GetExtensionMetadata(rootMesh);\r\n        if (!extensionMetadata) {\r\n            throw new Error(`Cannot reset on a glTF mesh that does not have the ${NAME} extension`);\r\n        }\r\n\r\n        for (const entry of extensionMetadata.original) {\r\n            entry.mesh.material = entry.material;\r\n        }\r\n\r\n        extensionMetadata.lastSelected = null;\r\n    }\r\n\r\n    /**\r\n     * Reset back to the original before selecting a variant.\r\n     * @param rootMesh The glTF root mesh\r\n     */\r\n    public reset(rootMesh: Mesh): void {\r\n        return KHR_materials_variants.Reset(rootMesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the last selected variant name(s) or null if original.\r\n     * @param rootMesh The glTF root mesh\r\n     * @returns The selected variant name(s).\r\n     */\r\n    public static GetLastSelectedVariant(rootMesh: Mesh): Nullable<string | string[]> {\r\n        const extensionMetadata = this._GetExtensionMetadata(rootMesh);\r\n        if (!extensionMetadata) {\r\n            throw new Error(`Cannot get the last selected variant on a glTF mesh that does not have the ${NAME} extension`);\r\n        }\r\n\r\n        return extensionMetadata.lastSelected;\r\n    }\r\n\r\n    /**\r\n     * Gets the last selected variant name(s) or null if original.\r\n     * @param rootMesh The glTF root mesh\r\n     * @returns The selected variant name(s).\r\n     */\r\n    public getLastSelectedVariant(rootMesh: Mesh): Nullable<string | string[]> {\r\n        return KHR_materials_variants.GetLastSelectedVariant(rootMesh);\r\n    }\r\n\r\n    private static _GetExtensionMetadata(rootMesh: Nullable<Mesh>): Nullable<IExtensionMetadata> {\r\n        return rootMesh?._internalMetadata?.gltf?.[NAME] || null;\r\n    }\r\n\r\n    /** @internal */\r\n    public onLoading(): void {\r\n        const extensions = this._loader.gltf.extensions;\r\n        if (extensions && extensions[this.name]) {\r\n            const extension = extensions[this.name] as IKHRMaterialVariants_Variants;\r\n            this._variants = extension.variants;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadMeshPrimitiveAsync(\r\n        context: string,\r\n        name: string,\r\n        node: INode,\r\n        mesh: IMesh,\r\n        primitive: IMeshPrimitive,\r\n        assign: (babylonMesh: AbstractMesh) => void\r\n    ): Nullable<Promise<AbstractMesh>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRMaterialVariants_Mapping, AbstractMesh>(context, primitive, this.name, (extensionContext, extension) => {\r\n            const promises = new Array<Promise<any>>();\r\n            promises.push(\r\n                this._loader._loadMeshPrimitiveAsync(context, name, node, mesh, primitive, (babylonMesh) => {\r\n                    assign(babylonMesh);\r\n\r\n                    if (babylonMesh instanceof Mesh) {\r\n                        const babylonDrawMode = GLTFLoader._GetDrawMode(context, primitive.mode);\r\n\r\n                        const root = this._loader.rootBabylonMesh;\r\n                        const metadata = root ? (root._internalMetadata = root._internalMetadata || {}) : {};\r\n                        const gltf = (metadata.gltf = metadata.gltf || {});\r\n                        const extensionMetadata: IExtensionMetadata = (gltf[NAME] = gltf[NAME] || { lastSelected: null, original: [], variants: {} });\r\n\r\n                        // Store the original material.\r\n                        extensionMetadata.original.push({ mesh: babylonMesh, material: babylonMesh.material });\r\n\r\n                        // For each mapping, look at the variants and make a new entry for them.\r\n                        for (let mappingIndex = 0; mappingIndex < extension.mappings.length; ++mappingIndex) {\r\n                            const mapping = extension.mappings[mappingIndex];\r\n                            const material = ArrayItem.Get(`${extensionContext}/mappings/${mappingIndex}/material`, this._loader.gltf.materials, mapping.material);\r\n                            promises.push(\r\n                                this._loader._loadMaterialAsync(`#/materials/${mapping.material}`, material, babylonMesh, babylonDrawMode, (babylonMaterial) => {\r\n                                    for (let mappingVariantIndex = 0; mappingVariantIndex < mapping.variants.length; ++mappingVariantIndex) {\r\n                                        const variantIndex = mapping.variants[mappingVariantIndex];\r\n                                        const variant = ArrayItem.Get(`/extensions/${NAME}/variants/${variantIndex}`, this._variants, variantIndex);\r\n                                        extensionMetadata.variants[variant.name] = extensionMetadata.variants[variant.name] || [];\r\n                                        extensionMetadata.variants[variant.name].push({\r\n                                            mesh: babylonMesh,\r\n                                            material: babylonMaterial,\r\n                                        });\r\n\r\n                                        // Replace the target when original mesh is cloned\r\n                                        babylonMesh.onClonedObservable.add((newOne: Node) => {\r\n                                            const newMesh = newOne as Mesh;\r\n                                            let metadata: Nullable<IExtensionMetadata> = null;\r\n                                            let newRoot: Nullable<Node> = newMesh;\r\n\r\n                                            // Find root to get medata\r\n                                            do {\r\n                                                newRoot = newRoot!.parent;\r\n                                                if (!newRoot) {\r\n                                                    return;\r\n                                                }\r\n                                                metadata = KHR_materials_variants._GetExtensionMetadata(newRoot as Mesh);\r\n                                            } while (metadata === null);\r\n\r\n                                            // Need to clone the metadata on the root (first time only)\r\n                                            if (root && metadata === KHR_materials_variants._GetExtensionMetadata(root)) {\r\n                                                // Copy main metadata\r\n                                                newRoot._internalMetadata = {};\r\n                                                for (const key in root._internalMetadata) {\r\n                                                    newRoot._internalMetadata[key] = root._internalMetadata[key];\r\n                                                }\r\n\r\n                                                // Copy the gltf metadata\r\n                                                newRoot._internalMetadata.gltf = [];\r\n                                                for (const key in root._internalMetadata.gltf) {\r\n                                                    newRoot._internalMetadata.gltf[key] = root._internalMetadata.gltf[key];\r\n                                                }\r\n\r\n                                                // Duplicate the extension specific metadata\r\n                                                newRoot._internalMetadata.gltf[NAME] = { lastSelected: null, original: [], variants: {} };\r\n                                                for (const original of metadata.original) {\r\n                                                    newRoot._internalMetadata.gltf[NAME].original.push({\r\n                                                        mesh: original.mesh,\r\n                                                        material: original.material,\r\n                                                    });\r\n                                                }\r\n                                                for (const key in metadata.variants) {\r\n                                                    if (Object.prototype.hasOwnProperty.call(metadata.variants, key)) {\r\n                                                        newRoot._internalMetadata.gltf[NAME].variants[key] = [];\r\n                                                        for (const variantEntry of metadata.variants[key]) {\r\n                                                            newRoot._internalMetadata.gltf[NAME].variants[key].push({\r\n                                                                mesh: variantEntry.mesh,\r\n                                                                material: variantEntry.material,\r\n                                                            });\r\n                                                        }\r\n                                                    }\r\n                                                }\r\n\r\n                                                metadata = newRoot._internalMetadata.gltf[NAME];\r\n                                            }\r\n\r\n                                            // Relocate\r\n                                            for (const target of metadata!.original) {\r\n                                                if (target.mesh === babylonMesh) {\r\n                                                    target.mesh = newMesh;\r\n                                                }\r\n                                            }\r\n                                            for (const target of metadata!.variants[variant.name]) {\r\n                                                if (target.mesh === babylonMesh) {\r\n                                                    target.mesh = newMesh;\r\n                                                }\r\n                                            }\r\n                                        });\r\n                                    }\r\n                                })\r\n                            );\r\n                        }\r\n                    }\r\n                })\r\n            );\r\n            return Promise.all(promises).then(([babylonMesh]) => {\r\n                return babylonMesh;\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_materials_variants(loader));\r\n"]}