import { NodeMaterialBlock } from "../nodeMaterialBlock.js";
import { NodeMaterialBlockConnectionPointTypes } from "../Enums/nodeMaterialBlockConnectionPointTypes.js";
import { NodeMaterialBlockTargets } from "../Enums/nodeMaterialBlockTargets.js";
import { RegisterClass } from "../../../Misc/typeStore.js";
/**
 * Block used to expand a Vector3/4 into 4 outputs (one for each component)
 */
export class VectorSplitterBlock extends NodeMaterialBlock {
    /**
     * Create a new VectorSplitterBlock
     * @param name defines the block name
     */
    constructor(name) {
        super(name, NodeMaterialBlockTargets.Neutral);
        this.registerInput("xyzw", NodeMaterialBlockConnectionPointTypes.Vector4, true);
        this.registerInput("xyz ", NodeMaterialBlockConnectionPointTypes.Vector3, true);
        this.registerInput("xy ", NodeMaterialBlockConnectionPointTypes.Vector2, true);
        this.registerOutput("xyz", NodeMaterialBlockConnectionPointTypes.Vector3);
        this.registerOutput("xy", NodeMaterialBlockConnectionPointTypes.Vector2);
        this.registerOutput("zw", NodeMaterialBlockConnectionPointTypes.Vector2);
        this.registerOutput("x", NodeMaterialBlockConnectionPointTypes.Float);
        this.registerOutput("y", NodeMaterialBlockConnectionPointTypes.Float);
        this.registerOutput("z", NodeMaterialBlockConnectionPointTypes.Float);
        this.registerOutput("w", NodeMaterialBlockConnectionPointTypes.Float);
        this.inputsAreExclusive = true;
    }
    /**
     * Gets the current class name
     * @returns the class name
     */
    getClassName() {
        return "VectorSplitterBlock";
    }
    /**
     * Gets the xyzw component (input)
     */
    get xyzw() {
        return this._inputs[0];
    }
    /**
     * Gets the xyz component (input)
     */
    get xyzIn() {
        return this._inputs[1];
    }
    /**
     * Gets the xy component (input)
     */
    get xyIn() {
        return this._inputs[2];
    }
    /**
     * Gets the xyz component (output)
     */
    get xyzOut() {
        return this._outputs[0];
    }
    /**
     * Gets the xy component (output)
     */
    get xyOut() {
        return this._outputs[1];
    }
    /**
     * Gets the zw component (output)
     */
    get zw() {
        return this._outputs[2];
    }
    /**
     * Gets the x component (output)
     */
    get x() {
        return this._outputs[3];
    }
    /**
     * Gets the y component (output)
     */
    get y() {
        return this._outputs[4];
    }
    /**
     * Gets the z component (output)
     */
    get z() {
        return this._outputs[5];
    }
    /**
     * Gets the w component (output)
     */
    get w() {
        return this._outputs[6];
    }
    _inputRename(name) {
        switch (name) {
            case "xy ":
                return "xyIn";
            case "xyz ":
                return "xyzIn";
            default:
                return name;
        }
    }
    _outputRename(name) {
        switch (name) {
            case "xy":
                return "xyOut";
            case "xyz":
                return "xyzOut";
            default:
                return name;
        }
    }
    _buildBlock(state) {
        super._buildBlock(state);
        const input = this.xyzw.isConnected ? this.xyzw : this.xyzIn.isConnected ? this.xyzIn : this.xyIn;
        const xyzOutput = this._outputs[0];
        const xyOutput = this._outputs[1];
        const zwOutput = this._outputs[2];
        const xOutput = this._outputs[3];
        const yOutput = this._outputs[4];
        const zOutput = this._outputs[5];
        const wOutput = this._outputs[6];
        if (xyzOutput.hasEndpoints) {
            if (input === this.xyIn) {
                state.compilationString += this._declareOutput(xyzOutput, state) + ` = vec3(${input.associatedVariableName}, 0.0);\r\n`;
            }
            else {
                state.compilationString += this._declareOutput(xyzOutput, state) + ` = ${input.associatedVariableName}.xyz;\r\n`;
            }
        }
        if (zwOutput.hasEndpoints && this.xyzw.isConnected) {
            state.compilationString += this._declareOutput(zwOutput, state) + ` = ${this.xyzw.associatedVariableName}.zw;\r\n`;
        }
        if (xyOutput.hasEndpoints) {
            state.compilationString += this._declareOutput(xyOutput, state) + ` = ${input.associatedVariableName}.xy;\r\n`;
        }
        if (xOutput.hasEndpoints) {
            state.compilationString += this._declareOutput(xOutput, state) + ` = ${input.associatedVariableName}.x;\r\n`;
        }
        if (yOutput.hasEndpoints) {
            state.compilationString += this._declareOutput(yOutput, state) + ` = ${input.associatedVariableName}.y;\r\n`;
        }
        if (zOutput.hasEndpoints) {
            state.compilationString += this._declareOutput(zOutput, state) + ` = ${input.associatedVariableName}.z;\r\n`;
        }
        if (wOutput.hasEndpoints) {
            state.compilationString += this._declareOutput(wOutput, state) + ` = ${input.associatedVariableName}.w;\r\n`;
        }
        return this;
    }
}
RegisterClass("BABYLON.VectorSplitterBlock", VectorSplitterBlock);
//# sourceMappingURL=vectorSplitterBlock.js.map