{"version": 3, "file": "KHR_texture_transform.js", "sourceRoot": "", "sources": ["../../../../../../lts/loaders/generated/glTF/2.0/Extensions/KHR_texture_transform.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAI1D,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,MAAM,IAAI,GAAG,uBAAuB,CAAC;AAErC;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,qBAAqB;IAa9B;;OAEG;IACH,YAAY,MAAkB;QAf9B;;WAEG;QACa,SAAI,GAAG,IAAI,CAAC;QAaxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,OAAO;QACT,IAAI,CAAC,OAAe,GAAG,IAAI,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAe,EAAE,WAAyB,EAAE,MAA6C;QACjH,OAAO,UAAU,CAAC,kBAAkB,CAAoC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,gBAAgB,EAAE,SAAS,EAAE,EAAE;YACrI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,cAAc,EAAE,EAAE;gBAC9E,IAAI,CAAC,CAAC,cAAc,YAAY,OAAO,CAAC,EAAE;oBACtC,MAAM,IAAI,KAAK,CAAC,GAAG,gBAAgB,8BAA8B,CAAC,CAAC;iBACtE;gBAED,IAAI,SAAS,CAAC,MAAM,EAAE;oBAClB,cAAc,CAAC,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC7C,cAAc,CAAC,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;iBAChD;gBAED,mCAAmC;gBACnC,cAAc,CAAC,eAAe,GAAG,CAAC,CAAC;gBACnC,cAAc,CAAC,eAAe,GAAG,CAAC,CAAC;gBAEnC,IAAI,SAAS,CAAC,QAAQ,EAAE;oBACpB,cAAc,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;iBAC7C;gBAED,IAAI,SAAS,CAAC,KAAK,EAAE;oBACjB,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC3C,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAC9C;gBAED,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,EAAE;oBACjC,cAAc,CAAC,gBAAgB,GAAG,SAAS,CAAC,QAAQ,CAAC;iBACxD;gBAED,MAAM,CAAC,cAAc,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;CACJ;AAED,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\n\r\nimport type { ITextureInfo } from \"../glTFLoaderInterfaces\";\r\nimport type { IGLTFLoaderExtension } from \"../glTFLoaderExtension\";\r\nimport { GLTFLoader } from \"../glTFLoader\";\r\nimport type { IKHRTextureTransform } from \"babylonjs-gltf2interface\";\r\n\r\nconst NAME = \"KHR_texture_transform\";\r\n\r\n/**\r\n * [Specification](https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Khronos/KHR_texture_transform/README.md)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class KHR_texture_transform implements IGLTFLoaderExtension {\r\n    /**\r\n     * The name of this extension.\r\n     */\r\n    public readonly name = NAME;\r\n\r\n    /**\r\n     * Defines whether this extension is enabled.\r\n     */\r\n    public enabled: boolean;\r\n\r\n    private _loader: GLTFLoader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    constructor(loader: GLTFLoader) {\r\n        this._loader = loader;\r\n        this.enabled = this._loader.isExtensionUsed(NAME);\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose() {\r\n        (this._loader as any) = null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public loadTextureInfoAsync(context: string, textureInfo: ITextureInfo, assign: (babylonTexture: BaseTexture) => void): Nullable<Promise<BaseTexture>> {\r\n        return GLTFLoader.LoadExtensionAsync<IKHRTextureTransform, BaseTexture>(context, textureInfo, this.name, (extensionContext, extension) => {\r\n            return this._loader.loadTextureInfoAsync(context, textureInfo, (babylonTexture) => {\r\n                if (!(babylonTexture instanceof Texture)) {\r\n                    throw new Error(`${extensionContext}: Texture type not supported`);\r\n                }\r\n\r\n                if (extension.offset) {\r\n                    babylonTexture.uOffset = extension.offset[0];\r\n                    babylonTexture.vOffset = extension.offset[1];\r\n                }\r\n\r\n                // Always rotate around the origin.\r\n                babylonTexture.uRotationCenter = 0;\r\n                babylonTexture.vRotationCenter = 0;\r\n\r\n                if (extension.rotation) {\r\n                    babylonTexture.wAng = -extension.rotation;\r\n                }\r\n\r\n                if (extension.scale) {\r\n                    babylonTexture.uScale = extension.scale[0];\r\n                    babylonTexture.vScale = extension.scale[1];\r\n                }\r\n\r\n                if (extension.texCoord != undefined) {\r\n                    babylonTexture.coordinatesIndex = extension.texCoord;\r\n                }\r\n\r\n                assign(babylonTexture);\r\n            });\r\n        });\r\n    }\r\n}\r\n\r\nGLTFLoader.RegisterExtension(NAME, (loader) => new KHR_texture_transform(loader));\r\n"]}