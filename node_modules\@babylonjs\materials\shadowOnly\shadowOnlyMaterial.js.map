{"version": 3, "file": "shadowOnlyMaterial.js", "sourceRoot": "", "sources": ["../../../../lts/materials/generated/shadowOnly/shadowOnlyMaterial.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,mBAAmB,EAAE,2CAA6B;AAE3D,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAI/C,OAAO,EAAE,eAAe,EAAE,qDAAuC;AACjE,OAAO,EAAE,cAAc,EAAE,oDAAsC;AAC/D,OAAO,EAAE,YAAY,EAAE,kDAAoC;AAC3D,OAAO,EAAE,YAAY,EAAE,0CAA4B;AAInD,OAAO,EAAE,KAAK,EAAE,iCAAmB;AACnC,OAAO,EAAE,aAAa,EAAE,0CAA4B;AAEpD,OAAO,uBAAuB,CAAC;AAC/B,OAAO,qBAAqB,CAAC;AAC7B,OAAO,EAAE,eAAe,EAAE,qDAAuC;AAEjE,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,6DAA+C;AAE7F,MAAM,yBAA0B,SAAQ,eAAe;IAgBnD;QACI,KAAK,EAAE,CAAC;QAhBL,cAAS,GAAG,KAAK,CAAC;QAClB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,eAAU,GAAG,KAAK,CAAC;QACnB,cAAS,GAAG,KAAK,CAAC;QAClB,QAAG,GAAG,KAAK,CAAC;QACZ,WAAM,GAAG,KAAK,CAAC;QACf,yBAAoB,GAAG,CAAC,CAAC;QACzB,iBAAY,GAAG,CAAC,CAAC;QACjB,cAAS,GAAG,KAAK,CAAC;QAClB,+BAA0B,GAAG,KAAK,CAAC;QACnC,wBAAmB,GAAG,KAAK,CAAC;QAI/B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CACJ;AAED,MAAM,OAAO,kBAAmB,SAAQ,YAAY;IAIhD,YAAY,IAAY,EAAE,KAAa;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAHf,uBAAkB,GAAG,IAAI,CAAC;QAM3B,gBAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAFpC,CAAC;IAIM,iBAAiB;QACpB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAEM,gBAAgB;QACnB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,mBAAmB;QACtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAmB;QACtC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAEO,2BAA2B,CAAC,IAAkB;QAClD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;YACnC,IAAI,KAAK,CAAC,aAAa,EAAE;gBACrB,OAAO,KAAqB,CAAC;aAChC;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACH,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB;;QACjF,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,4BAA4B,KAAK,YAAY,EAAE;gBACtH,OAAO,IAAI,CAAC;aACf;SACJ;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAC1B,OAAO,CAAC,eAAe,GAAG,IAAI,yBAAyB,EAAE,CAAC;SAC7D;QAED,MAAM,OAAO,GAA8B,OAAO,CAAC,eAAe,CAAC;QACnE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE;YAClC,OAAO,IAAI,CAAC;SACf;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,qDAAqD;QACrD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnC,IAAI,KAAK,CAAC,aAAa,EAAE;oBACrB,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE;wBAC7B,MAAM,CAAC,cAAc;qBACxB;oBAED,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAEnE,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;wBACtB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;wBAC3C,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;qBACrD;oBACD,MAAM;iBACT;aACJ;SACJ;QAED,cAAc,CAAC,iCAAiC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE5G,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;QAExI,OAAO,CAAC,YAAY,GAAG,cAAc,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAE9F,MAAM,eAAe,GAAG,MAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,0CAAE,kBAAkB,EAAE,CAAC;QAErF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,IAAI,eAAe,IAAK,eAAuB,CAAC,YAAY,IAAK,eAAuB,CAAC,YAAY,EAAE,KAAK,yBAAyB,EAAE;YACnI,MAAM,GAAG,GAAG,eAA0C,CAAC;YAEvD,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,CAAC,mBAAmB,CAAC;SACtD;QAED,UAAU;QACV,cAAc,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEvE,qBAAqB;QACrB,IAAI,OAAO,CAAC,OAAO,EAAE;YACjB,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAE5B,YAAY;YACZ,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,GAAG,EAAE;gBACb,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;aACnC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAEhE,IAAI,OAAO,CAAC,oBAAoB,GAAG,CAAC,EAAE;gBAClC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;aAC7C;YAED,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;YAE3F,YAAY;YACZ,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACzC;YAED,cAAc,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5E,cAAc,CAAC,6BAA6B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE/D,MAAM,UAAU,GAAG,YAAY,CAAC;YAChC,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC7J,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAU,CAAC;YAErC,MAAM,cAAc,GAAG,IAAI,KAAK,EAAU,CAAC;YAE3C,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC/B,cAAc,CAAC,8BAA8B,CAAyB;gBAClE,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,CAAC;aAC3B,CAAC,CAAC;YAEH,OAAO,CAAC,SAAS,CACb,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CAC1B,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe,EAAE,EAAE,qBAAqB,EAAE,CAAC,EAAE;aAChD,EACD,MAAM,CACT,EACD,OAAO,EACP,IAAI,CAAC,gBAAgB,CACxB,CAAC;SACL;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;YAC9C,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,OAAO,CAAC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC1C,OAAO,CAAC,MAAM,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAA8B,OAAO,CAAC,eAAe,CAAC;QACnE,IAAI,CAAC,OAAO,EAAE;YACV,OAAO;SACV;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE;YACT,OAAO;SACV;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAE3E,QAAQ;QACR,cAAc,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;YACjC,aAAa;YACb,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEnC,aAAa;YACb,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5D;YAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE9D,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;SACjC;QAED,SAAS;QACT,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;YAEvE,MAAM,KAAK,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;YAErD,IAAI,KAAK,EAAE;gBACP,+HAA+H;gBAC/H,gIAAgI;gBAChI,sHAAsH;gBACtH,kIAAkI;gBAClI,0CAA0C;gBAC1C,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;aACxB;SACJ;QAED,OAAO;QACP,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE;YACtG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;SAC/D;QAED,MAAM;QACN,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAElE,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9C,CAAC;IAEM,KAAK,CAAC,IAAY;QACrB,OAAO,mBAAmB,CAAC,KAAK,CAAqB,GAAG,EAAE,CAAC,IAAI,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACpH,CAAC;IAEM,SAAS;QACZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,4BAA4B,CAAC;QAC9D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEM,YAAY;QACf,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED,UAAU;IACH,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,OAAO,mBAAmB,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,kBAAkB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC/G,CAAC;CACJ;AAED,aAAa,CAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"core/types\";\r\nimport { SerializationHelper } from \"core/Misc/decorators\";\r\nimport type { Matrix } from \"core/Maths/math.vector\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { IShadowLight } from \"core/Lights/shadowLight\";\r\nimport type { IEffectCreationOptions } from \"core/Materials/effect\";\r\nimport { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { MaterialHelper } from \"core/Materials/materialHelper\";\r\nimport { PushMaterial } from \"core/Materials/pushMaterial\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\n\r\nimport \"./shadowOnly.fragment\";\r\nimport \"./shadowOnly.vertex\";\r\nimport { EffectFallbacks } from \"core/Materials/effectFallbacks\";\r\nimport type { CascadedShadowGenerator } from \"core/Lights/Shadows/cascadedShadowGenerator\";\r\nimport { addClipPlaneUniforms, bindClipPlane } from \"core/Materials/clipPlaneMaterialHelper\";\r\n\r\nclass ShadowOnlyMaterialDefines extends MaterialDefines {\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public NORMAL = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public INSTANCES = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n\r\n    constructor() {\r\n        super();\r\n        this.rebuild();\r\n    }\r\n}\r\n\r\nexport class ShadowOnlyMaterial extends PushMaterial {\r\n    private _activeLight: IShadowLight;\r\n    private _needAlphaBlending = true;\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n    }\r\n\r\n    public shadowColor = Color3.Black();\r\n\r\n    public needAlphaBlending(): boolean {\r\n        return this._needAlphaBlending;\r\n    }\r\n\r\n    public needAlphaTesting(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return null;\r\n    }\r\n\r\n    public get activeLight(): IShadowLight {\r\n        return this._activeLight;\r\n    }\r\n\r\n    public set activeLight(light: IShadowLight) {\r\n        this._activeLight = light;\r\n    }\r\n\r\n    private _getFirstShadowLightForMesh(mesh: AbstractMesh): Nullable<IShadowLight> {\r\n        for (const light of mesh.lightSources) {\r\n            if (light.shadowEnabled) {\r\n                return light as IShadowLight;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    // Methods\r\n    public isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (this.isFrozen) {\r\n            if (subMesh.effect && subMesh.effect._wasPreviouslyReady && subMesh.effect._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            subMesh.materialDefines = new ShadowOnlyMaterialDefines();\r\n        }\r\n\r\n        const defines = <ShadowOnlyMaterialDefines>subMesh.materialDefines;\r\n        const scene = this.getScene();\r\n\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Ensure that active light is the first shadow light\r\n        if (this._activeLight) {\r\n            for (const light of mesh.lightSources) {\r\n                if (light.shadowEnabled) {\r\n                    if (this._activeLight === light) {\r\n                        break; // We are good\r\n                    }\r\n\r\n                    const lightPosition = mesh.lightSources.indexOf(this._activeLight);\r\n\r\n                    if (lightPosition !== -1) {\r\n                        mesh.lightSources.splice(lightPosition, 1);\r\n                        mesh.lightSources.splice(0, 0, this._activeLight);\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        MaterialHelper.PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false);\r\n\r\n        MaterialHelper.PrepareDefinesForMisc(mesh, scene, false, this.pointsCloud, this.fogEnabled, this._shouldTurnAlphaTestOn(mesh), defines);\r\n\r\n        defines._needNormals = MaterialHelper.PrepareDefinesForLights(scene, mesh, defines, false, 1);\r\n\r\n        const shadowGenerator = this._getFirstShadowLightForMesh(mesh)?.getShadowGenerator();\r\n\r\n        this._needAlphaBlending = true;\r\n\r\n        if (shadowGenerator && (shadowGenerator as any).getClassName && (shadowGenerator as any).getClassName() === \"CascadedShadowGenerator\") {\r\n            const csg = shadowGenerator as CascadedShadowGenerator;\r\n\r\n            this._needAlphaBlending = !csg.autoCalcDepthBounds;\r\n        }\r\n\r\n        // Attribs\r\n        MaterialHelper.PrepareDefinesForAttributes(mesh, defines, false, true);\r\n\r\n        // Get correct effect\r\n        if (defines.isDirty) {\r\n            defines.markAsProcessed();\r\n\r\n            scene.resetCachedMaterial();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            MaterialHelper.HandleFallbacksForShadows(defines, fallbacks, 1);\r\n\r\n            if (defines.NUM_BONE_INFLUENCERS > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n\r\n            defines.IMAGEPROCESSINGPOSTPROCESS = scene.imageProcessingConfiguration.applyByPostProcess;\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            MaterialHelper.PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            MaterialHelper.PrepareAttributesForInstances(attribs, defines);\r\n\r\n            const shaderName = \"shadowOnly\";\r\n            const join = defines.toString();\r\n            const uniforms = [\"world\", \"view\", \"viewProjection\", \"vEyePosition\", \"vLightsType\", \"vFogInfos\", \"vFogColor\", \"pointSize\", \"alpha\", \"shadowColor\", \"mBones\"];\r\n            const samplers = new Array<string>();\r\n\r\n            const uniformBuffers = new Array<string>();\r\n\r\n            addClipPlaneUniforms(uniforms);\r\n            MaterialHelper.PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: 1,\r\n            });\r\n\r\n            subMesh.setEffect(\r\n                scene.getEngine().createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: this.onCompiled,\r\n                        onError: this.onError,\r\n                        indexParameters: { maxSimultaneousLights: 1 },\r\n                    },\r\n                    engine\r\n                ),\r\n                defines,\r\n                this._materialContext\r\n            );\r\n        }\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        subMesh.effect._wasPreviouslyReady = true;\r\n        subMesh.effect._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        return true;\r\n    }\r\n\r\n    public bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <ShadowOnlyMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices\r\n        this.bindOnlyWorldMatrix(world);\r\n        this._activeEffect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n\r\n        // Bones\r\n        MaterialHelper.BindBonesParameters(mesh, this._activeEffect);\r\n\r\n        if (this._mustRebind(scene, effect)) {\r\n            // Clip plane\r\n            bindClipPlane(effect, this, scene);\r\n\r\n            // Point size\r\n            if (this.pointsCloud) {\r\n                this._activeEffect.setFloat(\"pointSize\", this.pointSize);\r\n            }\r\n\r\n            this._activeEffect.setFloat(\"alpha\", this.alpha);\r\n            this._activeEffect.setColor3(\"shadowColor\", this.shadowColor);\r\n\r\n            scene.bindEyePosition(effect);\r\n        }\r\n\r\n        // Lights\r\n        if (scene.lightsEnabled) {\r\n            MaterialHelper.BindLights(scene, mesh, this._activeEffect, defines, 1);\r\n\r\n            const light = this._getFirstShadowLightForMesh(mesh);\r\n\r\n            if (light) {\r\n                // Make sure the uniforms for this light will be rebound for other materials using this light when rendering the current frame.\r\n                // Indeed, there is an optimization in Light that binds the light uniforms only once per frame for a given light (if using ubo).\r\n                // Doing this way assumes that all uses of this light are the same, meaning all parameters passed to Light._bindLlight\r\n                // are the same, notably useSpecular. However, isReadyForSubMesh (see above) is passing false for this parameter, which may not be\r\n                // the value the other materials may pass.\r\n                light._renderId = -1;\r\n            }\r\n        }\r\n\r\n        // View\r\n        if ((scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) || defines[\"SHADOWCSM0\"]) {\r\n            this._activeEffect.setMatrix(\"view\", scene.getViewMatrix());\r\n        }\r\n\r\n        // Fog\r\n        MaterialHelper.BindFogParameters(scene, mesh, this._activeEffect);\r\n\r\n        this._afterBind(mesh, this._activeEffect);\r\n    }\r\n\r\n    public clone(name: string): ShadowOnlyMaterial {\r\n        return SerializationHelper.Clone<ShadowOnlyMaterial>(() => new ShadowOnlyMaterial(name, this.getScene()), this);\r\n    }\r\n\r\n    public serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.ShadowOnlyMaterial\";\r\n        return serializationObject;\r\n    }\r\n\r\n    public getClassName(): string {\r\n        return \"ShadowOnlyMaterial\";\r\n    }\r\n\r\n    // Statics\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): ShadowOnlyMaterial {\r\n        return SerializationHelper.Parse(() => new ShadowOnlyMaterial(source.name, scene), source, scene, rootUrl);\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ShadowOnlyMaterial\", ShadowOnlyMaterial);\r\n"]}